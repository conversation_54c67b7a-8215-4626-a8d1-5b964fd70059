workflow:
  rules:
    - if: $CI_COMMIT_BRANCH != "main" && $CI_COMMIT_BRANCH != "dev" && $CI_COMMIT_BRANCH != "uat" && $CI_PIPELINE_SOURCE != "merge_request_event"
      when: never
    - when: always

default:
  image: node:22.11.0
  before_script:
    # Install pnpm package manager
    - curl -fsSL https://fnm.vercel.app/install | bash
    - export FNM_PATH="/home/<USER>/.local/share/fnm"
    - export PATH="$FNM_PATH:$PATH"
    - eval "$(fnm env)"
    - fnm install 20
    - fnm use 20
    - node -v
    - npm -v
    - corepack prepare pnpm@latest --activate
    # Delete old or conflicting token configurations
    - pnpm config delete //git.ults.in/:_authToken
    - pnpm config delete //git.ults.in/api/v4/projects/1551/packages/npm/:_authToken
    - pnpm config set store-dir .pnpm-store
    # Set up authentication for a private npm registry
    - pnpm config set //git.ults.in/:_authToken=${GITLAB_AUTH_TOKEN}
    # Print versions
    - node -v
    - pnpm -v
    # Install project dependancies
    - pnpm install

stages:
  - validate
  - build
  - test
  - deploy

# Validate merge request title
validate_mr_title:
  stage: validate
  tags:
    - kla-dev
  before_script: []
  script:
    - echo "Validating MR title..."
    - |
      title="$CI_MERGE_REQUEST_TITLE"
      regex="^QNA-.*: .*"
      if [[ "$title" =~ $regex ]]; then
        echo "MR Title is valid"
      else
        echo "MR Title is invalid"
        exit 1
      fi
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "uat" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main")'

check_version_bump:
  stage: validate
  tags:
    - kla-dev
  script:
    - echo "Checking version bump and changelog..."
    - git fetch origin $CI_MERGE_REQUEST_TARGET_BRANCH_NAME:$CI_MERGE_REQUEST_TARGET_BRANCH_NAME
    - BASE_VERSION=$(git show origin/$CI_MERGE_REQUEST_TARGET_BRANCH_NAME:package.json | jq -r '.version')
    - PR_VERSION=$(jq -r '.version' package.json)
    - pnpm install semver
    - echo "Base version - $BASE_VERSION"
    - echo "PR version - $PR_VERSION"
    - |
      node -e "const semver = require('semver'); \
        if (!semver.gt('$PR_VERSION', '$BASE_VERSION')) { \
          console.error('Version in PR is not bumped up from the base branch.'); \
          process.exit(1); \
        }"
    - |
      if ! grep -q "## \[$PR_VERSION\]" CHANGELOG.md; then
        echo "No entry found for version $PR_VERSION in CHANGELOG.md"
        exit 1
      fi
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "uat" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main")'

# Validate code formatting
check_code_formatting:
  stage: validate
  tags:
    - kla-dev
  script:
    - echo "Checking code formatting..."
    - pnpm install
    - pnpm run lint
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "uat" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main")'

# coverage_analysis:
#   stage: test
#   tags:
#     - kla-dev
#   script:
#     - |
#       echo "Running tests and generating coverage report..."
#       pnpm run coverage  # Run tests and generate coverage report

#       echo "Extracting total coverage..."
#       COVERAGE_STATEMENTS=$(node -e "console.log(require('./coverage/coverage-summary.json').total.statements.pct)")
#       COVERAGE_BRANCHES=$(node -e "console.log(require('./coverage/coverage-summary.json').total.branches.pct)")
#       COVERAGE_FUNCTIONS=$(node -e "console.log(require('./coverage/coverage-summary.json').total.functions.pct)")
#       COVERAGE_LINES=$(node -e "console.log(require('./coverage/coverage-summary.json').total.lines.pct)")
#       COVERAGE="Statements   : $COVERAGE_STATEMENTS%\nBranches     : $COVERAGE_BRANCHES%\nFunctions    : $COVERAGE_FUNCTIONS%\nLines        : $COVERAGE_LINES%"
#       echo "Coverage: $COVERAGE"

#       # Prepare JSON payload with proper formatting for the comment
#       COMMENT_PAYLOAD=$(cat <<EOF
#       {
#         "body": "### Merge Request Coverage Report\\n\\n**Merge Request Title:** $CI_MERGE_REQUEST_TITLE\\n**Merge Request ID:** $CI_MERGE_REQUEST_IID\\n\\n---\\n\\n**Coverage:\n** $COVERAGE\\n\\n---\\n\\nPlease review the coverage details to ensure quality compliance."
#       }
#       EOF
#       )

#       # Define API URL for posting the comment
#       API_URL="https://git.ults.in/api/v4/projects/${CI_PROJECT_ID}/merge_requests/${CI_MERGE_REQUEST_IID}/notes"

#       echo "Posting coverage comment to merge request..."
#       RESPONSE=$(curl -s -o response.txt -w "%{http_code}" -X POST -H "Content-Type: application/json" -H "PRIVATE-TOKEN: $GITLAB_TOKEN" \
#         -d "$COMMENT_PAYLOAD" \
#         "$API_URL")

#       # Check if the response code indicates success
#       if [ "$RESPONSE" -ne 201 ]; then
#           echo "Failed to post comment. HTTP status: $RESPONSE"
#           echo "Response body:"
#           cat response.txt
#           exit 1
#       fi

#       # Check if the total coverage is below the threshold
#       echo "Checking if total coverage meets the threshold..."
#       if (( $(echo "$COVERAGE_STATEMENTS < 35" | bc -l) )); then
#           echo "Coverage is less than 35%!"
#           exit 1
#       fi
#   rules:
#     - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'

# Run Tests and SonarQube analysis
unit_test_and_sonarqube_analysis:
  stage: test
  tags:
    - kla-dev
  script:
    - echo "Running unit tests..."
    - pnpm run test
    - echo "Running SonarQube analysis..."
    - pnpm add sonarqube-scanner
    - npx sonarqube-scanner -Dsonar.projectKey=question-section-app -Dsonar.sources=. -Dsonar.host.url=${SONAR_HOST_URL} -Dsonar.login=${SONAR_TOKEN} -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info -Dbuild.version=$(jq -r '.version' package.json) || (echo "SonarQube analysis failed." && exit 1)
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"' # Run when MR is raised
    - if: '$CI_COMMIT_BRANCH == "main" && $CI_PIPELINE_SOURCE == "push"' # Run when MR is merged to main
    - if: '$CI_COMMIT_BRANCH == "dev" && $CI_PIPELINE_SOURCE == "push"' # Run when MR is merged to dev
    - if: '$CI_COMMIT_BRANCH == "uat" && $CI_PIPELINE_SOURCE == "push"' # Run when MR is merged to uat
      when: always

# Build the project
build:
  stage: build
  tags:
    - kla-dev
  script:
    - pnpm run build
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

# Deploy to dev environment
deploy_dev:
  stage: deploy
  tags:
    - kla-dev
  script:
    - wget https://dl.min.io/client/mc/release/linux-amd64/mc
    - chmod +x mc
    - pnpm run build
    - ./mc alias set myminio http://************:31303 kla_admin "$MINIO_PASS"
    - |
      find ./dist -type f | while read -r file; do
        case "${file##*.}" in
          css)
            ./mc cp --attr Content-Type=text/css "$file" "myminio/kla-dev/question-section/${file#./dist/}"
            ;;
          js)
            ./mc cp --attr Content-Type=application/javascript "$file" "myminio/kla-dev/question-section/${file#./dist/}"
            ;;
          html)
            ./mc cp --attr Content-Type=text/html "$file" "myminio/kla-dev/question-section/${file#./dist/}"
            ;;
          *)
            ./mc cp --attr Content-Type=application/octet-stream "$file" "myminio/kla-dev/question-section/${file#./dist/}"
            ;; # Explicitly set Content-Type for all other files
        esac
      done


  rules:
    - if: '$CI_COMMIT_BRANCH == "dev" && $CI_PIPELINE_SOURCE == "push"'

# Deploy to staging/UAT environment
deploy_staging:
  stage: deploy
  tags:
    - kla-dev
  script:
    - wget https://dl.min.io/client/mc/release/linux-amd64/mc
    - chmod +x mc
    - export DEPLOY_ENV=staging
    - pnpm run build
    - ./mc alias set myminio http://************:31303 kla_admin "$MINIO_PASS"
    - |
      find ./dist -type f | while read -r file; do
        case "${file##*.}" in
          css)
            ./mc cp --attr Content-Type=text/css "$file" "myminio/kla-staging/question-section/${file#./dist/}"
            ;;
          js)
            ./mc cp --attr Content-Type=application/javascript "$file" "myminio/kla-staging/question-section/${file#./dist/}"
            ;;
          html)
            ./mc cp --attr Content-Type=text/html "$file" "myminio/kla-staging/question-section/${file#./dist/}"
            ;;
          *)
            ./mc cp --attr Content-Type=application/octet-stream "$file" "myminio/kla-staging/question-section/${file#./dist/}"
            ;; # Explicitly set Content-Type for all other files
        esac
      done
  rules:
    - if: '$CI_COMMIT_BRANCH == "uat" && $CI_PIPELINE_SOURCE == "push"'

# Deploy to production environment
deploy_production:
  stage: deploy
  tags:
    - kla-dev
  script:
    - wget https://dl.min.io/client/mc/release/linux-amd64/mc
    - chmod +x mc
    - pnpm run build
    - ./mc alias set myminio http://************:31303 kla_admin "$MINIO_PASS"
    - |
      find ./dist -type f | while read -r file; do
        case "${file##*.}" in
          css)
            ./mc cp --attr Content-Type=text/css "$file" "myminio/kla-prod/question-section/${file#./dist/}"
            ;;
          js)
            ./mc cp --attr Content-Type=application/javascript "$file" "myminio/kla-prod/question-section/${file#./dist/}"
            ;;
          html)
            ./mc cp --attr Content-Type=text/html "$file" "myminio/kla-prod/question-section/${file#./dist/}"
            ;;
          *)
            ./mc cp --attr Content-Type=application/octet-stream "$file" "myminio/kla-prod/question-section/${file#./dist/}"
            ;;
        esac
      done
  rules:
    - if: '$CI_COMMIT_BRANCH == "main" && $CI_PIPELINE_SOURCE == "push"'
