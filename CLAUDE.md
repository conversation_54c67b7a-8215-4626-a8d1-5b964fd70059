# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the Question Section App - a React application for managing legislative assembly questions, built with Vite and part of the KLA Legislative Assembly System.

## Development Commands

```bash
# Development
pnpm run dev              # Dev server (development environment - ************)
pnpm run dev:local        # Dev server (localhost:8080)
pnpm run dev:prod         # Dev server (production environment)

# Building
pnpm run build            # Production build
pnpm run build:dev        # Development build
pnpm run build:local      # Local build
pnpm run build:prod       # Production build

# Testing
pnpm test                 # Run tests in watch mode
pnpm test:run            # Run tests once
pnpm test:ui             # Run tests with UI
pnpm coverage            # Generate coverage report

# Code Quality
pnpm run lint            # ESLint
pnpm run format          # Prettier formatting
pnpm run preview         # Preview production build
```

## Architecture

### Frontend Stack
- **React 18.3.1** with Vite 5.4.13 build tool
- **Redux Toolkit** for state management and API calls (RTK Query)
- **React Router v6** for routing
- **React Hook Form + Zod** for form handling and validation
- **TailwindCSS** with KLA theme configuration
- **@kla-v2/ui-components** - Shared UI component library
- **i18n** support for English and Malayalam

### API Integration
The application integrates with two main services:
- **Question Service API** (`VITE_API_BASE_URL`)
- **MDM Service API** (`VITE_MDM_SERVICE_BASE_URL`)

API services are implemented using RTK Query pattern in `src/services/`. The app supports a hybrid approach where some endpoints use real APIs while others can still use MSW mocks (controlled by `VITE_ENABLE_MOCKS`).

### Testing Infrastructure
- **Vitest** for unit testing
- **React Testing Library** for component testing
- **MSW (Mock Service Worker)** for API mocking during development/testing
- Test files follow the pattern `*.test.jsx` or `*.test.js`

### Key Application Modules
- **Notice Management**: Various notice types (questions, short notice, half-hour discussion)
- **Allotment System**: Day allocation for questions
- **Consent Management**: Parliamentary consent workflows
- **Balloting**: Question selection and ordering
- **Minister Groups**: Designation and grouping management
- **Reporting**: Answer status reports, delay statements
- **Schedule Management**: Activity scheduling

### Form Schema Pattern
Forms use a consistent pattern with React Hook Form + Zod:
- Form fields defined in `form-fields.js`
- Validation schemas in `form-schema.js` or `form-schema.jsx`
- Components use these via React Hook Form's `useForm` hook

### Environment Configuration
- `.env` - Base configuration
- `.env.development` - Development environment (************)
- `.env.localhost` - Local development (localhost:8080)
- `.env.production` - Production environment
- `.env.local` - Local overrides (gitignored)

Key environment variables:
- `VITE_API_BASE_URL` - Question service API base URL
- `VITE_MDM_SERVICE_BASE_URL` - Master Data Management service URL
- `VITE_ENABLE_MOCKS` - Enable/disable MSW mocks