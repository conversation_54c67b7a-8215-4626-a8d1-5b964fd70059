import { http, HttpResponse } from "msw";

const BASE_URL = import.meta.env.BASE_URL;

const userProfileData = {
  content: [
    {
      userid: "550e8400-e29b-41d4-a716-446655440000",
      username: "pinara<PERSON>",
      email: "<EMAIL>",
      memberDisplayName: "<PERSON><PERSON><PERSON><PERSON>",
      memberDisplayNameInLocal: "പിണറായി വിജയൻ",
      displayName: "<PERSON><PERSON><PERSON><PERSON>",
      displayNameInLocal: "പിണറായി വിജയൻ",
      roles: "MINISTER",
      profileURL: "https://randomuser.me/api/portraits/men/75.jpg",
      userType: "members",

      ministerDesignation: "Chief Minister of Kerala",
      ministerDesignationInLocal: "കേരള മുഖ്യമന്ത്രി",
      ministerId: 123,
      memberId: 1234,
      portfolioId: 456,
      portfolio: "Home Affairs, Vigilance",
      portfolioInLocal: "ആഭ്യന്തരം, വിജിലൻസ്",

      constituencyId: 500,
      constituencyName: "Kozhikode North",
      constituencyNameInLocal: "കോഴിക്കോട് നോർത്ത്",
      constituencyNumber: "12A",

      politicalPartyId: 55,
      politicalPartyName: "United Democratic Front",
      politicalPartyNameInLocal: "യുണൈറ്റഡ് ഡെമോക്രാറ്റിക് ഫ്രണ്ട്",
      isActive: true,
      createdAt: "2021-05-20T10:30:00Z",
      lastModifiedAt: "2025-04-10T15:45:00Z",
      createdBy: "admin-uuid",
      lastModifiedBy: "admin-uuid",
    },
  ],
};

export const userProfileHandlers = [
  http.get(BASE_URL + "api/user/current-profile", () => {
    return HttpResponse.json(userProfileData);
  }),
];
