import { assemblyList } from "../../utilis/mock-data/assembly";
import { http, HttpResponse } from "msw";

const { VITE_MDMS_API_BASE_URL } = import.meta.env;

// Mock session data for each KLA (adjust as needed)
const sessionList = {
  1: [
    { id: 1, name: "Session 1A" },
    { id: 2, name: "Session 1B" },
  ],
  2: [
    { id: 3, name: "Session 2A" },
    { id: 4, name: "Session 2B" },
  ],
  3: [
    { id: 5, name: "Session 3A" },
    { id: 6, name: "Session 3B" },
  ],
  4: [
    { id: 7, name: "Session 4A" },
    { id: 8, name: "Session 4B" },
  ],
  // Add more KLA sessions as needed...
};

const assemblyListhandler = [
  // Handler for fetching all assemblies
  http.get(VITE_MDMS_API_BASE_URL + "api/get/assembly", () => {
    return HttpResponse.json(assemblyList, { status: 200 });
  }),

  // Handler for fetching sessions by selected KLA
  http.get(
    VITE_MDMS_API_BASE_URL + "api/get/assembly/:klaId/sessions",
    ({ params }) => {
      const { klaId } = params;
      const response = sessionList[klaId] || [];
      return HttpResponse.json(response, { status: 200 });
    }
  ),
];

export { assemblyListhandler };
