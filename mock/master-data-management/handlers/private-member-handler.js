import { http, HttpResponse, delay } from "msw";

const { VITE_MDM_SERVICE_BASE_URL } = import.meta.env;

const noticeForQuestionToPrivateMembersHandlers = [
  http.get(
    VITE_MDM_SERVICE_BASE_URL + "api/private-member-resolutions",
    async () => {
      await delay(300);

      const resolutions = [
        { id: "416dd842-2ee5-48c7-aee8-a4d9c8ea0df4", name: "Resolution A" },
        { id: "416dd842-2ee5-48c7-aee8-a4d9c8ea0df0", name: "Resolution B" },
      ];

      return HttpResponse.json({ data: resolutions });
    }
  ),

  http.get(VITE_MDM_SERVICE_BASE_URL + "api/private-member-bills", async () => {
    await delay(300);

    const bills = [
      { id: "416dd842-2ee5-48c7-aee8-a4d9c8ea0dfd", name: "Bill <PERSON>" },
      { id: "416dd842-2ee5-48c7-aee8-a4d9c8ea0df6", name: "<PERSON>" },
    ];

    return HttpResponse.json({ data: bills });
  }),

  http.get(VITE_MDM_SERVICE_BASE_URL + "api/private-member-list", async () => {
    await delay(300);

    const members = [
      {
        memberId: 102,
        memberDisplayName: "Anjali Ramesh",
        memberDisplayNameInLocal: "അഞ്ജലി രമേശ്",
        constituencyName: "Thiruvananthapuram North",
        constituencyNameInLocal: "തിരുവനന്തപുരം നോർത്ത്",
        politicalPartyName: "Independent",
        politicalPartyNameInLocal: "സ്വതന്ത്രന്‍",
      },
      {
        memberId: 103,
        memberDisplayName: "Rahul Nair",
        memberDisplayNameInLocal: "റാഹുൽ നായർ",
        constituencyName: "Kazhakoottam",
        constituencyNameInLocal: "കഴക്കൂട്ടം",
        politicalPartyName: "Youth Democratic Union",
        politicalPartyNameInLocal: "യൂത്ത് ഡെമോക്രാറ്റിക് യൂണിയൻ",
      },
      {
        memberId: 104,
        memberDisplayName: "Nimisha K",
        memberDisplayNameInLocal: "നിമിഷ കെ",
        constituencyName: "Sreekariyam",
        constituencyNameInLocal: "ശ്രീകാര്യം",
        politicalPartyName: "Green Students Forum",
        politicalPartyNameInLocal: "ഗ്രീൻ സ്റ്റുഡന്റ്സ് ഫോറം",
      },
      {
        memberId: 101,
        memberDisplayName: "John Smith",
        memberDisplayNameInLocal: "ജോൺ സ്മിത്ത്",
        constituencyName: "Kozhikode North",
        constituencyNameInLocal: "കോഴിക്കോട് നോർത്ത്",
        politicalPartyName: "United Democratic Front",
        politicalPartyNameInLocal: "യുണൈറ്റഡ് ഡെമോക്രാറ്റിക് ഫ്രണ്ട്",
      },
    ];

    return HttpResponse.json({ data: members });
  }),
];

export { noticeForQuestionToPrivateMembersHandlers };
