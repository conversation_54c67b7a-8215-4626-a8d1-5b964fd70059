import { jsxData } from "@/pages/allotment-of-days/constants";
import { delay, http, HttpResponse } from "msw";

const { VITE_MDMS_API_BASE_URL } = import.meta.env;

const calendarOfSittingshandler = [
  http.get(VITE_MDMS_API_BASE_URL + "api/calendar-of-sittings", async () => {
    await delay(300);
    console.log("Intercepted request for calendar-of-sittings");

    const calendarOfSittings = jsxData;
    return HttpResponse.json({
      data: calendarOfSittings,
    });
  }),
];

export { calendarOfSittingshandler };
