import { http, HttpResponse } from "msw";

const { VITE_MDMS_API_BASE_URL } = import.meta.env;

const activeAssembly = {
  assembly: "15",
  session: "13",
};

const activeAssemblyhandler = [
  // Handler for fetching all assemblies
  http.get(VITE_MDMS_API_BASE_URL + "api/active-assembly", () => {
    return HttpResponse.json(activeAssembly, { status: 200 });
  }),
];

export { activeAssemblyhandler };
