import { http, HttpResponse } from "msw";
import { consentMembers } from "../mock-data/consent-members";

const { VITE_MDM_SERVICE_BASE_URL } = import.meta.env;

const consentMembersHandlers = [
  // Get all consent members
  http.get(`${VITE_MDM_SERVICE_BASE_URL}/api/consents/members-consented`, () => {
    return HttpResponse.json(consentMembers, { status: 200 });
  }),
];

export default consentMembersHandlers;