function base64ToFile(base64String, fileName, mimeType) {
  // Decode the base64 string
  const binaryString = atob(base64String);

  // Create a Uint8Array to hold the binary data
  const length = binaryString.length;
  const bytes = new Uint8Array(length);

  // Convert the binary string into bytes
  for (let i = 0; i < length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  // Create a Blob from the byte array
  const blob = new Blob([bytes], { type: mimeType });

  // Create a File object
  const file = new File([blob], fileName, { type: mimeType });

  return file;
}

export { base64ToFile };
export { pdfBase64 } from "./mock-data/pdf-base64";
