// Helper functions for filter operations
const filterOperations = {
  currentNo: (doc, value) => {
    return String(doc.currentNo).toLowerCase().includes(value.toLowerCase());
  },
  documentType: (doc, value) => {
    return String(doc.documentType).toLowerCase().includes(value.toLowerCase());
  },

  equals: (doc, value) => {
    return Object.values(doc).some((field) =>
      String(field).toLowerCase().includes(value.toLowerCase())
    );
  },

  is: (doc, value) => {
    return Object.values(doc).some((field) =>
      String(field).toLowerCase().includes(value.toLowerCase())
    );
  },

  isNot: (doc, value) => {
    return !Object.values(doc).some((field) =>
      String(field).toLowerCase().includes(value.toLowerCase())
    );
  },

  contains: (doc, value) => {
    return Object.values(doc).some((field) =>
      String(field).toLowerCase().includes(value.toLowerCase())
    );
  },

  Is: (doc, value) => {
    return Object.values(doc).some((field) =>
      String(field).toLowerCase().includes(value.toLowerCase())
    );
  },

  between: (doc, value, filterKey) => {
    const [start, end] = value.split(",");
    const docDate = new Date(doc[filterKey]);
    return docDate >= new Date(start) && docDate <= new Date(end);
  },
};

const SKIP_PARAMS = ["page", "size", "search"];

const applyFilters = (documents, url) => {
  return documents.filter((doc) => {
    for (const [key, value] of url.searchParams.entries()) {
      const [filterKey, filterType] = key.split(":");

      // Skip pagination and search parameters
      if (SKIP_PARAMS.includes(filterKey)) {
        continue;
      }

      // Handle special cases for documentType and currentNo
      if (filterKey === "documentType" || filterKey === "currentNo") {
        if (!filterOperations[filterKey](doc, value)) {
          return false;
        }
        continue;
      }

      // Handle standard filter types
      const operation = filterOperations[filterType];
      if (operation) {
        const result =
          filterType === "between"
            ? operation(doc, value, filterKey)
            : operation(doc, value);

        if (!result) {
          return false;
        }
      } else {
        console.warn(`Unhandled filter type: ${filterType}`);
      }
    }

    return true;
  });
};

const createSearchPredicate = (searchQuery) => {
  const lowercaseQuery = searchQuery.toLowerCase();
  return (doc) => {
    if (!searchQuery) return true;
    const matchesField = (value) => {
      if (value === null || value === undefined) return false;
      const stringValue = String(value).toLowerCase();
      return stringValue.includes(lowercaseQuery);
    };

    return (
      matchesField(doc.currentNo) ||
      matchesField(doc.documentType) ||
      matchesField(doc.documentName) ||
      matchesField(doc.delayStatement) ||
      matchesField(doc.createdBy)
    );
  };
};

const paginateResults = (items, page, pageSize) => {
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  return {
    data: items.slice(startIndex, endIndex),
    pagination: {
      page,
      size: pageSize,
      totalElements: items.length,
      totalPages: Math.ceil(items.length / pageSize),
    },
  };
};

export { applyFilters, createSearchPredicate, paginateResults };
