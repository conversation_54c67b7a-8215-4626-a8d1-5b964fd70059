import { delay, http, HttpResponse } from "msw";
import { db } from "../db";

const { VITE_API_BASE_URL } = import.meta.env;
const BASE_URL = VITE_API_BASE_URL;

export const allotmentOfDaysHandlers = [
  // Update an allotment of days
  http.patch(
    BASE_URL + "/question-service/api/allotment-of-days/:documentId",
    async ({ request, params }) => {
      await delay(500);
      const { documentId } = params;
      const body = await request.json();

      const existingAllotment = db.allotmentOfDays.findFirst({
        where: {
          id: {
            equals: documentId,
          },
        },
      });

      if (!existingAllotment) {
        return new HttpResponse(null, {
          status: 404,
          statusText: "Allotment not found",
        });
      }

      const updatedAllotment = db.allotmentOfDays.update({
        where: {
          id: {
            equals: documentId,
          },
        },
        data: {
          allotments: body.allotments || existingAllotment.allotments,
          lastModifiedAt: new Date().toISOString(),
        },
      });

      return HttpResponse.json(updatedAllotment);
    }
  ),
];
