import { http, HttpResponse } from "msw";
const { VITE_API_BASE_URL } = import.meta.env;
const BASE_URL = VITE_API_BASE_URL;

const consentHandlers = [
  http.post(
    BASE_URL + "api/consents/:consentId/withdraw",
    async ({ params }) => {
      const { consentId } = params;
      return HttpResponse.json({
        id: consentId,
        consentType: "PPO",
        memberId: 1001,
        memberDisplayName: "Sophia Miller",
        memberDisplayNameInLocal: "<PERSON> Miller",
        status: "CANCELED",
        requestDate: "2025-03-14T16:08:29.077705",
        responseDate: new Date().toISOString(),
      });
    }
  ),
];

export { consentHandlers };
