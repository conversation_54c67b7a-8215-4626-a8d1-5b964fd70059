import { allotmentOfDaysHandlers } from "./allotment-handlers";
import { consentHandlers } from "./consent-handlers";
import { ministerPhotoHandlers } from "./minister-photo-handlers";
import { partyHandlers } from "./parties-handlers";
import { metadataHandler } from "./document-metadata-handler";
import { lateAnswerBulletinHandlers } from "./late-answer-bulletin";
import { delayedAnswerBulletinHandlers } from "./delayed-answer-bulletin-handlers";
import { memberConsentedHandlers } from "./members-consent-handlers";
import { answerStatusReportHandlers } from "./answer-handlers";
import { unstarredQuestionHandlers } from "./unstarred-questions-handlers";
import { starredQuestionHandlers } from "./starred-questions-handlers";
import { sectionStaffHandlers } from "./section-staff-actions-handlers";
import { sectionStaffAllHandlers } from "./section-staff-all-handlers";
import { delayStatementHandlers } from "./delay-statement-handlers";
import { questionEditHandlers } from "./notice-question-edit";
import { scheduleActivityUpdateHandlers } from "./schedule-of-activity-updates-handlers";
import { privateMemberHandlers } from "./private-member-handlers";
import { checkDuplicateHandler } from "./check-duplicate-handler";

// All notice-related handlers have been removed and replaced with actual API calls
export const handlers = [
  ...ministerPhotoHandlers,
  ...allotmentOfDaysHandlers,
  ...partyHandlers,
  ...metadataHandler,
  ...consentHandlers,
  ...lateAnswerBulletinHandlers,
  ...delayedAnswerBulletinHandlers,
  ...memberConsentedHandlers,
  ...unstarredQuestionHandlers,
  ...starredQuestionHandlers,
  ...answerStatusReportHandlers,
  ...sectionStaffHandlers,
  ...sectionStaffAllHandlers,
  ...delayStatementHandlers,
  ...questionEditHandlers,
  ...scheduleActivityUpdateHandlers,
  ...privateMemberHandlers,
  ...checkDuplicateHandler,
];
