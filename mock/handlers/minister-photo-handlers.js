import {http, HttpResponse} from 'msw';

// Base64 encoded sample image data
// This is a small placeholder image, but in practice you would use a more appropriate minister photo
const sampleImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAQAAAAHUWYVAAABKklEQVR42u3RMQEAAAjDMOb/xy6Y4OBIJTTpKT0qQIAICBABASIgQAQEiIAICBABASIgQAQEiIAICBABASIgQAQEiIAICBABASIgQAQEiIAICBABASIgQAQEiIAICBABASIgQAQEiIAICBABASIgQAQEiIAICBABASIgQAQEiIAICBABASIgQAQEiIAICBABASIgQAQEiIAAMQGIgAARECACAkRAgAiIgAARECACAkRAgAiIgAARECACAkRAgAiIgAARECACAkRAgAiIgAARECACAkRAgAiIgAARECACAkRAgAiIgAARECACAkRAgAiIgAARECACAkRAgAiIgAARECACAkRAgAgIECBABASIgAARECACAkRABASIgAARECACAkRABASIgADRZQuC2T1ACMjwTQAAAABJRU5ErkJggg==';

// Convert base64 to binary using atob (built into browsers)
function base64ToBinary(base64) {
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes;
}

// Minister photo handler
export const ministerPhotoHandlers = [
    // GET photo for a minister by ID
    http.get('/mdm-service/api/minister/:ministerId/photo', async () => {
        // Convert the base64 image to binary
        const imageBytes = base64ToBinary(sampleImageBase64);

        // Return the image as a binary response
        return new HttpResponse(imageBytes, {
            status: 200,
            headers: {
                'Content-Type': 'image/png',
                'Content-Length': imageBytes.length.toString(),
                'Cache-Control': 'public, max-age=86400'
            }
        });
    })
];
