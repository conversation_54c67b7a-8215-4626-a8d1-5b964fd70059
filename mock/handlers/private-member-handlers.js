import { http, HttpResponse } from "msw";

// Mock data for private member resolutions
const mockResolutions = [
  {
    id: "416dd842-2ee5-48c7-aee8-a4d9c8ea0df4",
    name: "Resolution on Climate Change",
  },
  {
    id: "416dd842-2ee5-48c7-aee8-a4d9c8ea0df3",
    name: "Resolution on Infrastructure Development",
  },
  {
    id: "416dd842-2ee5-48c7-aee8-a4d9c8ea0df2",
    name: "Resolution on Education Reform",
  },
  {
    id: "416dd842-2ee5-48c7-aee8-a4d9c8ea0df1",
    name: "Resolution on Healthcare Improvement",
  },
  {
    id: "416dd842-2ee5-48c7-aee8-a4d9c8ea0df0",
    name: "Resolution on Rural Development",
  },
];

// Mock data for private member bills
const mockBills = [
  {
    id: "116dd842-2ee5-48c7-aee8-a4d9c8ea0df4",
    name: "Climate Protection Act",
  },
  {
    id: "216dd842-2ee5-48c7-aee8-a4d9c8ea0df4",
    name: "Infrastructure Development Bill",
  },
  { id: "316dd842-2ee5-48c7-aee8-a4d9c8ea0df4", name: "Education Reform Bill" },
  {
    id: "416dd842-2ee5-48c7-aee8-a4d9c8ea0df4",
    name: "Healthcare Improvement Bill",
  },
  {
    id: "516dd842-2ee5-48c7-aee8-a4d9c8ea0df4",
    name: "Rural Development Bill",
  },
];

// Mock data for private member list
const mockMembers = [
  {
    memberId: "1",
    memberDisplayName: "John Doe",
    memberDisplayNameInLocal: "ജോൺ ഡോ",
    constituencyName: "North District",
    constituencyNameInLocal: "നോർത്ത് ഡിസ്ട്രിക്റ്റ്",
    politicalPartyName: "Democratic Party",
    politicalPartyNameInLocal: "ഡെമോക്രാറ്റിക് പാർട്ടി",
  },
  {
    memberId: "2",
    memberDisplayName: "Jane Smith",
    memberDisplayNameInLocal: "ജെയ്ൻ സ്മിത്ത്",
    constituencyName: "South District",
    constituencyNameInLocal: "സൗത്ത് ഡിസ്ട്രിക്റ്റ്",
    politicalPartyName: "Republican Party",
    politicalPartyNameInLocal: "റിപ്പബ്ലിക്കൻ പാർട്ടി",
  },
  {
    memberId: "3",
    memberDisplayName: "Michael Johnson",
    memberDisplayNameInLocal: "മൈക്കിൾ ജോൺസൺ",
    constituencyName: "East District",
    constituencyNameInLocal: "ഈസ്റ്റ് ഡിസ്ട്രിക്റ്റ്",
    politicalPartyName: "Independent",
    politicalPartyNameInLocal: "സ്വതന്ത്രൻ",
  },
  {
    memberId: "4",
    memberDisplayName: "Emily Williams",
    memberDisplayNameInLocal: "എമിലി വില്യംസ്",
    constituencyName: "West District",
    constituencyNameInLocal: "വെസ്റ്റ് ഡിസ്ട്രിക്റ്റ്",
    politicalPartyName: "Green Party",
    politicalPartyNameInLocal: "ഗ്രീൻ പാർട്ടി",
  },
  {
    memberId: "5",
    memberDisplayName: "Robert Brown",
    memberDisplayNameInLocal: "റോബർട്ട് ബ്രൗൺ",
    constituencyName: "Central District",
    constituencyNameInLocal: "സെൻട്രൽ ഡിസ്ട്രിക്റ്റ്",
    politicalPartyName: "Workers Party",
    politicalPartyNameInLocal: "തൊഴിലാളി പാർട്ടി",
  },
];

// Define HTTP handlers
export const privateMemberHandlers = [
  // Handler for private member resolutions
  http.get(
    "http://172.24.12.50/mdm-service/api/private-member-resolutions",
    () => {
      return HttpResponse.json(
        {
          success: true,
          data: mockResolutions,
          timestamp: new Date().toISOString(),
        },
        { status: 200 }
      );
    }
  ),

  // Handler for private member bills
  http.get("http://172.24.12.50/mdm-service/api/private-member-bills", () => {
    return HttpResponse.json(
      {
        success: true,
        data: mockBills,
        timestamp: new Date().toISOString(),
      },
      { status: 200 }
    );
  }),

  // Handler for private member list
  http.get("http://172.24.12.50/mdm-service/api/private-member-list", () => {
    return HttpResponse.json(
      {
        success: true,
        data: mockMembers,
        timestamp: new Date().toISOString(),
      },
      { status: 200 }
    );
  }),
];
