import {http, HttpResponse} from 'msw';

// Base64 encoded sample image data
// This is a small placeholder image, but in practice you would use a more appropriate minister photo
const sampleImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH5gcMESkZ7XWMqwAABO1JREFUeNrtnT1oHEcUx/9v9vRhJJzK5EQcpVA7hQprIbsIO6VBpFXnCilc5JQmhYsUQTgGfYCbQMDCRj5LJK2bVEkjA8KF2kBMTlgGlVEhdL6d92ZnN7c639ndWe3e/sA4xXn35r33m/fezuyMIFPx83OAbgJYBfAyACu8XGJ/2R5+GzgA8ADA7wB+sdI7P7Ivf2dflDQQjDGOGOIvTu6S+SdMeD8N5ASUL+DsjfeTzLstj2MAXcvJ8z7yPW3NUDx3BYx5Cz2/vNWYawP/1C0k64iTrJB4pKJX4F8Cxr4BjngHjkAW4xJ2pQ1xSB4IhOAgL+Gyd5iGKxo5/cFpWe/t2Jc/ADAeWPRbVVD/G1jj/4qFG8JiAeL/KuGhLQC9HbD31QAZRfQiVQPSu9DZAsXfQvEHoPiPhCEq54TiWyC+7fSXhkxbFLwK9O8AvRdKDC19hqj2x9xXACw1LwbQvwWK3imd6bYNocjnKP7X6j/qmKcDZt/5xXBkSJb4WdjygpDt4Ay/B+JrIOoYFpqmKLqJfNdECmfGVFRnQEYR/QbRu2bGUHQD+W5x+pGZMdYxJIvozAKZGUQbKEZvqIHCDCJZYYZs5DYcOXU2G/nuvA0pnP+MLCL6A6QPZjLyCZA/BYougqL1eSRfV4YwfKwIKN4wExq9hXxvdQrNmmWITpbYhcL1+aRdRVD/OvLHhq1PbUgW8TaAFTPBKLqBfHeF3Vdkn52axmXtxQJQvDWbZE9qSJ65BUg81gfGZCj6Evnu0Vkj0xVZ2I8Fx/B1n2jDpBS9BOSPDIrFkoa0EcZpw4iuIN9daJihRqVz1z8hfrm5aXvNZlD0A/Ld4ekjKGVIlvFlyKQbCYbo/MoNZFFxzWyYIXk2aUiBqy3X6fvIdxMlUcZkSJbZt2JCsQCdGSRcYIaQrFBDNnJdDZGsVRtQyFQbQvLSyJ8kN2QjtxWxL1WDmjKkIYZUYUhbTAWZsVQbInnV4ZGtIUhFCJIVZshGbsORU2ez0XpDdB5Q1qJBMSNJw+yRMEMVbGiHoQRj5FQhSFZtCEYOzJANJnIajpw6mw2ZISwrTE1DsC8rttlS4yz5s2yKVqtB6TSEX0v+YEFFnJ5fO9xsdBpCEgAOgeLrZkKjd5Dv6u+iqUMZSTwOj9VO1MUOz6dskNQ7Y0T8O0CfGwmMXkO+qzcApDzjXe3xtw4+M4DoNeR72v1PMyTLeAXAX00ZAqJXke9pm1F56d7tYQeg7aYM6QDYbs6QMH1v3RCKrr4hv7VnSFvI1xLJPgCEb8qQwmlbWPpG+XcGAz54DPTmfPprTkIAFnlg8Vuboc5B9A3yvWp7KJUnTb589Gji9QJAtvlFtJQR+ZYZvqvyKdqVzDo+jKUt8k1Hn5PsFLSRmykLvO1rIZ3BceTfB3AFiOzWDHGAh5aT5wDcC5qGDHGY7CaY+JOX8HPw9IIhYq69ZSVyRdFudnwXrBm8NWY6Q/J29qZxkh2iE+xkr4/5fvQ2jnCx79rDr0P09J2s8UpKKMoZMpqGIE2GIBliAMkQA0iGGEAyxACSIQaQDDGAZIgBJEMMIBlSGrOJv44hTYcgTYYgGWIA/wMKMCNBf8QBLwAAACV0RVh0ZGF0ZTpjcmVhdGUAMjAyMi0wNy0xMlQxNzo0MToyNSswMDowMPMJkUwAAAAldEVYdGRhdGU6bW9kaWZ5ADIwMjItMDctMTJUMTc6NDE6MjUrMDA6MDCCVCnwAAAAAElFTkSuQmCC';

// Convert base64 to binary using atob (built into browsers)
function base64ToBinary(base64) {
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes;
}

// Minister photo handler
export const ministerPhotoHandlers = [
    // GET photo for a minister by ID
    http.get('/mdm-service/api/minister/:ministerId/photo', async () => {
        // Convert the base64 image to binary
        const imageBytes = base64ToBinary(sampleImageBase64);

        // Return the image as a binary response
        return new HttpResponse(imageBytes, {
            status: 200,
            headers: {
                'Content-Type': 'image/png',
                'Content-Length': imageBytes.length.toString(),
                'Cache-Control': 'public, max-age=86400'
            }
        });
    })
];
