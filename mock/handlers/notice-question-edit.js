
import { delay, http, HttpResponse } from "msw";

const { VITE_API_BASE_URL } = import.meta.env;
const BASE_URL = VITE_API_BASE_URL;


const mockDataStore = {
  basicDetails: {
    
    "1": {
      id: "1",
      type: "NOTICE_FOR_QUESTION",
      assembly: 15,
      session: 13,
      createdAt: "2025-04-23T09:35:59.081151",
      createdBy: "user123",
      noticeNumber: "1234",
      questionDate: "2025-04-15",
      dateOfRegistration: "2025-04-15",
      ministerDesignationId: 2,
      ministerId: 456,
      portfolioId: 1,
      subSubjectId: 3,
      primaryMember: {
        memberId: 4780,
        memberDisplayName: "Shri<PERSON>R<PERSON><PERSON>",
        memberDisplayNameInLocal: "മൈക്കൽ ഫെർണാണ്ടസ്",
        constituencyId: null,
        constituencyName: "Angamaly",
        constituencyNameInLocal: "മെട്രോ ജില്ല",
        constituencyNumber: null,
        politicalPartyId: null,
        politicalPartyName: "United Democratic Front",
        politicalPartyNameInLocal: "ന്യൂ പ്രോഗ്രസ്സീവ് പാർട്ടി",
      },
      secondaryMembers: {
        memberDisplayName: "<PERSON> <PERSON>",
        memberDisplayNameInLocal: "മൈക്കൽ ഫെർണാണ്ടസ്",
        constituencyId: null,
        constituencyName: "Aluva",
        constituencyNameInLocal: "മെട്രോ ജില്ല",
        constituencyNumber: null,
        politicalPartyId: null,
        politicalPartyName: "United Democratic Front",
        politicalPartyNameInLocal: "ന്യൂ പ്രോഗ്രസ്സീവ് പാർട്ടി",
      },
      clauses: [],
    },
   
    "2": {
      id: "2",
      type: "NOTICE_FOR_QUESTION",
      assembly: 15,
      session: 14,
      createdAt: "2025-05-01T10:20:45.123456",
      createdBy: "user456",
      noticeNumber: "5678",
      questionDate: "2025-05-10",
      dateOfRegistration: "2025-05-01",
      ministerDesignationId: 3,
      ministerId: 789,
      portfolioId: 2,
      subSubjectId: 4,
      primaryMember: {
        memberId: 4790,
        memberDisplayName: "Smt. Anna Elizabeth",
        memberDisplayNameInLocal: "അന്ന എലിസബത്ത്",
        constituencyId: null,
        constituencyName: "Thrissur",
        constituencyNameInLocal: "തൃശൂർ",
        constituencyNumber: null,
        politicalPartyId: null,
        politicalPartyName: "Left Democratic Front",
        politicalPartyNameInLocal: "ഇടതുപക്ഷ ജനാധിപത്യ മുന്നണി",
      },
      secondaryMembers: [
        {
          memberDisplayName: "Shri Mathew Thomas",
          memberDisplayNameInLocal: "മാത്യു തോമസ്",
          constituencyId: null,
          constituencyName: "Kottayam",
          constituencyNameInLocal: "കോട്ടയം",
          constituencyNumber: null,
          politicalPartyId: null,
          politicalPartyName: "Left Democratic Front",
          politicalPartyNameInLocal: "ഇടതുപക്ഷ ജനാധിപത്യ മുന്നണി",
        }
      ],
      clauses: [],
    }
  },
  noticeDetails: {
    "1": {
      id: "1",
      type: "NOTICE_FOR_QUESTION",
      status: "DRAFT",
      assembly: 15,
      session: 13,
      createdAt: "2025-04-24T05:33:40.315118",
      lastModifiedAt: "2025-04-24T05:33:40.315118",
      createdBy: "user123",
      lastModifiedBy: "user123",
      noticeNumber: "1234",
      starred: true,
      noticePriority: "P1",
      noticeHeading: "കെടിട സമുച്ചയങ്ങളിലെ പാർക്കിംഗ് ഇല്ലായ്മക്ക് ",
      primaryMember: {
        memberId: 4780,
        memberDisplayName: "Shri.Roji M John",
        memberDisplayNameInLocal: "മൈക്കൽ ഫെർണാണ്ടസ്",
        constituencyId: null,
        constituencyName: "Angamaly",
        constituencyNameInLocal: "മെട്രോ ജില്ല",
        constituencyNumber: null,
        politicalPartyId: null,
        politicalPartyName: "United Democratic Front",
        politicalPartyNameInLocal: "ന്യൂ പ്രോഗ്രസ്സീവ് പാർട്ടി",
      },
      secondaryMembers: {
        memberDisplayName: "Shri Anwar Sadath",
        memberDisplayNameInLocal: "മൈക്കൽ ഫെർണാണ്ടസ്",
        constituencyId: null,
        constituencyName: "Aluva",
        constituencyNameInLocal: "മെട്രോ ജില്ല",
        constituencyNumber: null,
        politicalPartyId: null,
        politicalPartyName: "United Democratic Front",
        politicalPartyNameInLocal: "ന്യൂ പ്രോഗ്രസ്സീവ് പാർട്ടി",
      },
      clauses: [
        {
          id: "clause-001",
          content: "2016-17 സാമ്പത്തിക വര്‍ഷം മുതല്‍ നാളിതുവരെ കേന്ദ്രസര്‍ക്കാര്‍ നടപ്പിലാക്കിയ വാഹന നിര്‍മ്മാണ മേഖല സമ്പൂര്‍ണ്ണമായി തദ്ദേശീയ പുതുതലമുറ സാങ്കേതികവിദ്യ ഉപയോഗിക്കണമെന്ന് സംബദ്ധമായ കോമ്പനികളോട് അനിവാര്യമാക്കിയത് എന്നുമുതലാണെന്ന് വിശദമാക്കുമോ;",
          order: 1,
          tags: [
            { title: "Partially Disallow", ruleRef: "Rule.1(a),1(b)" }
          ]
        },
        {
          id: "clause-002",
          content: "വാഹന നിര്‍മ്മാണ മേഖലയിലെ ഇലക്ട്രോണിക്സിന്റെ പാലിച്ച ഇതു വഴം സംബന്ധി  ആവശ്യകതയാണ് ആലിംകരിക്കുന്നത് വെളിപ്പെടിത്തുമോ;",
          order: 2,
          tags: [
            { title: "Disallow" }
          ]
        },
        {
          id: "clause-003",
          content: "ഇലക്ട്രോണിക്സിന്റെ പാലിച്ച പുതുതലമുറ സാങ്കേതികവിദ്യ സ്വീകരിക്കുന്നത് സംബന്ധിച്ചിട്ടുള്ള എന്തെല്ലാം പ്രശ്നങ്ങള്‍ പാര്‍ട്ട് അനുഭവിക്കുന്നു കാര്യം അറിയിക്കാമോ പ്രശ്നങ്ങള്‍ പരിഹരിക്കാമോ",
          order: 3,
          tags: [
            { title: "Disallow"}
          ]
        }
      ],
    },
    "2": {
      id: "2",
      type: "NOTICE_FOR_QUESTION",
      status: "PENDING",
      assembly: 15,
      session: 14,
      createdAt: "2025-05-01T10:20:45.123456",
      lastModifiedAt: "2025-05-02T11:25:30.789012",
      createdBy: "user456",
      lastModifiedBy: "user456",
      noticeNumber: "5678",
      starred: false,
      noticePriority: "P2",
      noticeHeading: "പ്രാദേശിക വ്യവസായങ്ങളുടെ വികസനം സംബന്ധിച്ച്",
      primaryMember: {
        memberId: 4790,
        memberDisplayName: "Smt. Anna Elizabeth",
        memberDisplayNameInLocal: "അന്ന എലിസബത്ത്",
        constituencyId: null,
        constituencyName: "Thrissur",
        constituencyNameInLocal: "തൃശൂർ",
        constituencyNumber: null,
        politicalPartyId: null,
        politicalPartyName: "Left Democratic Front",
        politicalPartyNameInLocal: "ഇടതുപക്ഷ ജനാധിപത്യ മുന്നണി",
      },
      secondaryMembers: [
        {
          memberDisplayName: "Shri Mathew Thomas",
          memberDisplayNameInLocal: "മാത്യു തോമസ്",
          constituencyId: null,
          constituencyName: "Kottayam",
          constituencyNameInLocal: "കോട്ടയം",
          constituencyNumber: null,
          politicalPartyId: null,
          politicalPartyName: "Left Democratic Front",
          politicalPartyNameInLocal: "ഇടതുപക്ഷ ജനാധിപത്യ മുന്നണി",
        }
      ],
      clauses: [
        {
          id: "clause-004",
          content: "സംസ്ഥാനത്തെ പ്രാദേശിക വ്യവസായങ്ങൾക്ക് നൽകുന്ന പ്രോത്സാഹനങ്ങൾ എന്തെല്ലാമാണെന്ന് വിശദമാക്കാമോ;",
          order: 1,
          tags: []
        },
        {
          id: "clause-005",
          content: "കഴിഞ്ഞ അഞ്ച് വർഷത്തിനിടയിൽ എത്ര പുതിയ പ്രാദേശിക വ്യവസായങ്ങൾ ആരംഭിച്ചിട്ടുണ്ട് എന്ന് വിശദമാക്കാമോ;",
          order: 2,
          tags: []
        }
      ],
    }
  }
};

export const questionEditHandlers = [
  // GET Basic Details
  http.get(BASE_URL + "api/question/basic-details", async ({ request }) => {
    await delay(300);
    const url = new URL(request.url);
    const id = url.searchParams.get("id");

    const basicDetails = mockDataStore.basicDetails[id];

    if (!basicDetails) {
      return HttpResponse.json({ message: "Basic details not found" }, { status: 404 });
    }

    return HttpResponse.json(basicDetails);
  }),

  // PATCH Basic Details
  http.patch(BASE_URL + "api/question/basic-details", async ({ request }) => {
    const body = await request.json();
    const id = body.id;
    
    if (mockDataStore.basicDetails[id]) {
      mockDataStore.basicDetails[id] = {
        ...mockDataStore.basicDetails[id],
        ...body,
        lastModifiedAt: new Date().toISOString()
      };
    }

    return HttpResponse.json(mockDataStore.basicDetails[id]);
  }),

  // GET Notice Details
  http.get(BASE_URL + "api/question/notice-details", async ({ request }) => {
    await delay(300);
    const url = new URL(request.url);
    const id = url.searchParams.get("id");

    const noticeDetails = mockDataStore.noticeDetails[id];

    if (!noticeDetails) {
      return HttpResponse.json({ message: "Notice details not found" }, { status: 404 });
    }

    return HttpResponse.json(noticeDetails);
  }),

  // PATCH Notice Details
  http.patch(BASE_URL + "api/question/notice-details", async ({ request }) => {
    const body = await request.json();
    const id = body.id;
    
    if (mockDataStore.noticeDetails[id]) {
      mockDataStore.noticeDetails[id] = {
        ...mockDataStore.noticeDetails[id],
        ...body,
        lastModifiedAt: new Date().toISOString()
      };
    }

    return HttpResponse.json(mockDataStore.noticeDetails[id]);
  }),
];