import { http, HttpResponse } from "msw";
import { mock_starred_approved, mock_starred_pending } from "./constant";
import { formatedDate } from "@/utils";

const { VITE_API_BASE_URL } = import.meta.env;
const BASE_URL = VITE_API_BASE_URL;

export const starredQuestionHandlers = [

  http.get(BASE_URL + "api/documents-mockStarred", async ({ request }) => {
    const url = new URL(request.url);
    const date = url.searchParams.get("date");

    const today = formatedDate(new Date(), "yyyy-MM-dd");
    const yesterday = formatedDate(
      new Date(Date.now() - 86400000),
      "yyyy-MM-dd"
    ); 
    let responseData;

    if (formatedDate(date, "yyyy-MM-dd") === today) {
      responseData = {
        ...mock_starred_pending,
        id: date,
      };
      console.log(mock_starred_pending, "gfgtrd,,,,,,,,");
    } else if (formatedDate(date, "yyyy-MM-dd") === yesterday) {
      responseData = {
        ...mock_starred_approved,
        id: date,
      };
    } else {
      console.error();
    }

    return HttpResponse.json(responseData);
  }),
];
