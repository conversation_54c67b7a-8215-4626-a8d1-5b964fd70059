import { delay, http, HttpResponse } from "msw";
import uuid4 from "uuid4";

const { VITE_API_BASE_URL } = import.meta.env;
const BASE_URL = VITE_API_BASE_URL;

export const lateAnswerBulletinHandlers = [
  // Create LAB document
  http.post(BASE_URL + "api/documents-mock/draft", async ({ request }) => {
    await delay(500);
    const body = await request.json();

    if (body.type !== "LATE_ANSWER_BULLETIN") {
      return new HttpResponse(null, {
        status: 400,
        statusText: "Invalid document type",
      });
    }

    const mockLabDocument = {
      id: uuid4(),
      type: "LATE_ANSWER_BULLETIN",
      name: body.name || "LAB-2024-001",
      status: "DRAFT",
      currentNumber: "LAB-2024-001",
      assembly: body.assembly || 16,
      session: body.session || 3,
      lateAnswerStats: [
        {
          id: uuid4(),
          questionDate: "2024-06-01",
          starredQuestionCount: 5,
          unstarredQuestionCount: 15,
          totalQuestionCount: 20,
          questionsAnsweredOnTime: {
            starredWithInterimAnswerCount: 1,
            starredWithFinalAnswerCount: 3,
            unstarredWithInterimAnswerCount: 5,
            unstarredWithFinalAnswerCount: 7,
            totalQuestionCount: 16,
          },
          questionsNotAnsweredOnTime: {
            starredQuestionCount: 1,
            unstarredQuestionCount: 3,
            totalQuestionCount: 4,
          },
          questionsAnsweredLate: {
            starredWithInterimAnswerCount: 0,
            starredWithFinalAnswerCount: 0,
            unstarredWithInterimAnswerCount: 2,
            unstarredWithInterimAndFinalAnswerCount: 1,
            unstarredWithFinalAnswerCount: 1,
            totalQuestionCount: 4,
          },
          questionsNotAnswered: {
            starredQuestionCount: 0,
            unstarredQuestionCount: 0,
            totalQuestionCount: 0,
          },
        },
      ],
      createdAt: new Date().toISOString(),
      lastModifiedAt: new Date().toISOString(),
      createdBy: "new-user",
      lastModifiedBy: "new-user",
    };

    return HttpResponse.json(mockLabDocument);
  }),

  // Get LAB document
  http.get(BASE_URL + "api/documents-mock/:id", async ({ params }) => {
    await delay(500);

    const mockLabDocument = {
      id: params.id,
      type: "LATE_ANSWER_BULLETIN",
      name: "LAB-2024-001",
      status: "DRAFT",
      currentNumber: "LAB-2024-001",
      assembly: 16,
      session: 3,
      lateAnswerStats: [
        {
          id: uuid4(),
          questionDate: "2024-06-01",
          starredQuestionCount: 5,
          unstarredQuestionCount: 15,
          totalQuestionCount: 20,
          questionsAnsweredOnTime: {
            starredWithInterimAnswerCount: 1,
            starredWithFinalAnswerCount: 3,
            unstarredWithInterimAnswerCount: 5,
            unstarredWithFinalAnswerCount: 7,
            totalQuestionCount: 16,
          },
          questionsNotAnsweredOnTime: {
            starredQuestionCount: 1,
            unstarredQuestionCount: 3,
            totalQuestionCount: 4,
          },
          questionsAnsweredLate: {
            starredWithInterimAnswerCount: 0,
            starredWithFinalAnswerCount: 0,
            unstarredWithInterimAnswerCount: 2,
            unstarredWithInterimAndFinalAnswerCount: 1,
            unstarredWithFinalAnswerCount: 1,
            totalQuestionCount: 4,
          },
          questionsNotAnswered: {
            starredQuestionCount: 0,
            unstarredQuestionCount: 0,
            totalQuestionCount: 0,
          },
        },
        {
          id: uuid4(),
          questionDate: "2024-06-11",
          starredQuestionCount: 8,
          unstarredQuestionCount: 12,
          totalQuestionCount: 20,
          questionsAnsweredOnTime: {
            starredWithInterimAnswerCount: 2,
            starredWithFinalAnswerCount: 4,
            unstarredWithInterimAnswerCount: 3,
            unstarredWithFinalAnswerCount: 5,
            totalQuestionCount: 14,
          },
          questionsNotAnsweredOnTime: {
            starredQuestionCount: 2,
            unstarredQuestionCount: 4,
            totalQuestionCount: 6,
          },
          questionsAnsweredLate: {
            starredWithInterimAnswerCount: 0,
            starredWithFinalAnswerCount: 0,
            unstarredWithInterimAnswerCount: 3,
            unstarredWithInterimAndFinalAnswerCount: 2,
            unstarredWithFinalAnswerCount: 1,
            totalQuestionCount: 6,
          },
          questionsNotAnswered: {
            starredQuestionCount: 0,
            unstarredQuestionCount: 0,
            totalQuestionCount: 0,
          },
        },
      ],
      createdAt: new Date().toISOString(),
      lastModifiedAt: new Date().toISOString(),
      createdBy: "new-user",
      lastModifiedBy: "new-user",
    };

    return HttpResponse.json(mockLabDocument);
  }),
];
