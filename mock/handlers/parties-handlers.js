import { http, HttpResponse } from "msw";
import { mock_mlas, parties } from "./constant";

const { VITE_API_BASE_URL } = import.meta.env;

export const partyHandlers = [
  http.get(VITE_API_BASE_URL + "api/parties", async () => {
    return HttpResponse.json({ data: parties }, { status: 200 });
  }),

  http.get(VITE_API_BASE_URL + "api/members", async ({ request }) => {
    const url = new URL(request.url);
    const partyId = url.searchParams.get("partyId");
    const type = url.searchParams.get("type");
    if (type === "independent") {
      const independentMLAs = mock_mlas.filter(
        (mla) => !mla.id || mla.type === "independent"
      );
      return HttpResponse.json({ data: independentMLAs }, { status: 200 });
    }
    if (!partyId) {
      return HttpResponse.json({ data: mock_mlas }, { status: 200 });
    }
    const filteredMembers = mock_mlas.filter(
      (mla) => mla.id === parseInt(partyId, 10)
    );
    return HttpResponse.json({ data: filteredMembers }, { status: 200 });
  }),
];
