import { http, HttpResponse } from "msw";
import { v4 as uuidv4 } from "uuid";

// Full mock database (100 sample notices)
const mockNotices = Array.from({ length: 100 }, (_, index) => ({
  noticeId: uuidv4(),
  starred: index % 2 === 0,
  questionNumber: 1000 + index,
  matchType: index % 2 === 0 ? "HEADING" : "CLAUSE",
  matchContent: `Sample question about ${
    index % 3 === 0 ? "education" : index % 5 === 0 ? "health" : "climate"
  } reform and infrastructure (${index})`,
}));

export const checkDuplicateHandler = [
  http.get(
    "http://172.24.12.50/question-service/api/check-duplicate",
    ({ request }) => {
      const url = new URL(request.url);
      const searchParams = url.searchParams;

      const searchKeywords = searchParams.getAll("searchKeywords");
      const matchType = searchParams.get("matchType");
      const page = Number(searchParams.get("page") || 0);
      const size = Number(searchParams.get("size") || 10);

      const filtered = mockNotices.filter((item) => {
        const matchesKeyword = searchKeywords.some((keyword) =>
          item.matchContent.toLowerCase().includes(keyword.toLowerCase())
        );
        const matchesType = !matchType || item.matchType === matchType;
        return matchesKeyword && matchesType;
      });

      const start = page * size;
      const end = start + size;
      const paginatedContent = filtered.slice(start, end);

      return HttpResponse.json({
        first: page === 0,
        last: end >= filtered.length,
        page,
        size,
        totalElements: filtered.length,
        totalPages: Math.ceil(filtered.length / size),
        content: paginatedContent,
      });
    }
  ),
];
