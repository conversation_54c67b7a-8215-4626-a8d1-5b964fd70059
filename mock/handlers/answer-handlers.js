import { http, HttpResponse } from "msw";
import uuid4 from "uuid4";
const { VITE_API_BASE_URL } = import.meta.env;
const BASE_URL = VITE_API_BASE_URL;

const MOCK_DB_KEY = "mockAnswerStatusDB";

// Function to load the mock database from localStorage
const loadMockDB = () => {
  const storedDB = localStorage.getItem(MOCK_DB_KEY);
  return storedDB ? JSON.parse(storedDB) : {};
};

// Function to save the mock database to localStorage
const saveMockDB = (db) => {
  localStorage.setItem(MOCK_DB_KEY, JSON.stringify(db));
};

let mockAnswerStatusDB = loadMockDB();

if (Object.keys(mockAnswerStatusDB).length === 0) {
  const defaultId = uuid4();
  mockAnswerStatusDB[defaultId] = {
    id: defaultId,
    type: "ANSWER_STATUS_REPORT",
    name: "Sample Answer Status Report",
    status: "DRAFT",
    currentNumber: "ASR-001",
    assembly: "1",
    session: "1",
    answerStatusReport: [
      {
        designationInLocal: "പ്രധാനമന്ത്രി",
        questionDates: "2025-04-01",
        unstarredQuestionNumbersWithoutAnswer: 12,
        unstarredQuestionsWithInterimAnswerAndNotFinalAnswer: 5,
        countOfQuestionsAwaitingDelayStatement: 3,
      },
      {
        designationInLocal: "സഹമന്ത്രി",
        questionDates: "2025-04-05",
        unstarredQuestionNumbersWithoutAnswer: 7,
        unstarredQuestionsWithInterimAnswerAndNotFinalAnswer: 2,
        countOfQuestionsAwaitingDelayStatement: 1,
      },
    ],
    createdAt: new Date().toISOString(),
    lastModifiedAt: new Date().toISOString(),
    createdBy: "System",
    lastModifiedBy: "System",
  };

  saveMockDB(mockAnswerStatusDB);
}

const answerStatusReportHandlers = [
  http.post(BASE_URL + "api/documents-mockas/draft", async ({ request }) => {
    const requestData = await request.json();
    const id = uuid4();

    const mockResponse = {
      id: id,
      type: "ANSWER_STATUS_REPORT",
      name: requestData.name || "Sample Answer Status Report",
      status: "DRAFT",
      currentNumber: "123",
      assembly: requestData.assembly || "1",
      session: requestData.session || "1",
      answerStatusReport: requestData.answerStatusReport || [],
      createdAt: new Date().toISOString(),
      lastModifiedAt: new Date().toISOString(),
      createdBy: "System",
      lastModifiedBy: "System",
    };

    mockAnswerStatusDB[id] = mockResponse;
    saveMockDB(mockAnswerStatusDB); // Save to localStorage

    return HttpResponse.json(mockResponse, { status: 201 });
  }),

  http.get(
    BASE_URL + "api/documents-mockas/:documentId",
    async ({ params }) => {
      mockAnswerStatusDB = loadMockDB();
      const { documentId } = params;

      if (mockAnswerStatusDB[documentId]) {
        return HttpResponse.json(mockAnswerStatusDB[documentId], {
          status: 200,
        });
      } else {
        return HttpResponse.json(
          { error: "Answer Status Report not found" },
          { status: 404 }
        );
      }
    }
  ),

  http.post(
    BASE_URL + "api/documents-mockas/:documentId/submit",
    async ({ params, request }) => {
      mockAnswerStatusDB = loadMockDB();
      const { documentId } = params;
      const requestData = await request.json();

      if (mockAnswerStatusDB[documentId]) {
        mockAnswerStatusDB[documentId] = {
          ...mockAnswerStatusDB[documentId],
          assembly: requestData.assembly,
          session: requestData.session,
          answerStatusReport: [
            {
              designationInLocal: "പ്രധാനമന്ത്രി",
              questionDates: ["2025-04-01"],
              unstarredQuestionNumbersWithoutAnswer: 12,
              unstarredQuestionsWithInterimAnswerAndNotFinalAnswer: [5],
              countOfQuestionsAwaitingDelayStatement: [3],
            },
            {
              designationInLocal: "സഹമന്ത്രി",
              questionDates: ["2025-04-05"],
              unstarredQuestionNumbersWithoutAnswer: 7,
              unstarredQuestionsWithInterimAnswerAndNotFinalAnswer: [2],
              countOfQuestionsAwaitingDelayStatement: [1],
            },
          ],
          lastModifiedAt: new Date().toISOString(),
        };
        saveMockDB(mockAnswerStatusDB); // Save to localStorage

        return HttpResponse.json(mockAnswerStatusDB[documentId], {
          status: 200,
        });
      } else {
        return HttpResponse.json(
          { error: "Answer Status Report not found" },
          { status: 404 }
        );
      }
    }
  ),
];

export { answerStatusReportHandlers };
