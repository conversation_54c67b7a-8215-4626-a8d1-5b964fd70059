import { http, HttpResponse } from "msw";

const { VITE_API_BASE_URL } = import.meta.env;

const baseUrl = VITE_API_BASE_URL;

const metadataHandler = [
  http.get(
    `${baseUrl}api/documents/:documentId/metadata`,
    async ({ params }) => {
      const { documentId } = params;

      const mockMetadata = {
        id: documentId,  
        assembly: "15",
        session:"13",
        documentType: "Setting of Starred Question",
        createdOn: "16-08-2024",
        createdBy: "Question and answers",
      };

      return HttpResponse.json(mockMetadata);
    }
  ),
];

export { metadataHandler };
