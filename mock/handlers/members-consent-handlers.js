import { http, HttpResponse } from "msw";
import { db } from "../db";

const { VITE_MDM_SERVICE_BASE_URL } = import.meta.env;
const BASE_URL = VITE_MDM_SERVICE_BASE_URL;

const memberConsentedHandlers = [
  http.get(BASE_URL + "api/consents/members-consented", () => {
    const members = db.member.getAll();

    const responseData = members.map((member) => ({
      memberId: member.memberId,
      memberDisplayName: member.memberDisplayName,
      memberDisplayNameInLocal: member.memberDisplayNameInLocal,
      constituencyId: member.constituencyId,
      constituencyName: member.constituencyName,
      constituencyNameInLocal: member.constituencyNameInLocal,
      politicalPartyId: member.politicalPartyId,
      politicalPartyName: member.politicalPartyName,
      politicalPartyNameInLocal: member.politicalPartyNameInLocal,
    }));

    return HttpResponse.json(responseData);
  }),
];

export { memberConsentedHandlers };
