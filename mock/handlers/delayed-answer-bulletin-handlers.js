import { http, HttpResponse } from "msw";

const { VITE_API_BASE_URL } = import.meta.env;
const BASE_URL = VITE_API_BASE_URL;

// In-memory database to store posted data
const mockDB = {};

// POST handler - Store posted data
const delayedAnswerBulletinHandlers = [
  http.post(BASE_URL + "api/documents-mockdab/draft", async ({ request }) => {
    const requestData = await request.json();
    const id = `doc-${Date.now()}`; // Unique ID for the document

    const mockResponse = {
      id,
      type: "DELAYED_ANSWER_BULLETIN",
      name: requestData.name || "Sample Bulletin",
      status: "DRAFT",
      currentNumber: "1234",
      assembly: requestData.assembly || 1,
      session: requestData.session || 1,
      delayedAnswers: [
        {
          id: "12",
          assembly: 14,
          session: 22,
          designationId: 100,
          designation: "Health",
          designationInLocal: "ആരോഗ്യം",
          answerSubmittedDate: "2025-01-01",
          questionsWithInterimAndDelayedFinalAnswers: [
            {
              id: "13",
              questionNumber: 101,
            },
            {
              id: "14",
              questionNumber: 102,
            },
          ],
          questionsWithFinalDelayedAnswers: [
            {
              id: "123",
              questionNumber: 201,
            },
            {
              id: "124",
              questionNumber: 202,
            },
          ],
        },
        {
          id: "123",
          assembly: 15,
          session: 21,
          designationId: 100,
          designation: "Chief minister",
          designationInLocal: "മുഖ്യമന്ത്രി",
          answerSubmittedDate: "2025-02-02",
          questionsWithInterimAndDelayedFinalAnswers: [
            {
              id: "15",
              questionNumber: 103,
            },
            {
              id: "16",
              questionNumber: 104,
            },
          ],
          questionsWithFinalDelayedAnswers: [
            {
              id: "125",
              questionNumber: 203,
            },
            {
              id: "126",
              questionNumber: 204,
            },
          ],
        },
      ],
      createdAt: new Date().toISOString(),
      lastModifiedAt: new Date().toISOString(),
      createdBy: "System",
      lastModifiedBy: "System",
    };

    // Store in mock database
    mockDB[id] = mockResponse;

    return HttpResponse.json(mockResponse, { status: 201 });
  }),

  // GET handler - Retrieve stored document by ID
  http.get(
    BASE_URL + "api/documents-mockdab/:documentId",
    async ({ params }) => {
      const { documentId } = params;

      if (mockDB[documentId]) {
        return HttpResponse.json(mockDB[documentId], { status: 200 });
      } else {
        return HttpResponse.json(
          { error: "Document not found" },
          { status: 404 }
        );
      }
    }
  ),
];

export { delayedAnswerBulletinHandlers };
