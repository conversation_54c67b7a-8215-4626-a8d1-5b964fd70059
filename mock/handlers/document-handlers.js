import { http, HttpResponse } from "msw";
import { db } from "../db";
import {
  applyFilters,
  createSearchPredicate,
  paginateResults,
} from "../utilis/filters";

const { VITE_API_BASE_URL } = import.meta.env;
const BASE_URL = VITE_API_BASE_URL;

const documentsListHandlers = [
  http.get(BASE_URL + "api/documents", ({ request }) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1", 10);
    const pageSize = parseInt(url.searchParams.get("size") || "10", 10);
    const searchQuery = url.searchParams.get("search") || "";

    const allDocuments = db.documents.getAll();

    const searchFilteredDocuments = allDocuments.filter(
      createSearchPredicate(searchQuery)
    );

    const filteredDocuments = applyFilters(searchFilteredDocuments, url);

    return HttpResponse.json(
      paginateResults(filteredDocuments, page, pageSize)
    );
  }),
  http.patch(
    BASE_URL + "api/documents/:documentId",
    async ({ request, params }) => {
      const requestBody = await request.json();
      const { documentId } = params;
      const updatedDocument = db.documents.update({
        where: {
          id: {
            equals: documentId,
          },
        },
        data: {
          ...requestBody,
        },
      });

      return HttpResponse.json({ ...updatedDocument });
    }
  ),
];

export { documentsListHandlers };
