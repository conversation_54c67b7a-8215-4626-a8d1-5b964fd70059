import { http, HttpResponse } from "msw";

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

const sectionActionData = [
  {
content: [
  {
    id: "ab6a20ab-368d-47ee-a3cd-3d1849cf6348",
    noticeNumber: "1010",
    secondaryMembers: [
      {
        ministerId: 101,
        ministerDesignation: "Minister for Education",
        ministerDisplayName: "<PERSON><PERSON> <PERSON><PERSON>",
        ministerDisplayNameInLocal: "ശ്രീ. വി. ശിവൻകുട്ടി",
        portfolio: "Higher Education",
      },
      {
        ministerId: 102,
        ministerDesignation: "Minister for General Education",
        ministerDisplayName: "<PERSON><PERSON> <PERSON><PERSON>",
        ministerDisplayNameInLocal: "ശ്രീമതി. ആർ. ബിന്ദു",
        portfolio: "General Education",
      },
    ],
    starred: true,
    clubbed: true,
    noticeHeading: "Regarding the shortage of teachers in government higher secondary schools",
    ministerDesignation: "Minister for Education",
    portfolio: "Higher Education",
    questionDate: "04/23/2025",
    noticePriority: "P2",
    status: "TOBEASSIGNED",
    assembly: "15",
    session: "13",
  },
  {
    id: "ab6a20ab-368d-47ee-a3cd-3d1849cf6349",
    noticeNumber: "1009",
    secondaryMembers: [],
    starred: false,
    clubbed: false,
    noticeHeading: "Flood relief measures in coastal areas",
    ministerDesignation: "Chief Minister",
    portfolio: "Disaster management",
    questionDate: "04/01/2025",
    noticePriority: "P1",
    status: "TOBEASSIGNED",
    assembly: "15",
    session: "13",
  },
  {
    id: "9e0587aa-713e-4416-9086-8857b1245d37",
    noticeNumber: "1008",
    secondaryMembers: [
      {
        ministerId: 103,
        ministerDesignation:
          "Minister for Higher Education and Social Justice",
        ministerDisplayName: "Shri. R. Bindu",
        ministerDisplayNameInLocal: "ശ്രീമതി. ആർ. ബിന്ദു",
        portfolio: "Higher Education",
      },
      {
        ministerId: 104,
        ministerDesignation: "Minister for SC/ST Development",
        ministerDisplayName: "Shri. K. Radhakrishnan",
        ministerDisplayNameInLocal: "ശ്രീ. കെ. രാധാകൃഷ്ണൻ",
        portfolio: "SC/ST Development",
      },
    ],
    starred: true,
    clubbed: true,
    noticeHeading:
      "Scholarship provisions for SC/ST students in higher education",
    ministerDesignation: "Minister for Higher Education and Social Justice",
    portfolio: "Higher Education",
    questionDate: "03/18/2025",
    noticePriority: "P2",
    status: "TOBEASSIGNED",
    assembly: "15",
    session: "13",
  },
  {
    id: "9e0587aa-713e-4416-9086-8857b1245d38",
    noticeNumber: "1007",
    secondaryMembers: [],
    starred: true,
    clubbed: false,
    noticeHeading: "Irrigation projects in Palakkad district",
    ministerDesignation: "Minister for Water Resources",
    portfolio: "Irrigation",
    questionDate: "04/20/2025",
    noticePriority: "P1",
    status: "TOBEASSIGNED",
  },
  {
    id: "9e0587aa-713e-4416-9086-8857b1245d39",
    noticeNumber: "1006",
    secondaryMembers: [
      {
        ministerId: 105,
        ministerDesignation: "Minister for Law, Industries & Coir",
        ministerDisplayName: "Shri. P. Rajeev",
        ministerDisplayNameInLocal: "ശ്രീ. പി. രാജീവ്",
        portfolio: "Law",
      },
      {
        ministerId: 106,
        ministerDesignation: "Minister for Labour",
        ministerDisplayName: "Shri. V. Sivankutty",
        ministerDisplayNameInLocal: "ശ്രീ. വി. ശിവൻകുട്ടി",
        portfolio: "Labour",
      },
    ],
    starred: false,
    clubbed: true,
    noticeHeading: "Working conditions in coir industry",
    ministerDesignation: "Minister for Law, Industries & Coir",
    portfolio: "Coir",
    questionDate: "02/03/2025",
    noticePriority: "P2",
    status: "TOBEASSIGNED",
    assembly: "15",
    session: "14",
  },
  {
    id: "9e0587aa-713e-4416-9086-8857b1245d87",
    noticeNumber: "1005",
    secondaryMembers: [],
    starred: true,
    clubbed: false,
    noticeHeading: "Power outages in rural areas",
    ministerDesignation: "Minister for Electricity",
    portfolio: "Electricity",
    questionDate: "04/11/2025",
    noticePriority: "P1",
    status: "TOBEASSIGNED",
    assembly: "15",
    session: "13",
  },
  {
    id: "9e0587aa-713e-4416-9086-8857b1245d67",
    noticeNumber: "1004",
    secondaryMembers: [],
    starred: true,
    clubbed: false,
    noticeHeading: "Industrial growth in northern districts",
    ministerDesignation: "Minister for Industries",
    portfolio: "Industries",
    questionDate: "03/14/2025",
    noticePriority: "P1",
    status: "TOBEASSIGNED",
    assembly: "15",
    session: "13",
  },
  {
    id: "9e0587aa-713e-4416-9086-8857b1245d37",
    noticeNumber: "1003",
    secondaryMembers: [
      {
        ministerId: 107,
        ministerDesignation: "Minister for Electricity",
        ministerDisplayName: "Shri. K. Krishnankutty",
        ministerDisplayNameInLocal: "ശ്രീ. കെ. കൃഷ്ണൻകുട്ടി",
        portfolio: "Electricity",
      },
      {
        ministerId: 108,
        ministerDesignation: "Minister for Renewable Energy",
        ministerDisplayName: "Shri. K. Krishnankutty",
        ministerDisplayNameInLocal: "ശ്രീ. കെ. കൃഷ്ണൻകുട്ടി",
        portfolio: "Renewable Energy",
      },
    ],
    starred: false,
    clubbed: true,
    noticeHeading: "Implementation of solar energy projects",
    ministerDesignation: "Minister for Electricity",
    portfolio: "Electricity",
    questionDate: "04/24/2025",
    noticePriority: "P2",
    status: "TOBEASSIGNED",
    assembly: "15",
    session: "15",
  },
  {
    id: "4d2f9019-9a8c-4b20-a8ae-345e8b26a521",
    noticeNumber: "1002",
    secondaryMembers: [
      {
        ministerId: 109,
        ministerDesignation: "Minister for Transport",
        ministerDisplayName: "Shri. Antony Raju",
        ministerDisplayNameInLocal: "ശ്രീ. ആന്റണി രാജു",
        portfolio: "Road Transport",
      },
    ],
    starred: true,
    clubbed: true,
    noticeHeading: "Upgradation of rural bus services",
    ministerDesignation: "Minister for Transport",
    portfolio: "Road Transport",
    questionDate: "04/22/2025",
    noticePriority: "P2",
    status: "TOBEASSIGNED",
    assembly: "15",
    session: "13",
  },
  {
    id: "7f8d208f-0c92-4552-9012-6e87411b1c3c",
    noticeNumber: "1001",
    secondaryMembers: [],
    starred: true,
    clubbed: false,
    noticeHeading: "Environmental concerns near industrial zones",
    ministerDesignation: "Minister for Environment",
    portfolio: "Environment",
    questionDate: "03/30/2025",
    noticePriority: "P1",
    status: "TOBEASSIGNED",
    assembly: "15",
    session: "13",
  },
  {
    id: "8f0b9270-5c6f-4b2a-9019-17606b4dd61c",
    noticeNumber: "1011",
    secondaryMembers: [
      {
        ministerId: 110,
        ministerDesignation: "Minister for Fisheries",
        ministerDisplayName: "Shri. Saji Cherian",
        ministerDisplayNameInLocal: "ശ്രീ. സജി ചെറിയാൻ",
        portfolio: "Fisheries",
      },
    ],
    starred: false,
    clubbed: true,
    noticeHeading: "Safety measures for coastal fishermen",
    ministerDesignation: "Minister for Fisheries",
    portfolio: "Fisheries",
    questionDate: "04/19/2025",
    noticePriority: "P2",
    status: "TOBEASSIGNED",
    assembly: "15",
    session: "13",
  },
  {
    id: "9cbbfcf1-c578-4b10-8f82-b5fa3e91a9a0",
    noticeNumber: "1012",
    secondaryMembers: [],
    starred: true,
    clubbed: false,
    noticeHeading:
      "Increase in fuel prices and public transportation impact",
    ministerDesignation: "Minister for Finance",
    portfolio: "Finance",
    questionDate: "04/18/2025",
    noticePriority: "P1",
    status: "TOBEASSIGNED",
    assembly: "15",
    session: "13",
  },
  {
    id: "32f98ed6-87ae-47b1-8225-9aa0e4f8ea5e",
    noticeNumber: "1013",
    secondaryMembers: [],
    starred: false,
    clubbed: false,
    noticeHeading: "Promotion of local arts and crafts",
    ministerDesignation: "Minister for Culture",
    portfolio: "Culture",
    questionDate: "03/29/2025",
    noticePriority: "P2",
    status: "TOBEASSIGNED",
    assembly: "15",
    session: "13",
  },
],
},
];

const sectionStaffHandlers = [
  http.get(
    BASE_URL + "api/section-notice-for-questions",
    ({ request }) => {
      const url = new URL(request.url);

      const search = url.searchParams.get("search") || "";
      const searchText = url.searchParams.get("searchText") || "";
      const searchTerm = search || searchText;

      const ministerDesignation = url.searchParams.get("ministerDesignation") || "";
      const portfolio = url.searchParams.get("portfolio") || "";
      const status = url.searchParams.get("status") || "";
      const kla = url.searchParams.get("kla") || "";
      const session = url.searchParams.get("session") || "";
      const questionDateStartDate = url.searchParams.get("questionDateStartDate") || "";
      const questionDateEndDate = url.searchParams.get("questionDateEndDate") || "";
      const starred = url.searchParams.get("starred") || "";
      const clubbed = url.searchParams.get("clubbed") || "";
      const noticePriority = url.searchParams.get("noticePriority") || "";

      let page = parseInt(url.searchParams.get("page") || "1", 10);
      if (page === 0) page = 1;

      let pageSize = parseInt(url.searchParams.get("size") || "10", 10);

      if (![10, 50, 100].includes(pageSize)) {
        pageSize = 10;
      }

      let allDocuments = sectionActionData[0]?.content || [];

      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        allDocuments = allDocuments.filter(
          (doc) =>
            doc.noticeHeading?.toLowerCase().includes(searchLower) ||
            doc.ministerDesignation?.toLowerCase().includes(searchLower) ||
            doc.portfolio?.toLowerCase().includes(searchLower) ||
            doc.ministerPortfolio?.toLowerCase().includes(searchLower) ||
            doc.noticeNumber?.toString().includes(searchTerm)
        );
      }

      if (status) {
        const statuses = status.split(",");
        allDocuments = allDocuments.filter((doc) =>
          statuses.includes(doc.status)
        );
      }

      if (ministerDesignation) {
        const designations = ministerDesignation.split(",");
        allDocuments = allDocuments.filter((doc) =>
          designations.includes(doc.ministerDesignation)
        );
      }

      if (portfolio) {
        const portfolios = portfolio.split(",");
        allDocuments = allDocuments.filter(
          (doc) =>
            portfolios.includes(doc.portfolio) ||
            portfolios.includes(doc.ministerPortfolio)
        );
      }

      if (kla) {
        const klaValues = kla.split(",");
        allDocuments = allDocuments.filter((doc) =>
          klaValues.includes(doc.assembly)
        );
      }

      if (session) {
        const sessionValues = session.split(",");
        allDocuments = allDocuments.filter((doc) =>
          sessionValues.includes(doc.session)
        );
      }

      if (starred === "true") {
        allDocuments = allDocuments.filter((doc) => doc.starred === true);
      } else if (starred === "false") {
        allDocuments = allDocuments.filter((doc) => doc.starred === false);
      }

      if (clubbed === "true") {
        allDocuments = allDocuments.filter((doc) => doc.clubbed === true);
      } else if (clubbed === "false") {
        allDocuments = allDocuments.filter((doc) => doc.clubbed === false);
      }

      if (noticePriority) {
        const priorities = noticePriority.split(",");
        allDocuments = allDocuments.filter((doc) =>
          priorities.includes(doc.noticePriority)
        );
      }

      const isDateInRange = (dateStr, startDate, endDate) => {
        if (!dateStr) return false;
        if (!startDate && !endDate) return true;
        
        const parts = dateStr.split('/');
        if (parts.length !== 3) return false;
        
        const month = parseInt(parts[0]) - 1;
        const day = parseInt(parts[1]);
        let year = parseInt(parts[2]);
        
        if (year < 100) {
          year += year < 50 ? 2000 : 1900;
        }
        
        const date = new Date(year, month, day);
        
        let isAfterStart = true;
        let isBeforeEnd = true;
        
        
        if (startDate) {
          const [startYear, startMonth, startDay] = startDate.split('-').map(Number);
          const start = new Date(startYear, startMonth - 1, startDay);
          isAfterStart = date >= start;
        }
        
        if (endDate) {
          const [endYear, endMonth, endDay] = endDate.split('-').map(Number);
          const end = new Date(endYear, endMonth - 1, endDay);
          isBeforeEnd = date <= end;
        }
        
        return isAfterStart && isBeforeEnd;
      };
      
      if (questionDateStartDate || questionDateEndDate) {
        allDocuments = allDocuments.filter((doc) =>
          isDateInRange(doc.questionDate, questionDateStartDate, questionDateEndDate)
        );
      }

      const totalElements = allDocuments.length;
      const totalPages = Math.ceil(totalElements / pageSize);
      const startIndex = (page - 1) * pageSize;
      const paginatedDocuments = allDocuments.slice(
        startIndex,
        startIndex + pageSize
      );

      return HttpResponse.json({
        content: paginatedDocuments,
        totalElements: totalElements,
        totalPages: totalPages,
        pageSize: pageSize,
        pageNumber: page,
        first: page === 1,
        last: page >= totalPages,
        hasNext: page < totalPages,
        hasPrevious: page > 1,
        empty: paginatedDocuments.length === 0,
      });
    }
  ),
];

export { sectionStaffHandlers };
