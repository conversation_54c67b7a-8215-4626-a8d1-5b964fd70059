import { delay, http, HttpResponse } from "msw";
import { db } from "../db";
import { v4 as uuidv4 } from "uuid";
import uuid4 from "uuid4";
import { DelayList } from "./delay-list";
import { addDelayStatementList } from "../db/add-delay-list";

const { VITE_API_BASE_URL } = import.meta.env;
export const delayStatementHandlers = [
  http.post(
    VITE_API_BASE_URL + "documents-mocks/draft",
    async ({ request }) => {
      await delay(500);
      const body = await request.json();

      const now = new Date().toISOString();

      const newDelayStatementList = {
        id: uuidv4(),
        type: "DELAY_STATEMENT_LIST",
        name: body.name,
        status: "DRAFT",
        currentNumber: "1", // Assuming default for mock
        assembly: body.assembly,
        session: body.session,
        listNumber: Math.floor(Math.random() * 100), // Mock list number
        dateToBeLaidInAssembly: "2025-01-01", // Placeholder date

        ministerWiseLayingDocuments: [],

        createdAt: now,
        lastModifiedAt: now,
        createdBy: "admin",
        lastModifiedBy: "admin",
      };

      db.delayStatementLists.create(newDelayStatementList);

      return HttpResponse.json(newDelayStatementList);
    }
  ),
  http.patch(
    VITE_API_BASE_URL + "api/documents-mocks/draft",
    async ({ request }) => {
      await delay(500);

      const body = await request.json();

      const existing = db.delayStatementLists?.findFirst({
        where: {
          listNumber: { equals: body.listNumber },
          type: { equals: "DELAY_STATEMENT_LIST" },
        },
      });

      const updated = db.delayStatementLists.update({
        where: {
          listNumber: { equals: body.listNumber },
          type: { equals: "DELAY_STATEMENT_LIST" },
        },
        data: {
          listNumber: body.listNumber ?? existing.listNumber,
          dateToBeLaidInAssembly:
            body.dateToBeLaidInAssembly ?? existing.dateToBeLaidInAssembly,
          lastModifiedAt: new Date().toISOString(),
        },
      });

      return HttpResponse.json(updated);
    }
  ),

  http.get(VITE_API_BASE_URL + "api/documents-mocks/:id", ({ params }) => {
    const { id } = params;
    const record = DelayList.find((item) => item.id === id);

    if (!record) {
      return HttpResponse.json(
        { error: "Document not found" },
        { status: 404 }
      );
    }

    return HttpResponse.json(record);
  }),

  // Get Delay Statement List Refresh
  http.get(
    VITE_API_BASE_URL + "api/delay-statement-list/refresh",
    async ({ request }) => {
      await delay(500);

      const url = new URL(request.url);
      const page = parseInt(url.searchParams.get("page") || "0", 10);
      const size = parseInt(url.searchParams.get("size") || "10", 10);

      const allEntries = addDelayStatementList;

      const paginated = allEntries.slice(page * size, (page + 1) * size);

      const response = {
        content: paginated.map((entry) => ({
          assembly: entry.assembly,
          session: entry.session,
          questionNumber: entry.questionNumber,
          noticeForQuestionTitle: entry.noticeForQuestionTitle,
          delayStatementId: entry.delayStatementId,
          delayStatementDocuments: entry.delayStatementDocuments,
        })),
        page,
        size,
        totalElements: allEntries.length,
        totalPages: Math.ceil(allEntries.length / size),
        first: page === 0,
        last: (page + 1) * size >= allEntries.length,
      };

      return HttpResponse.json(response);
    }
  ),

  // Upload Delay Statement Document
  http.post(
    VITE_API_BASE_URL + "delay-statements/upload",
    async ({ request }) => {
      await delay(500);

      const formData = await request.formData();
      const file = formData.get("file");
      const name = formData.get("name");
      const contentType = formData.get("contentType");
      const delayStatementId = formData.get("delayStatementId");

      if (!file || !name || !contentType || !delayStatementId) {
        return new HttpResponse(
          JSON.stringify({ message: "Missing required fields" }),
          { status: 400 }
        );
      }

      const fileRecord = {
        id: uuid4(),
        delayStatementId,
        name,
        contentType,
        fileURL: `https://mock-storage.service/uploads/${name}`, // Simulated URL
      };

      db.delayStatementLists.create(fileRecord);

      return HttpResponse.json(fileRecord);
    }
  ),
  // Add item to Delay Statement List
  http.post(
    VITE_API_BASE_URL + "api/delay-statements/:id",
    async ({ request }) => {
      await delay(500);

      const body = await request.json();
      const { assembly, session, questionNumber, noticeForQuestionTitle } =
        body;

      if (!assembly || !session || !questionNumber || !noticeForQuestionTitle) {
        return new HttpResponse(
          JSON.stringify({ message: "Missing required fields" }),
          { status: 400 }
        );
      }

      const newEntry = {
        id: uuid4(),
        assembly,
        session,
        questionNumber,
        noticeForQuestionTitle,
        delayStatementDocuments: [], // Initially empty
      };

      db.delayStatementLists.create(newEntry);

      return HttpResponse.json(newEntry);
    }
  ),
  // Remove entry from Delay Statement List
  http.post(
    VITE_API_BASE_URL +
      "api/delay-statement-list/:documentId/delay-statement/:id/remove",
    async ({ request, params }) => {
      await delay(500);

      const { documentId, id: entryId } = params;
      const body = await request.json();
      const { reason } = body;

      if (!reason) {
        return new HttpResponse(
          JSON.stringify({ message: "Removal reason is required" }),
          { status: 400 }
        );
      }

      const recordIndex = DelayList.findIndex((item) => item.id === entryId);

      if (recordIndex === -1) {
        return new HttpResponse(null, {
          status: 404,
          statusText: "Entry not found",
        });
      }

      DelayList.splice(recordIndex, 1);

      return HttpResponse.json({
        message: `Entry ${entryId} removed from document ${documentId} for reason: ${reason}`,
      });
    }
  ),
  // Reorder entries in Delay Statement List
  http.post(
    VITE_API_BASE_URL + "api/delay-statement-list/:documentId",
    async ({ request }) => {
      await delay(500);

      // const body = await request.json();
      const { ministerWiseLayingListId, delayStatementEntries } =
        await request.json();

      if (!ministerWiseLayingListId || !Array.isArray(delayStatementEntries)) {
        return new HttpResponse(
          JSON.stringify({ message: "Invalid request data" }),
          { status: 400 }
        );
      }
      const delayList = DelayList.find((list) =>
        list.ministerWiseLayingDocuments.some(
          (doc) => doc.id === ministerWiseLayingListId
        )
      );

      if (!delayList) {
        return new HttpResponse(
          JSON.stringify({
            message: "Minister-wise laying document not found",
          }),
          { status: 404 }
        );
      }

      const layingDoc = delayList.ministerWiseLayingDocuments.find(
        (doc) => doc.id === ministerWiseLayingListId
      );

      layingDoc.delayStatementEntries = layingDoc.delayStatementEntries.map(
        (entry) => {
          const matched = delayStatementEntries.find((e) => e.id === entry.id);
          return matched ? { ...entry, order: matched.order } : entry;
        }
      );

      layingDoc.delayStatementEntries.sort((a, b) => a.order - b.order);

      return HttpResponse.json({
        ministerWiseLayingListId,
        delayStatementEntries: layingDoc.delayStatementEntries,
      });
    }
  ),
];
