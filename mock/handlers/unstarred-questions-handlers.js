// mocks/handlers/unstarred-question-handlers.js
import { http, HttpResponse } from "msw";
import { mock_unstarred_approved, mock_unstarred_pending } from "./constant";
import { formatedDate } from "@/utils";

const { VITE_API_BASE_URL } = import.meta.env;
const BASE_URL = VITE_API_BASE_URL;

export const unstarredQuestionHandlers = [
  // Get unstarred questions by date
  http.get(BASE_URL + "api/documents-mockUnstarred", async ({ request }) => {
    const url = new URL(request.url);
    const date = url.searchParams.get("date");

    const today = formatedDate(new Date(), "yyyy-MM-dd");
    const yesterday = formatedDate(
      new Date(Date.now() - 86400000),
      "yyyy-MM-dd"
    ); // 86400000 ms = 1 day

    console.log("date", date);
    console.log("today", today);

    let responseData;

    if (formatedDate(date, "yyyy-MM-dd") === today) {
      responseData = {
        ...mock_unstarred_pending,
        id: date,
      };
    } else if (formatedDate(date, "yyyy-MM-dd") === yesterday) {
      responseData = {
        ...mock_unstarred_approved,
        id: date,
      };
    } else {
      responseData = {
        message: "No data available for this date.",
        id: date,
      };
    }

    return HttpResponse.json(responseData);
  }),
];
