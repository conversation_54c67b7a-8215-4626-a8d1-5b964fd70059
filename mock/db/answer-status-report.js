import { primaryKey } from "@mswjs/data";
import uuid4 from "uuid4";

const answerStatusReportDbModel = {
  id: primaryKey(uuid4), 
  type: () => "ANSWER_STATUS_REPORT",
  name: String, 
  assembly: Number,
  session: Number, 
  status: () => "DRAFT",
  currentNumber: () => "123",
  createdAt: () => new Date().toISOString(),
  lastModifiedAt: () => new Date().toISOString(),
  createdBy: () => "System",
  lastModifiedBy: () => "System",
};

export { answerStatusReportDbModel };