import { factory } from "@mswjs/data";
import { ballotsDbModel } from "./ballots";
import {
  allotmentOfDaysDbModel,
  seedAllotmentOfDaysDb,
} from "./allotment-db-model";
import { noticeDbModel } from "./notice-db-model";
import { memberDbModel } from "./consent-members-db-model";
import { answerStatusReportDbModel } from "./answer-status-report";
import { delayStatementDbModel } from "./delay-statement-db-model";

function seededDb() {
  const dbFactory = factory({
    ballots: ballotsDbModel,
    allotmentOfDays: allotmentOfDaysDbModel.allotmentOfDays,
    calendarOfSittings: allotmentOfDaysDbModel.calendarOfSittings,
    ministerDesignationGroup: allotmentOfDaysDbModel.ministerDesignationGroup,
    document: allotmentOfDaysDbModel.document,
    notice: noticeDbModel,
    member: memberDbModel,
    answer: answerStatusReportDbModel,
    // delayStatement: delayStatementDbModel.delayStatementLists,
    ...delayStatementDbModel,
  });

  for (let i = 0; i < 100; i++) {
    dbFactory.ballots.create();
  }

  for (let i = 0; i < 6; i++) {
    dbFactory.member.create();
  }
  // dbFactory.delayStatement.create();

  // Seed the allotment of days data
  seedAllotmentOfDaysDb(dbFactory);

  return dbFactory;
}

const db = seededDb();
export { db };
