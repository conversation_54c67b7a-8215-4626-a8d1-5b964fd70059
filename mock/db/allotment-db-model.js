import { primaryKey } from "@mswjs/data";

// Simple ID generation functions
const generateId = (() => {
  let nextId = 1;
  return () => nextId++;
})();

// Database model for MSW data
export const allotmentOfDaysDbModel = {
  // Document model
  document: {
    id: primaryKey(String),
    name: String,
    type: String,
    status: String,
    assembly: Number,
    session: Number,
    currentNumber: String,
    createdAt: String,
    createdBy: String,
    lastModifiedAt: String,
    lastModifiedBy: String,
  },

  // Allotment of Days model
  allotmentOfDays: {
    id: primaryKey(String),
    name: String,
    type: String,
    status: String,
    assembly: Number,
    session: Number,
    currentNumber: String,
    referredCalendarOfSittingsId: String,
    referredMinisterDesignationGroupId: String,
    allotments: Array,
    createdAt: String,
    createdBy: String,
    lastModifiedAt: String,
    lastModifiedBy: String,
  },

  // Calendar of Sittings model
  calendarOfSittings: {
    id: primaryKey(String),
    name: String,
    startDate: String,
    endDate: String,
    sittingDates: Array,
    assembly: Number,
    session: Number,
    status: String,
  },

  // Minister Designation Group model
  ministerDesignationGroup: {
    id: primaryKey(String),
    name: String,
    members: Array,
  },
};

// Function to seed the mock database with initial data
export const seedAllotmentOfDaysDb = (db) => {
  // Seed minister designation groups
  const ministerGroups = [
    {
      id: String(generateId()),
      name: "Group 1",
      members: [
        {
          id: String(generateId()),
          name: "Chief Minister",
          person: "Shri. Pinarayi Vijayan",
        },
        {
          id: String(generateId()),
          name: "Minister for Revenue and Housing",
          person: "Shri. K. Rajan",
        },
        {
          id: String(generateId()),
          name: "Minister for Water Resources",
          person: "Shri. Roshy Augustine",
        },
        {
          id: String(generateId()),
          name: "Minister for Electricity",
          person: "Shri. K. Krishnankutty",
        },
      ],
    },
    {
      id: String(generateId()),
      name: "Group 2",
      members: [
        {
          id: String(generateId()),
          name: "Minister for Forests, Wild life protection",
          person: "Shri. A. K. Saseendran",
        },
        {
          id: String(generateId()),
          name: "Minister for Sports, Wakf and Haj Pilgrimage, Posts and Telegraphs, Railways",
          person: "Shri. V. Abdurahiman",
        },
        {
          id: String(generateId()),
          name: "Minister for Transport",
          person: "Shri. K. B. Ganesh Kumar",
        },
        {
          id: String(generateId()),
          name: "Minister for Revenue and Housing",
          person: "Shri Ramachandran Kadannappalli",
        },
      ],
    },
    {
      id: String(generateId()),
      name: "Group 3",
      members: [
        {
          id: String(generateId()),
          name: "Minister for Food and Civil Supplies",
          person: "Adv. G. R. Anil",
        },
        {
          id: String(generateId()),
          name: "Minister for Finance",
          person: "Shri. K. N. Balagopal",
        },
        {
          id: String(generateId()),
          name: "Minister for Higher Education and Social Justice",
          person: "Dr. R. Bindu",
        },
        {
          id: String(generateId()),
          name: "Minister for Animal Husbandry and Dairy Development",
          person: "Smt. J. Chinchurani",
        },
      ],
    },
    {
      id: String(generateId()),
      name: "Group 4",
      members: [
        {
          id: String(generateId()),
          name: "Minister for Local Self Government, Excise & Parliamentary Affairs",
          person: "Shri. M. B. Rajesh",
        },
        {
          id: String(generateId()),
          name: "Minister for Public Works and Tourism",
          person: "Adv. P. A. Mohamed Riyas",
        },
      ],
    },
    {
      id: String(generateId()),
      name: "Group 5",
      members: [
        {
          id: String(generateId()),
          name: "Minister for Law, Industries and Coir",
          person: "Shri. P. Rajeeve",
        },
        {
          id: String(generateId()),
          name: "Minister for Health and Woman and Child Development",
          person: "Smt. Veena George",
        },
        {
          id: String(generateId()),
          name: "Minister for Agriculture",
          person: "Shri. P. Prasad",
        },
      ],
    },
    {
      id: String(generateId()),
      name: "Group 6",
      members: [
        {
          id: String(generateId()),
          name: "Minister for Fisheries and Cultural affairs",
          person: "Smt. Saji Cherian",
        },
        {
          id: String(generateId()),
          name: "Minister for General Education and Labour",
          person: "Shri. V. Sivankutty",
        },
        {
          id: String(generateId()),
          name: "Minister for Co-operation, Ports, and Devaswoms",
          person: "Shri. V. N. Vasavan",
        },
        {
          id: String(generateId()),
          name: "Minister for Welfare of Scheduled Castes, Scheduled Tribes",
          person: "Shri. O.R. Kelu",
        },
      ],
    },
  ];

  ministerGroups.forEach((group) => {
    db.ministerDesignationGroup.create(group);
  });

  // Seed calendar of sittings
  const calendarId = String(generateId());
  const sittingDates = [
    "2024-10-02",
    "2024-10-03",
    "2024-10-04",
    "2024-10-05",
    "2024-10-06",
    "2024-10-07",
    "2024-10-08",
    "2024-10-09",
    "2024-10-10",
    "2024-10-11",
    "2024-10-12",
    "2024-10-13",
    "2024-10-19",
    "2024-10-20",
    "2024-10-21",
    "2024-10-22",
    "2024-10-24",
    "2024-10-27",
    "2024-10-28",
    "2024-10-29",
    "2024-10-30",
    "2024-11-01",
    "2024-11-02",
    "2024-11-03",
    "2024-11-07",
    "2024-11-08",
    "2024-11-09",
    "2024-11-10",
    "2024-11-11",
    "2024-11-12",
    "2024-11-13",
    "2024-11-14",
    "2024-11-15",
    "2024-11-16",
    "2024-11-17",
    "2024-11-18",
    "2024-11-19",
    "2024-12-03",
    "2024-12-04",
    "2024-12-05",
    "2024-12-06",
    "2024-12-07",
  ];

  db.calendarOfSittings.create({
    id: calendarId,
    name: "Calendar of Sittings - December 2024",
    startDate: "2024-10-01",
    endDate: "2024-12-15",
    sittingDates,
    assembly: 15,
    session: 13,
    status: "APPROVED",
  });

  // Seed document
  const documentId = "1"; // Simple document ID for easy access
  db.document.create({
    id: documentId,
    name: "Allotment of Days December 2024",
    type: "ALLOTMENT_OF_DAYS",
    status: "DRAFT",
    assembly: 15,
    session: 13,
    currentNumber: "234",
    createdAt: "2024-10-10T10:30:00Z",
    createdBy: "Question Section Assistant",
    lastModifiedAt: "2024-10-10T10:30:00Z",
    lastModifiedBy: "Question Section Assistant",
  });

  // Seed allotment of days
  db.allotmentOfDays.create({
    id: documentId,
    name: "Allotment of Days December 2024",
    type: "ALLOTMENT_OF_DAYS",
    status: "DRAFT",
    assembly: 15,
    session: 13,
    currentNumber: "234",
    referredCalendarOfSittingsId: calendarId,
    referredMinisterDesignationGroupId: ministerGroups[0].id,
    allotments: [
      {
        id: String(generateId()),
        groupId: ministerGroups[0].id,
        groupName: ministerGroups[0].name,
        ministers: ministerGroups[0].members,
        dates: [
          "2/10/2024",
          "2/10/2024",
          "8/10/2024",
          "19/10/2024",
          "27/10/2024",
          "2/11/2024",
          "8/11/2024",
          "14/11/2024",
          "2/10/2024",
        ],
      },
      {
        id: String(generateId()),
        groupId: ministerGroups[1].id,
        groupName: ministerGroups[1].name,
        ministers: ministerGroups[1].members,
        dates: [
          "3/10/2024",
          "9/10/2024",
          "20/10/2024",
          "28/10/2024",
          "3/11/2024",
          "9/11/2024",
          "15/11/2024",
          "3/12/2024",
        ],
      },
      {
        id: String(generateId()),
        groupId: ministerGroups[2].id,
        groupName: ministerGroups[2].name,
        ministers: ministerGroups[2].members,
        dates: [
          "4/10/2024",
          "10/10/2024",
          "21/10/2024",
          "29/10/2024",
          "4/11/2024",
          "10/11/2024",
          "16/11/2024",
          "4/12/2024",
        ],
      },
      {
        id: String(generateId()),
        groupId: ministerGroups[3].id,
        groupName: ministerGroups[3].name,
        ministers: ministerGroups[3].members,
        dates: [
          "5/10/2024",
          "11/10/2024",
          "22/10/2024",
          "30/10/2024",
          "5/11/2024",
          "11/11/2024",
          "17/11/2024",
          "5/10/2024",
        ],
      },
      {
        id: String(generateId()),
        groupId: ministerGroups[4].id,
        groupName: ministerGroups[4].name,
        ministers: ministerGroups[4].members,
        dates: [
          "6/10/2024",
          "12/10/2024",
          "23/10/2024",
          "1/11/2024",
          "6/11/2024",
          "12/11/2024",
          "18/11/2024",
          "6/12/2024",
        ],
      },
      {
        id: String(generateId()),
        groupId: ministerGroups[5].id,
        groupName: ministerGroups[5].name,
        ministers: ministerGroups[5].members,
        dates: [
          "7/10/2024",
          "13/10/2024",
          "24/10/2024",
          "2/11/2024",
          "7/11/2024",
          "13/11/2024",
          "19/11/2024",
          "7/12/2024",
        ],
      },
    ],
    createdAt: "2024-10-10T10:30:00Z",
    createdBy: "Question Section Assistant",
    lastModifiedAt: "2024-10-10T10:30:00Z",
    lastModifiedBy: "Question Section Assistant",
  });
};
