import { primaryKey, oneOf } from "@mswjs/data";
import uuid4 from "uuid4";

export const delayStatementDbModel = {
  delayStatementLists: {
    id: primaryKey(() => uuid4()),
    kla: String,
    session: String,
    listNumber: String,
    dateToBeLaidInAssembly: String,
    lastModifiedAt: String,
    type: String,
    name: String,
    status: String,
    currentNumber: String,
    assembly: Number,
    createdAt: String,
    createdBy: String,
    lastModifiedBy: String,
    ministerWiseLayingDocuments: oneOf("ministerWiseLayingDocuments"),
  },
  ministerWiseLayingDocuments: {
    id: primaryKey(() => uuid4()),
    ministerId: Number,
    ministerDisplayName: String,
    ministerDisplayNameInLocal: String,
    ministerSubject: String,
    ministerSubSubject: String,
    questionDate: String,
    designationId: String,
    designation: String,
    designationInLocal: String,
    assembly: Number,
    session: Number,
    nameofMember: String,
    delayStatementEntries: oneOf("delayStatementEntries", { many: true }),
  },
  delayStatementEntries: {
    id: primaryKey(() => uuid4()),
    order: Number,
    questionNumber: Number,
    noticeForQuestionTitle: String,
    delayStatementId: String,
    delayStatementDocuments: Array,
  },
  delayStatementRefreshList: {
    id: primaryKey(() => uuid4()),
    assembly: Number,
    session: Number,
    questionNumber: Number,
    noticeForQuestionTitle: String,
    delayStatementId: String,
    delayStatementDocuments: Array,
    removed: Boolean,
    removedReason: String,
    removedAt: String,
  },
  delayStatementRefreshPagination: {
    id: primaryKey(() => uuid4()),
    page: Number,
    size: Number,
    totalElements: Number,
    totalPages: Number,
    first: Boolean,
    last: Boolean,
    content: Array,
  },
};
