import { primary<PERSON>ey } from "@mswjs/data";
import uuid4 from "uuid4";

const constituencyGroups = {
  urban: [
    { name: "Metro District", local: "മെട്രോ ജില്ല" },
    { name: "Capital Region", local: "മേധാവി പ്രദേശം" },
    { name: "Downtown Zone", local: "ടൗൺ സെന്റർ മേഖല" },
  ],
  rural: [
    { name: "Hillside County", local: "ഹിൽസൈഡ് കൗണ്ടി" },
    { name: "Agricultural Belt", local: "കൃഷി മേഖലം" },
    { name: "Lakeshore Region", local: "കായലോര പ്രദേശം" },
  ],
};

const politicalPartyGroups = {
  democraticFront: [
    { name: "Democratic Alliance", local: "ഡെമോക്രാറ്റിക് ആലൈൻസ്" },
    { name: "United Democratic Front", local: "യുണൈറ്റഡ് ഡെമോക്രാറ്റിക് ഫ്രണ്ട്" },
    { name: "People's Coalition", local: "പീപ്പിൾസ് കോളിഷൻ" },
  ],
  progressiveUnion: [
    { name: "Progressive Movement", local: "പ്രോഗ്രസ്സീവ് മൂവ്മെന്റ്" },
    { name: "New Progressive Party", local: "ന്യൂ പ്രോഗ്രസ്സീവ് പാർട്ടി" },
    { name: "Forward Alliance", local: "ഫോർവേഡ് ആലൈൻസ്" },
  ],
};

const memberNames = [
  { name: "<PERSON>", local: "ജോൺ ഡോ" },
  { name: "<PERSON>", local: "അലീസ് മാത്യൂസ്" },
  { name: "<PERSON>", local: "രവി കുമാർ" },
  { name: "<PERSON><PERSON>", local: "ഫാത്തിമ നൂർ" },
  { name: "<PERSON>", local: "മൈക്കൽ ഫെർണാണ്ടസ്" },
];

const memberDbModel = {
  id: primaryKey(() => uuid4()),
  memberId: () => Math.floor(Math.random() * 10000),
  memberDisplayName: () => {
    const index = Math.floor(Math.random() * memberNames.length);
    return memberNames[index].name;
  },
  memberDisplayNameInLocal: () => {
    const index = Math.floor(Math.random() * memberNames.length);
    return memberNames[index].local;
  },
  constituencyId: () => Math.floor(Math.random() * 1000),
  constituencyName: () => {
    const group = Math.random() > 0.5 ? "urban" : "rural";
    const index = Math.floor(Math.random() * constituencyGroups[group].length);
    return constituencyGroups[group][index].name;
  },
  constituencyNameInLocal: () => {
    const group = Math.random() > 0.5 ? "urban" : "rural";
    const index = Math.floor(Math.random() * constituencyGroups[group].length);
    return constituencyGroups[group][index].local;
  },
  politicalPartyId: () => Math.floor(Math.random() * 100),
  politicalPartyName: () => {
    const group = Math.random() > 0.5 ? "democraticFront" : "progressiveUnion";
    const index = Math.floor(Math.random() * politicalPartyGroups[group].length);
    return politicalPartyGroups[group][index].name;
  },
  politicalPartyNameInLocal: () => {
    const group = Math.random() > 0.5 ? "democraticFront" : "progressiveUnion";
    const index = Math.floor(Math.random() * politicalPartyGroups[group].length);
    return politicalPartyGroups[group][index].local;
  },
};

export { memberDbModel };
