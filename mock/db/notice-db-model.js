import { primaryKey } from "@mswjs/data";
import uuid4 from "uuid4";

const noticeDbModel = {
  id: primaryKey(() => uuid4()),
  name: () => "Sample Document",
  type: () => "QuestionNotice",
  status: () => "Draft",
  currentNumber: () => "Q-2025-001",
  assembly: () => 16,
  session: () => 3,
  serialNumber: () => 1001,
  createdAt: () => new Date().toISOString(),
  lastModifiedAt: () => new Date().toISOString(), // TODO
  createdBy: () => "user123", // TODO
  lastModifiedBy: () => "user123", // TODO
  noticePriority: () => "P1",
  starred: () => true,
  noticeHeading: () => "NOTICE FOR QUESTION",
  clauses: () => [],
};

export { noticeDbModel };
