import { primaryKey } from "@mswjs/data";

const generateSerialNo = (() => {
  let serialNo = 123456;
  return () => `${serialNo++}`;
})();

const generateId = (() => {
  let id = 1;
  return () => `${id++}`;
})();

let ballotCounter = 1;

const generateBallotHeading = () => {
  const heading = `Balloting ${ballotCounter} December 2024`;
  ballotCounter = (ballotCounter % 30) + 1;
  return heading;
};

const generateStatus = () => {
  const statusList = ["submitted", "cancelled"];
  return statusList[Math.floor(Math.random() * statusList.length)];
};

const generateRandomDate = () => {
  return new Date(new Date() - Math.random() * 1e12).toISOString();
};

const ministerGroups = ["Group 1", "Group 2", "Group 3", "Group 4", "Group 5"];

const getRandomMinisterGroup = () => {
  return ministerGroups[Math.floor(Math.random() * ministerGroups.length)];
};

const ballotsDbModel = {
  currentNumber: generateSerialNo,
  id: primaryKey(generateId),
  createdAt: generateRandomDate,
  lastModifiedAt: generateRandomDate,
  ballotHeading: generateBallotHeading,
  referenceMinisterDesignationGroupId: () =>
    `uuid-${Math.random().toString(36).substr(2, 9)}`, // Generate UUID-like string
  referenceMinisterDesignationGroupName: getRandomMinisterGroup,
  referenceMinisterDesignationGroupNameInLocal: getRandomMinisterGroup,
  questionDate: generateRandomDate,
  assembly: () => "15",
  session: () => "1",
  status: generateStatus,
  ballotStatus: () => "ACTIVE",
  createdBy: () => "user1",
  lastModifiedBy: () => "user2",
  ballotEntries: () => [
    {
      id: `uuid-${Math.random().toString(36).substr(2, 9)}`,
      ballotOrder: Math.floor(Math.random() * 100) + 1,
      memberId: Math.floor(Math.random() * 1000) + 1,
      memberDisplayNameInLocal: "Local Member Name",
      constituency: "Random Constituency",
      constituencyInLocal: "Local Constituency",
      politicalPartyId: Math.floor(Math.random() * 100) + 1,
      politicalPartyName: "Party Name",
      politicalPartyNameInLocal: "Party Name Local",
    },
  ],
};

export { ballotsDbModel };
