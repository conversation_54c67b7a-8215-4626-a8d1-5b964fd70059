# API Integration Guide

## Overview
This document explains the integration of real API endpoints in the Question Section App, replacing the previous mock handlers.

## API Services

The following API services have been implemented:

1. **Notice Service (`src/services/notice.js`)**
   - General notices API handling

2. **Question Notice Service (`src/services/question-notice.js`)**
   - API for questions and related notices

3. **Half an Hour Discussion Service (`src/services/half-an-hour-discussion.js`)**
   - API for half-hour discussion notices

4. **Notice for Question to Private Members Service (`src/services/notice-for-question-to-private-members.js`)**
   - API for private member question notices

5. **Notice for Short Notice Service (`src/services/notice-for-short-notice.js`)**
   - API for short notices

## How to Use

### Enabling/Disabling Mocks

The application now supports a hybrid approach, where real APIs are used for notice-related endpoints, while mock handlers can still be used for other endpoints that haven't been migrated yet.

To control this behavior, modify the `.env.local` file:

```
# Set to "true" to enable mock service worker for APIs that haven't been migrated yet
# Set to "false" to use real API calls for everything
VITE_ENABLE_MOCKS=true
```

### Development Workflow

1. For notice-related features, the application now uses real API endpoints instead of mock handlers
2. For other features, you can continue to use mock handlers by setting `VITE_ENABLE_MOCKS=true`
3. As more API endpoints are integrated, you can gradually remove mock handlers and add proper API services

### Cleaning Up Mock Handlers

A cleanup script is provided to remove unused mock handlers:

```bash
# Make the script executable
chmod +x cleanup-handlers.sh

# Run the script
./cleanup-handlers.sh
```

## API Services Structure

Each API service follows the same pattern:

1. Uses Redux Toolkit Query (RTK Query) for API management
2. Defines endpoints with proper tag invalidation for caching
3. Uses ES6 spread syntax for clean parameter handling
4. Exports hooks that can be directly used in components

Example of a typical API service:

```javascript
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

export const someApi = createApi({
  reducerPath: "someApiName",
  baseQuery: fetchBaseQuery({
    baseUrl: import.meta.env.VITE_API_BASE_URL + "api/",
  }),
  tagTypes: ["TagName"],
  endpoints: (builder) => ({
    getData: builder.query({
      query: ({ param1, param2 }) => {
        const params = {
          ...(param1 && { param1 }),
          ...(param2 && { param2 }),
        };
        
        return { url: `endpoint-path`, params };
      },
      providesTags: ["TagName"],
    }),
  }),
});

export const { useGetDataQuery } = someApi;
```

## Components Integration

Components that previously used mock data now use the proper API hooks. For example:

```jsx
import { useGetDataQuery, useSaveDataMutation } from "@/services/some-api";

function MyComponent() {
  const { data, isLoading } = useGetDataQuery(params);
  const [saveData, { isLoading: isSaving }] = useSaveDataMutation();
  
  // Component logic...
}
```
