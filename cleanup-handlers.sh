#!/bin/bash

# Define the directory path
MOCK_HANDLERS_DIR="/Users/<USER>/Developer/kla/question/question-section-app/mock/handlers"

# List of mock handler files to delete
FILES_TO_DELETE=(
  "notice-bank-handlers.js"
  "notice-for-question-handlers.js"
  "notice-handlers.js"
  "othernotices-mynotice-handlers.js"
  "othernotices-noticebank-handlers.js"
  "explantory-note-handlers.js"
)

# Delete each file
for file in "${FILES_TO_DELETE[@]}"; do
  if [ -f "$MOCK_HANDLERS_DIR/$file" ]; then
    rm "$MOCK_HANDLERS_DIR/$file"
    echo "Deleted: $file"
  else
    echo "File not found: $file"
  fi
done

echo "Cleanup completed!"
