# Question Section App

This is a React application created using Vite. This README will guide you through setting up and running the project.

## Table of Contents

1. [Requirements](#requirements)
1. [Installation](#installation)
1. [Environment Configuration](#environment-configuration)
1. [Development](#development)
1. [Building](#building)
1. [Running Tests](#running-tests)
1. [Linting](#linting)
1. [Formatting](#formatting)

## Requirements

- [Node.js](https://nodejs.org) (LTS version as specified in .nvmrc)
- [pnpm](https://pnpm.io/) package manager

## Installation

To get started with, follow these steps:

1. **Clone the repository:**

   ```bash
   git clone https://git.ults.in/kla-v2/question-section-app.git
   ```

2. **Navigate to the project directory:**

   ```bash
   cd question-section-app
   ```

3. **Install dependencies:**

   ```bash
   pnpm install
   ```

## Environment Configuration

The application uses environment files to configure API endpoints and other environment-specific settings:

- `.env` - Default environment variables (loaded in all cases)
- `.env.development` - Development environment variables (loaded when running in development mode)
- `.env.localhost` - Local development environment variables (loaded when running in localhost mode)
- `.env.local` - Local overrides (loaded in all cases, but ignored by git)

These files contain the following environment variables:

- `VITE_API_BASE_URL` - Base URL for the question service API
- `VITE_MDM_SERVICE_BASE_URL` - Base URL for the MDM service API

## Development

The application supports different environment configurations:

### Development Server

#### Development Environment (Default)

To start the development server with development environment settings:

```bash
pnpm run dev
```

This uses the `.env` and `.env.development` files and connects to the development services at ************.

#### Local Development

To start the development server with local environment settings:

```bash
pnpm run dev:local
```

This uses the `.env` and `.env.localhost` files and connects to the local services at localhost:8080.

The development server will start and you can view the app in the browser at `http://localhost:5173` (or another port if configured).

## Building

To create a build of the application:

### Development Build

```bash
pnpm run build
```

This creates a build using the development environment settings (`.env` and `.env.development`).

### Local Build

```bash
pnpm run build:local
```

This creates a build using the local environment settings (`.env` and `.env.localhost`).

### Production Build

```bash
pnpm run build:prod
```

This creates a build using production environment settings (`.env` and `.env.production`).

The build files will be generated in the `dist` directory.

To preview the build:

```bash
pnpm run preview
```

## Running Tests

To run tests for the project:

```bash
pnpm test
```

## Linting

To check for code quality and style issues:

```bash
pnpm run lint
```

## Formatting

To format the code:

```bash
pnpm run format
```
