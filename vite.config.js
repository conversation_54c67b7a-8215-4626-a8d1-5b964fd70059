import react from "@vitejs/plugin-react-swc";
import { dirname, resolve } from "path";
import { fileURLToPath } from "url";
import { defineConfig, loadEnv } from "vite";
import { coverageConfigDefaults } from "vitest/config";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current directory
  // The third parameter '' means load all env vars, not just ones prefixed with VITE_
  // eslint-disable-next-line no-undef
  const env = loadEnv(mode, process.cwd(), '');

  // Log the mode and environment variables
  console.log(`Running in mode: ${mode}`);
  console.log(`Environment variables loaded:`, {
    VITE_API_BASE_URL: env.VITE_API_BASE_URL,
    VITE_MDM_SERVICE_BASE_URL: env.VITE_MDM_SERVICE_BASE_URL
  });

  // Force environment variables to be available in the app
  const envPrefix = ['VITE_'];

  // Get deployment environment from env variables
  const DEPLOY_ENV = env.DEPLOY_ENV || env.VITE_DEPLOY_ENV || "dev";

  const BASE_PATH =
    DEPLOY_ENV === "staging"
      ? "/staging/ui/question-section/"
      : "/ui/question-section/";

  return {
    base: BASE_PATH,
    plugins: [react()],
    envPrefix: envPrefix,
    test: {
      environment: "jsdom",
      testTimeout: 10000,
      globals: true,
      setupFiles: "./setupTest.js",
      coverage: {
        reporter: ["text", "lcov", "html", "json", "json-summary"],
        include: ["src/**/*.{js,ts,jsx,tsx}"],
        exclude: [
          "**/icons/**",
          "src/main.jsx",
          ...coverageConfigDefaults.exclude,
        ],
      },
      server: {
        deps: {
          inline: [/@kla-v2.*/],
        },
      },
    },
    resolve: {
      alias: {
        "@": resolve(__dirname, "./src"),
      },
    },
  };
});
