import PropTypes from 'prop-types';

function ErrorCircleIcon({ onClick, className = "" }) {
  return (
    <svg
      onClick={onClick}
      className={`cursor-pointer ${className}`}
      width="16"
      height="16"
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.99967 15.1663C11.6816 15.1663 14.6663 12.1816 14.6663 8.49967C14.6663 4.81778 11.6816 1.83301 7.99967 1.83301C4.31778 1.83301 1.33301 4.81778 1.33301 8.49967C1.33301 12.1816 4.31778 15.1663 7.99967 15.1663Z"
        stroke="#98A2B3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10 6.5L6 10.5"
        stroke="#98A2B3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6 6.5L10 10.5"
        stroke="#98A2B3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

ErrorCircleIcon.propTypes = {
  onClick: PropTypes.func, 
  className: PropTypes.string,
};


export default ErrorCircleIcon;
