function AssignIcon(props) {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M12.4976 9.55312C11.1519 8.70549 9.5906 8.2631 8.00024 8.27875C6.83639 8.24923 5.68034 8.47677 4.61449 8.94516C3.54836 9.41366 2.59872 10.1116 1.83328 10.9892L1.83321 10.9892L1.83146 10.9914C1.7655 11.0764 1.72905 11.1805 1.72763 11.2881H1.72762V11.2888V14.1726C1.72149 14.4277 1.81658 14.6748 1.99213 14.86C2.16782 15.0454 2.40979 15.1536 2.66507 15.161L2.66507 15.161H2.66651H8.17318H8.28964L8.20943 15.0765L7.36498 14.1877L7.35041 14.1723L7.32926 14.1721L2.71651 14.1237V11.4733C3.39519 10.765 4.21298 10.2045 5.11862 9.82716C6.03041 9.44725 7.01128 9.26124 7.99885 9.281L8.00052 9.28098C9.35247 9.26276 10.6831 9.61873 11.8454 10.3095L11.8808 10.3305L11.9082 10.2999L12.5082 9.62875L12.5477 9.58465L12.4976 9.55312ZM13.2832 12.6354V14.1721L14.2721 14.191V14.1909C14.2721 14.1908 14.2721 14.1908 14.2721 14.1907L14.2721 11.6443V11.5137L14.1848 11.6109L13.296 12.602L13.2832 12.6163V12.6354ZM15.4828 8.23883L15.4828 8.23882L15.482 8.23808C15.3842 8.15104 15.2559 8.10633 15.1252 8.11375C14.9945 8.12116 14.8721 8.1801 14.7848 8.27763L14.7848 8.27766L9.65689 14.0146L7.38382 11.571C7.34175 11.522 7.29048 11.4818 7.23292 11.4525C7.17504 11.423 7.11193 11.4053 7.04719 11.4002C6.98245 11.3952 6.91736 11.4029 6.85561 11.423C6.79387 11.4431 6.73669 11.4752 6.68735 11.5174L6.68733 11.5174L6.6862 11.5184C6.63786 11.5624 6.59872 11.6155 6.57104 11.6747C6.54336 11.7339 6.52768 11.798 6.52492 11.8633C6.52217 11.9286 6.53238 11.9938 6.55497 12.0551C6.57756 12.1164 6.61208 12.1727 6.65653 12.2206L6.65655 12.2206L9.62988 15.4206L9.6672 15.4607L9.70376 15.4199L15.5215 8.92211L15.5216 8.92212L15.5224 8.92117C15.6049 8.82422 15.6468 8.69917 15.6395 8.57206C15.6321 8.44496 15.576 8.3256 15.4828 8.23883ZM7.99978 7.60543C8.66561 7.60632 9.31675 7.40969 9.87081 7.04042C10.4249 6.67115 10.8569 6.14584 11.1124 5.53094C11.3678 4.91605 11.4351 4.2392 11.3057 3.58605C11.1764 2.9329 10.8562 2.3328 10.3857 1.86166C9.91519 1.39053 9.31552 1.06954 8.66254 0.939317C8.00956 0.80909 7.33263 0.875475 6.71739 1.13007C6.10215 1.38467 5.57626 1.81604 5.20625 2.3696C4.83624 2.92316 4.63874 3.57403 4.63873 4.23987C4.63873 5.13169 4.9927 5.98705 5.62289 6.61808C6.25308 7.24912 7.10796 7.60423 7.99978 7.60543ZM7.99994 1.85876C8.47037 1.8579 8.93047 1.99667 9.32198 2.25749C9.71348 2.51831 10.0188 2.88945 10.1992 3.3239C10.3796 3.75836 10.4271 4.23659 10.3355 4.69802C10.2439 5.15945 10.0175 5.58333 9.68484 5.91598C9.3522 6.24862 8.92832 6.47507 8.46688 6.56663C8.00545 6.6582 7.52722 6.61077 7.09277 6.43034C6.65831 6.24992 6.28717 5.94462 6.02635 5.55311C5.76553 5.16161 5.62676 4.7015 5.62762 4.23107C5.62877 3.60225 5.87908 2.99951 6.32373 2.55486C6.76837 2.11022 7.37111 1.85991 7.99994 1.85876Z"
        fill="white"
        stroke="white"
        strokeWidth={0.1}
      />
    </svg>
  );
}

export default AssignIcon;
