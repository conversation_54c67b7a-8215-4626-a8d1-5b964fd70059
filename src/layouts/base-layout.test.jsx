import { render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import { RouterProvider, createMemoryRouter } from "react-router-dom";
import { describe, expect, it } from "vitest";
import { store } from "../app/store";
import { BaseLayout } from "./base-layout";
import { LanguageProvider } from "@/components/languages/language-context";

// Mock the auto-breadcrumb hook - use vi.fn() directly in factory
vi.mock('@/hooks/use-auto-breadcrumb', () => ({
  useAutoBreadcrumb: vi.fn(),
}));

const SettingsPage = () => {
  // Remove manual breadcrumb setup - now handled by useAutoBreadcrumb
  return <div>SettingsPage</div>;
};

const renderWithRouterAndProviders = () => {
  const routes = [
    {
      path: "/",
      element: <BaseLayout />,
      children: [
        { path: "/", element: <div>HomePage</div> },
        { path: "/settings", element: <SettingsPage /> },
      ],
    },
  ];

  const router = createMemoryRouter(routes, {
    initialEntries: ["/settings"],
  });

  render(
    <Provider store={store}>
      <LanguageProvider>
        <RouterProvider router={router} />
      </LanguageProvider>
    </Provider>
  );

  return {
    getHomePage: () => screen.queryByText("HomePage"),
    settingsPage: screen.getByText("SettingsPage"),
  };
};

describe("BaseLayout Component", () => {
  it("renders the layout with content", () => {
    const { settingsPage } = renderWithRouterAndProviders();
    expect(settingsPage).toBeInTheDocument();
  });


});