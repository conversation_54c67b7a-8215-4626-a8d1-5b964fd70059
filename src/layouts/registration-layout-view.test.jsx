import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import { RegistrationLayoutView } from "./registration-layout-view";

describe("RegistrationLayoutView Component", () => {
  it("renders the title correctly when documentData is provided", () => {
    const mockDocumentData = { title: "Test Document Title" };

    render(
      <RegistrationLayoutView documentData={mockDocumentData}>
        <p>Child Content</p>
      </RegistrationLayoutView>
    );

    expect(screen.getByText("Test Document Title")).toBeInTheDocument();
  });

  it("renders child content correctly", () => {
    render(
      <RegistrationLayoutView documentData={{ name: "Sample Title" }}>
        <p>Child Content</p>
      </RegistrationLayoutView>
    );

    expect(screen.getByText("Child Content")).toBeInTheDocument();
  });

  it("renders without crashing when documentData is missing", () => {
    render(
      <RegistrationLayoutView>
        <p>No Document Data</p>
      </RegistrationLayoutView>
    );

    expect(screen.getByText("No Document Data")).toBeInTheDocument();
  });

  it("renders empty title when documentData.title is undefined", () => {
    render(
      <RegistrationLayoutView documentData={{}}>
        <p>Content</p>
      </RegistrationLayoutView>
    );

    expect(screen.queryByText(/undefined/i)).not.toBeInTheDocument();
  });
});
