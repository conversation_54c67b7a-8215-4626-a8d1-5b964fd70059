import { But<PERSON> } from "@kla-v2/ui-components";
import { PanelBottomIcon } from "lucide-react";
import { LayoutGridIcon } from "lucide-react";
import { EllipsisIcon } from "lucide-react";
import { ChevronRightIcon } from "lucide-react";
import { ChevronLeftIcon } from "lucide-react";
import PropTypes from "prop-types";

function HeaderTitlepanelLayout() {
  return (
    <div className="flex ml-2 h-10 flex-col gap-4">
      <div className="flex justify-between items-center gap-4">
        <h3 className="typography-page-heading text-grey-600">Details</h3>
        <div className="flex gap-4 items-center">
          <div className="h-6 w-11 rounded border border-border-1 divide-x divide-border-1 flex items-center justify-center">
            <Button
              variant="textInfo"
              className="w-6 h-6 p-1 rounded-none"
              aria-label="Chevron Left"
            >
              <ChevronLeftIcon className="w-3 h-3" />
            </Button>
            <Button
              variant="textInfo"
              className="w-6 h-6 p-1 rounded-none"
              aria-label="Chevron Right"
            >
              <ChevronRightIcon className="w-3 h-3" />
            </Button>
          </div>
          <Button
            variant="textInfo"
            className="w-6 h-6 p-0 rounded-none"
            aria-label="Ellipsis"
          >
            <EllipsisIcon className="text-grey-400 w-5 h-5" />
          </Button>
          <Button
            variant="textInfo"
            className="w-6 h-6 p-0 rounded-none"
            aria-label="Panel Bottom"
          >
            <PanelBottomIcon className="text-primary w-5 h-5" />
          </Button>
          <Button
            variant="textInfo"
            className="w-6 h-6 p-0 rounded-none"
            aria-label="Layout Grid"
          >
            <LayoutGridIcon className="text-grey-400 w-5 h-5" />
          </Button>
        </div>
      </div>
    </div>
  );
}
HeaderTitlepanelLayout.propTypes = {
  name: PropTypes.string,
};

export default HeaderTitlepanelLayout;
