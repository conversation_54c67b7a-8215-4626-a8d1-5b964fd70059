import PropTypes from "prop-types";

const RegistrationLayoutView = ({ documentData, children }) => {
  return (
    <div className="flex flex-col">
      <div className="flex justify-between">
        <h1 className="typography-page-heading">{documentData?.title}</h1>
      </div>
      <div className="h-full max-h-full overflow-auto flex flex-col gap-4">
        {children}
      </div>
    </div>
  );
};

RegistrationLayoutView.propTypes = {
  documentData: PropTypes.shape({
    title: PropTypes.string,
  }),
  children: PropTypes.node,
};

export { RegistrationLayoutView };
