import { LanguageContext } from "@/components/languages/language-context";
import { selectBreadcrumbItems } from "@/slices/layout/breadcrumb-slice";
import { useGetActiveAssemblyQuery } from "@/services/master-data-management/active-assembly";
import { B<PERSON><PERSON><PERSON>b<PERSON><PERSON>, Header, Toaster } from "@kla-v2/ui-components";
import { MenuIcon } from "lucide-react";
import { useContext } from "react";
import { useSelector } from "react-redux";
import { Outlet, useNavigate } from "react-router-dom";

function BaseLayout() {
  const breadcrumb = useSelector(selectBreadcrumbItems);
  const { language, changeLanguage } = useContext(LanguageContext);
  const navigate = useNavigate();

  useGetActiveAssemblyQuery();


  const breadcrumbItems = breadcrumb.map((item) => {
    const mappedItem = { ...item };
    if (item?.action) {
      switch (item.action) {
        case "navigate":
          mappedItem["onClick"] = () => {
            navigate(item.route);
          };
          break;
        default:
          console.warn("Unknown breadcrumb action type");
          break;
      }
    }
    return mappedItem;
  });

  return (
    <div className="flex flex-col size-full max-h-full">
      <Header
        onLanguageToggle={(e) => changeLanguage(e)}
        defaultValue={language || "en"}
      />

      <div className="flex items-center gap-2.5 bg-background-container px-5 py-3">
        <div>
          <MenuIcon className="text-foreground size-4" />
        </div>
        <BreadcrumbList items={breadcrumbItems} />
      </div>
      <div className="relative grow overflow-auto">
        <Outlet />
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <Toaster
            className="fixed mt-28 pointer-events-auto"
            position="top-right"
          />
        </div>
      </div>
    </div>
  );
}

export { BaseLayout };
