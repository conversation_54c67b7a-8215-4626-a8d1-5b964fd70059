/**
 * Constants for document types
 */
export const DOCUMENT_TYPES = {
  NOTICE_FOR_QUESTION: 'NOTICE_FOR_QUESTION',
  LATE_ANSWER_BULLETIN: 'LATE_ANSWER_BULLETIN',
  DELAYED_ANSWER_BULLETIN: 'DELAYED_ANSWER_BULLETIN',
  SHORT_NOTICE: 'SHORT_NOTICE',
};

/**
 * Constants for document statuses
 */
export const DOCUMENT_STATUS = {
  DRAFT: 'DRAFT',
  SUBMITTED: 'SUBMITTED',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
};

/**
 * Constants for notice priorities
 */
export const NOTICE_PRIORITIES = {
  P1: 'P1',
  P2: 'P2',
  P3: 'P3',
  NIL: 'NIL',
};

/**
 * Default pagination values
 */
export const PAGINATION = {
  DEFAULT_PAGE: 0,
  DEFAULT_SIZE: 10,
};

/**
 * Routes for different document types
 */
export const DOCUMENT_ROUTES = {
  NOTICE_FOR_QUESTION: '/member/my-question-notices/notice-for-question',
  LATE_ANSWER_BULLETIN: '/section/documents/late-answer-bulletin',
  DELAYED_ANSWER_BULLETIN: '/section/documents/delayed-answer-bulletin',
  SHORT_NOTICE: '/member/my-notices/short-notice/add',
  HALF_AN_HOUR_DISCUSSION: '/member/my-notices/half-an-hour-discussion',
  QUESTION_TO_PRIVATE_MEMBER: '/member/my-notices/notice-for-question-to-private-member',
};
