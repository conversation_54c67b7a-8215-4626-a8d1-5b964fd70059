export const ROUTE_CONFIG = {
  home: {
    path: "/",
    title: "Home",
    breadcrumb: "Home",
  },
  
  // Section Staff Routes
  section: {
    path: "/section",
    title: "Section",
    breadcrumb: "Section",
    children: {
      documents: {
        path: "documents",
        title: "Documents",
        breadcrumb: "Documents",
        children: {
          ministerDesignationGroup: {
            path: "minister-designation-group/:documentId",
            title: "Minister Designation Group",
            breadcrumb: "Minister Designation Group",
          },
          viewMinisterDesignationGroup: {
            path: "view/minister-designation-group/:documentId",
            title: "View Minister Designation Group",
            breadcrumb: "Minister Designation Group (View)",
          },
          allotmentOfDays: {
            path: "allotment-of-days/:documentId",
            title: "Allotment of Days",
            breadcrumb: "Allotment of Days",
          },
          scheduleOfActivity: {
            path: "schedule-of-activity/:documentId",
            title: "Schedule of Activity",
            breadcrumb: "Schedule of Activity",
          },
          delayedAnswerBulletin: {
            path: "delayed-answer-bulletin/:documentId",
            title: "Delayed Answer Bulletin",
            breadcrumb: "Delayed Answer Bulletin",
          },
          answerStatusReport: {
            path: "answer-status-report/:documentId",
            title: "Answer Status Report",
            breadcrumb: "Answer Status Report",
          },
          delayStatement: {
            path: "delay-statement/:documentId",
            title: "Delay Statement",
            breadcrumb: "Delay Statement",
          },
          lateAnswerBulletin: {
            path: "late-answer-bulletin/:documentId",
            title: "Late Answer Bulletin",
            breadcrumb: "Late Answer Bulletin",
          },
          correctionOfAnswer: {
            path: "correction-of-answer/:documentId",
            title: "Correction of Answer",
            breadcrumb: "Correction of Answer",
          },
        },
      },
      questionEditList: {
        path: "question-edit-list",
        title: "Question Edit List",
        breadcrumb: "Question Edit List",
        children: {
          all: {
            path: "all",
            title: "All Questions",
            breadcrumb: "All",
          },
          action: {
            path: "action",
            title: "Action Required",
            breadcrumb: "Action Required",
          },
          edit: {
            path: "edit/:documentId",
            title: "Edit Question",
            breadcrumb: "Edit Question",
          },
        },
      },
      ballot: {
        path: "ballot",
        title: "Ballot",
        breadcrumb: "Ballot",
        children: {
          list: {
            path: "list",
            title: "Ballot List",
            breadcrumb: "Ballot List",
          },
          perform: {
            path: "perform",
            title: "Perform Balloting",
            breadcrumb: "Perform Balloting",
          },
          performWithParams: {
            path: "perform/:ballotingId/:questionDate/:status",
            title: "Perform Balloting",
            breadcrumb: "Perform Balloting",
          },
        },
      },
      settingStarredQuestions: {
        path: "setting-starred-questions",
        title: "Setting of Starred Questions",
        breadcrumb: "Setting of Starred Questions",
      },
      settingUnstarredQuestions: {
        path: "setting-of-unstarred-questions/:documentId",
        title: "Setting of Unstarred Questions",
        breadcrumb: "Setting of Unstarred Questions",
      },
      noticesForQuestion: {
        path: "notices-for-question",
        title: "Notices for Question",
        breadcrumb: "Notices for Question",
        children: {
          action: {
            path: "",
            title: "Action to be Taken",
            breadcrumb: "Action to be Taken",
          },
          all: {
            path: "all",
            title: "All Notices",
            breadcrumb: "All Notices",
          },
        },
      },
    },
  },
  
  // Member Routes
  member: {
    path: "/member",
    title: "Member",
    breadcrumb: "Member",
    children: {
      consent: {
        path: "consent",
        title: "Consent",
        breadcrumb: "Consent",
      },
      myNotices: {
        path: "my-notices",
        title: "My Notices",
        breadcrumb: "My Notices",
        children: {
          noticeBank: {
            path: "notice-bank",
            title: "Notice Bank",
            breadcrumb: "Notice Bank",
          },
          shortNotice: {
            path: "short-notice/add/:documentId",
            title: "Short Notice",
            breadcrumb: "Short Notice",
          },
          halfAnHourDiscussion: {
            path: "half-an-hour-discussion/:documentId",
            title: "Half an Hour Discussion",
            breadcrumb: "Half an Hour Discussion",
          },
          noticeForQuestionToPrivateMember: {
            path: "notice-for-question-to-private-member/:documentId",
            title: "Notice for Question to Private Member",
            breadcrumb: "Question to Private Member",
          },
        },
      },
      myQuestionNotices: {
        path: "my-question-notices",
        title: "My Question Notices",
        breadcrumb: "My Question Notices",
        children: {
          noticeBank: {
            path: "notice-bank",
            title: "Notice Bank",
            breadcrumb: "Notice Bank",
          },
          noticeForQuestion: {
            path: "notice-for-question/:documentId",
            title: "Notice for Question",
            breadcrumb: "Notice for Question",
          },
        },
      },
    },
  },
  
  // PPO Routes
  ppo: {
    path: "/ppo",
    title: "PPO",
    breadcrumb: "PPO",
    children: {
      consent: {
        path: "consent",
        title: "Consent",
        breadcrumb: "Consent",
      },
      myQuestionNotices: {
        path: "my-question-notices",
        title: "My Question Notices",
        breadcrumb: "My Question Notices",
        children: {
          noticeBank: {
            path: "notice-bank",
            title: "Notice Bank",
            breadcrumb: "Notice Bank",
          },
          noticeForQuestion: {
            path: "notice-for-question/:documentId",
            title: "Notice for Question",
            breadcrumb: "Notice for Question",
          },
        },
      },
    },
  },
};

// Helper function to get full path for a route
export const getRoutePath = (path) => {
  const parts = path.split('.');
  let config = ROUTE_CONFIG;
  let fullPath = '';
  
  for (const part of parts) {
    if (part === 'home') {
      config = config[part];
      fullPath = config.path;
    } else if (config.children?.[part]) {
      config = config.children[part];
      fullPath = fullPath + '/' + config.path;
    } else {
      console.warn(`Route part "${part}" not found in config`);
      return null;
    }
  }
  
  return fullPath;
};

// Helper function to get breadcrumb items for a route
export const getBreadcrumbItems = (path) => {
  const parts = path.split('.');
  const items = [];
  let config = ROUTE_CONFIG;
  let fullPath = '';
  
  for (let i = 0; i < parts.length; i++) {
    const part = parts[i];
    
    if (part === 'home') {
      config = config[part];
      fullPath = config.path;
      items.push({
        label: config.breadcrumb,
        route: fullPath,
        action: i < parts.length - 1 ? 'navigate' : undefined,
      });
    } else if (config.children?.[part]) {
      config = config.children[part];
      fullPath = fullPath + '/' + config.path;
      items.push({
        label: config.breadcrumb,
        route: fullPath.replace(/:\w+/g, (match) => {
          // Handle dynamic params - this should be replaced with actual values
          return match;
        }),
        action: i < parts.length - 1 ? 'navigate' : undefined,
      });
    }
  }
  
  return items;
};