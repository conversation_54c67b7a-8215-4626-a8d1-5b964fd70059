import { DataTable } from "@kla-v2/ui-components";
import { Users } from "lucide-react";
import { User } from "lucide-react";
import PropTypes from "prop-types";

export default function ApprovedNotices({ entries, t }) {
  const columns = [
    { accessorKey: "questionNumber", header: t("Question No.") },
    {
      accessorKey: "clubbed",
      header: () => <div className="text-center">{t("Clubbed/Single")}</div>,

      cell: ({ row }) => {
        const { clubbed } = row.original;
        return (
          <div className="flex justify-center">
            {clubbed === false ? <User size={17} /> : <Users size={17} />}
          </div>
        );
      },
    },
    {
      accessorKey: "members",
      header: () => <div className="text-center">{t("Members")}</div>,
      cell: ({ row }) => {
        const members = row.original.members;
        return (
          <div className="flex justify-center gap-1 ">
            {members?.map((member) => (
              <img
                key={member.memberId}
                src={"https://placehold.co/400"}
                alt={member.memberDisplayName}
                className="h-6 w-6 rounded-full object-cover border"
              />
            ))}
          </div>
        );
      },
    },
    { accessorKey: "noticeHeading", header: t("Subject") },
    {
      id: "view",
      header: "",
      cell: () => (
        <span className="text-primary typography-body-text-s-14 cursor-pointer ">
          View
        </span>
      ),
    },
  ];

  return (
    <div className="w-full px-5 flex flex-col h-full">
      <div className="flex-none mb-4">
        <p className="typography-body-text-s-18 text-primary mt-2">
          Setting Up
        </p>
        <p className="typography-body-text-r-14 text-grey-500">
          Questions for {entries?.ministerName}
        </p>
      </div>
      <div className="flex-1 overflow-auto [-ms-overflow-style:'none'] [scrollbar-width:'none'] [&::-webkit-scrollbar]:hidden">
        <div className="h-full">
          <DataTable
            columns={columns}
            data={entries?.questions || []}
            stickyHeader={true}
            className="h-full"
          />
        </div>
      </div>
    </div>
  );
}

ApprovedNotices.propTypes = {
  entries: PropTypes.object.isRequired,
  t: PropTypes.func.isRequired,
};
