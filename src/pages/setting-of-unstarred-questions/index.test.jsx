import { store } from "@/app/store";
import * as ministerService from "@/services/master-data-management/minister-list";
import * as questionService from "@/services/unstarred-questions";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { Provider } from "react-redux";
import { BrowserRouter } from "react-router-dom";
import { beforeEach, describe, expect, it, vi } from "vitest";
import SettingOfUnstarredQuestionsPage from ".";

// Mock useLanguage hook
vi.mock("@/hooks", async () => {
  const original = await vi.importActual("@/hooks");
  return {
    ...original,
    useLanguage: () => ({ t: (key) => key }),
  };
});

// Mock components used in the page
vi.mock("@/components/document-metadata", () => ({
  DocumentMetadata: () => <div data-testid="document-metadata" />,
}));

vi.mock("./minister-card", () => ({
  default: ({ name, onClick }) => (
    <div onClick={onClick} data-testid="minister-card">
      {name}
    </div>
  ),
}));
vi.mock("./pending-notices", () => ({
  default: () => <div data-testid="pending-notices">Pending Notices</div>,
}));
vi.mock("./approved-notices", () => ({
  default: () => <div data-testid="approved-notices">Approved Notices</div>,
}));
vi.mock("./booklet-preview-modal", () => ({
  default: ({ isOpen }) =>
    isOpen ? <div data-testid="preview-modal">Modal Open</div> : null,
}));
vi.mock("./question-booklet-view", () => ({
  default: () => <div data-testid="booklet-view" />,
}));

describe("SettingOfUnstarredQuestionsPage", () => {
  beforeEach(() => {
    // Mock the API responses
    vi.spyOn(ministerService, "useGetMinisterListQuery").mockReturnValue({
      data: [
        {
          id: "1",
          displayName: "Minister One",
        },
      ],
    });

    vi.spyOn(questionService, "useGetUnstarredQuestionsQuery").mockReturnValue({
      data: {
        assembly: 1,
        session: 2,
        type: "SET_UNSTARRED_QUESTIONS",
        currentNumber: "001",
        createdAt: "2025-04-07T10:00:00Z",
        createdBy: "Admin",
        name: "Document",
        status: "approved",
        groupName: "Group 1",
        groupEntries: [
          {
            ministerId: "1",
            ministerDesignation: "Minister of X",
            politicalPartyName: "Party Y",
          },
        ],
        unstarredQuestions: [
          {
            ministerId: "1",
            content: "Test Question",
            order: 1,
          },
        ],
      },
      isLoading: false,
      error: null,
    });
  });

  it("renders and allows interaction", async () => {
    render(
      <Provider store={store}>
        <BrowserRouter>
          <SettingOfUnstarredQuestionsPage />
        </BrowserRouter>
      </Provider>
    );

    // Wait for content to appear
    await waitFor(() => {
      expect(
        screen.getByText("Setting of Unstarred Question")
      ).toBeInTheDocument();
    });

    // Check if minister card renders
    expect(screen.getByTestId("minister-card")).toHaveTextContent(
      "Minister One"
    );

    // Click the minister card
    await userEvent.click(screen.getByTestId("minister-card"));
    expect(screen.getByTestId("approved-notices")).toBeInTheDocument();

    // Open preview modal
    await userEvent.click(
      screen.getByRole("button", { name: /generateBooklet/i })
    );
    expect(await screen.findByTestId("preview-modal")).toBeInTheDocument();
  });
});
