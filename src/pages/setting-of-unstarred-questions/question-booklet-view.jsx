import PropTypes from "prop-types";
import { formatMalayalamDate } from "@/utils";

const QuestionBookletView = ({ questionData }) => {
  return (
    <div className="space-y-2">
      <div className="mx-auto p-6 bg-white rounded shadow-sm text-center">
        <div className="text-sm font-semibold space-y-1">
          <p>{questionData?.assembly}-ാം കേരള നിയമസഭ</p>
          <p>{questionData?.session}-ാം സമ്മേളനം</p>
        </div>

        <div className="mt-4 text-sm font-medium text-gray-600">
          {formatMalayalamDate(questionData?.createdAt)}
        </div>

        <div className="mt-3 font-semibold text-sm">
          നക്ഷത്രചിഹ്നമിടാത്ത ചോദ്യങ്ങൾ
          <br />
          <span className="font-normal text-xs">
            (ആകെ ചൊദ്യങ്ങള്‍: {questionData?.unstarredQuestions.length})
          </span>
        </div>

        <div className="w-[645px] mt-5 border border-gray-300 p-4 rounded-md text-sm text-left inline-block">
          <p className="font-semibold text-center mb-2">
            മറുപടി നൽകുന്ന മന്ത്രിമാർ
          </p>
          {questionData.groupEntries.map((minister, index) => (
            <div key={minister.id || index} className="pl-10">
              <p>{minister.designationInLocal}</p>
            </div>
          ))}
        </div>
      </div>

      {questionData.unstarredQuestions.map((question, index) => {
        const bgColors = ["bg-warning-100", "bg-error-100", "bg-green-100"];
        const randomColor =
          bgColors[Math.floor(Math.random() * bgColors.length)];

        return (
          <div
            key={question.id || index}
            className="bg-white p-6 rounded-md shadow-sm space-y-4 border"
          >
            <div className="items-center gap-2 flex justify-center">
              <span
                className={`w-6 h-6 flex items-center justify-center rounded-full ${randomColor} text-black text-sm`}
              >
                {index + 1}
              </span>
              <span className="text-primary typography-body-text-s-18">
                {question.noticeHeading}
              </span>
            </div>

            <div className="space-y-2">
              {question.members.map((member, i) => (
                <div key={member.id || i} className="flex items-center gap-4">
                  <span className="typography-body-text-m-14 w-2">{i + 1}</span>
                  <img
                    src={"https://placehold.co/400"}
                    alt={member.memberDisplayName}
                    className="w-11 h-11 rounded-full"
                  />
                  <div className="flex flex-row items-center gap-2 text-sm">
                    <span className="typography-body-text-m-14">
                      {member.memberDisplayName}
                    </span>
                    <span className="typography-body-text-m-12 text-grey-600">
                      {member.constituency}
                    </span>
                    <span className="typography-body-text-r-12 text-grey-500">
                      {member.politicalParty}
                    </span>
                  </div>
                </div>
              ))}
            </div>

            <div className="space-y-2">
              <span className="typography-para-16">
                താഴെ കാണുന്ന ചോദ്യങ്ങൾക്കു {question.ministerDesignationInLocal}{" "}
                സദയം മറുപടി പറയാമോ?
              </span>
            </div>

            <div className="space-y-2">
              {question.clauses.map((clause, idx) => (
                <div
                  key={clause.id || idx}
                  className="border p-3 rounded-md flex flex-row"
                >
                  <span className="typography-para-14 mr-2">
                    ({String.fromCharCode(64 + clause.order)})
                  </span>
                  <div>{clause.content}</div>
                </div>
              ))}
            </div>
          </div>
        );
      })}
    </div>
  );
};

QuestionBookletView.propTypes = {
  questionData: PropTypes.shape({
    assembly: PropTypes.number,
    session: PropTypes.number,
    createdAt: PropTypes.string,
    unstarredQuestions: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        noticeHeading: PropTypes.string,
        ministerDesignationInLocal: PropTypes.string,
        members: PropTypes.arrayOf(
          PropTypes.shape({
            id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
            memberDisplayName: PropTypes.string,
            constituency: PropTypes.string,
            politicalParty: PropTypes.string,
          })
        ),
        clauses: PropTypes.arrayOf(
          PropTypes.shape({
            id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
            order: PropTypes.number,
            content: PropTypes.string,
          })
        ),
      })
    ),
    groupEntries: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        designationInLocal: PropTypes.string,
      })
    ),
  }).isRequired,
};

export default QuestionBookletView;
