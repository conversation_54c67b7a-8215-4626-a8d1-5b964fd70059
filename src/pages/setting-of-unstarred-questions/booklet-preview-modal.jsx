import { DocumentMetadata } from "@/components/document-metadata";
import { useLanguage } from "@/hooks";
import {
  Button,
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  Di<PERSON>Header,
  DialogPortal,
  DialogTitle,
} from "@kla-v2/ui-components";
import { Edit, Send } from "lucide-react";
import PropTypes from "prop-types";

const BookletPreviewModal = ({ isOpen, onClose, children, documentdata }) => {
  const { t } = useLanguage();
  const documentMetaData = {
    assembly: documentdata?.assembly?.toString() || "15",
    session: documentdata?.session?.toString() || "",
    documentType:
      documentdata?.documentType || "Setting of Unstarred Questions",
    currentNo: documentdata?.currentNumber || "",
    createdOn: new Date().toLocaleDateString("en-GB"),
    createdBy: documentdata?.createdBy || "",
    name: documentdata?.name || "Setting of Unstarred Questions",
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose} className="overflow-hidden">
      <DialogPortal>
        <DialogContent className="w-[1140px] h-[921px] max-w-[80vw] max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>
              <p
                className="typography-page-heading mb-4 flex items-start"
                data-testid="preview-submit-popup"
              >
                {t("Generated View")}
              </p>
              <div className="h-12">
                <DocumentMetadata documentMetadata={documentMetaData} />
              </div>
            </DialogTitle>
          </DialogHeader>
          <div className="p-4 bg-background overflow-auto">
            <div className="max-h-[90vh] space-y-2">{children}</div>
          </div>
          <DialogFooter className="flex">
            <DialogClose asChild>
              <Button
                size="sm"
                variant="secondary"
                iconPosition="left"
                icon={Edit}
              >
                {t("Edit")}
              </Button>
            </DialogClose>
            <DialogClose asChild>
              <Button
                size="sm"
                variant="primary"
                iconPosition="right"
                icon={Send}
              >
                {t("Forward")}
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

BookletPreviewModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
  documentdata: PropTypes.object,
};

export default BookletPreviewModal;
