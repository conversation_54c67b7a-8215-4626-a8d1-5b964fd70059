import PropTypes from "prop-types";
import { cn } from "@/lib/utils";

export default function Minister<PERSON>ard({
  name,
  designation,
  party,
  count,
  onClick,
  selected = false,
}) {
  return (
    <div
      className={cn(
        "flex items-center justify-between bg-white p-2 rounded-lg shadow border hover:shadow-md transition cursor-pointer",
        selected && "bg-info-50"
      )}
    >
      <div className="flex items-center gap-4" onClick={onClick}>
        <img
          src={"https://placehold.co/400"}
          alt={name}
          className="h-14 w-14 rounded-lg object-cover border"
        />
        <div className="gap-y-6">
          <p className="font-semibold text-sm text-black">{name}</p>
          <p className="text-xs text-gray-600">{designation}</p>
          <p className="text-xs text-gray-500">{party}</p>
        </div>
      </div>

      <div className="relative w-8 h-8 rounded-full border-2 border-pink-500 flex items-center justify-center text-xs font-semibold text-gray-700">
        {count}
      </div>
    </div>
  );
}

MinisterCard.propTypes = {
  name: PropTypes.string.isRequired,
  designation: PropTypes.string.isRequired,
  party: PropTypes.string.isRequired,
  count: PropTypes.number.isRequired,
  onClick: PropTypes.func.isRequired,
  selected: PropTypes.bool,
};
