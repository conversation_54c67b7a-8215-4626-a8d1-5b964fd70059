import { DataTable } from "@kla-v2/ui-components";
import { Users } from "lucide-react";
import { User } from "lucide-react";
import PropTypes from "prop-types";

export default function PendingNotices({ entries, t }) {
  const columns = [
    { accessorKey: "noticeNumber", header: t("Notice No.") },
    {
      accessorKey: "clubbed",
      header: t("Clubbed/Single"),
      cell: ({ row }) => {
        const { clubbed } = row.original;
        return (
          <div className="flex justify-center">
            {clubbed === false ? <User size={17} /> : <Users size={17} />}
          </div>
        );
      },
    },
    { accessorKey: "noticeHeading", header: t("noticeHeading") },
    { accessorKey: "ministerDesignation", header: t("Designation") },
    { accessorKey: "portfolio", header: t("Portfolio") },
    {
      accessorKey: "currenthlywith",
      header: t("Currently With"),
      cell: ({ row }) => {
        const { currentlyWith } = row.original;
        return (
          <div>
            <div>{currentlyWith?.name || ""}</div>
            <div className="text-sm text-gray-500">
              {currentlyWith?.designation || ""}
            </div>
          </div>
        );
      },
    },
  ];

  return (
    <div className="w-full px-5 flex flex-col h-full">
      <h3 className="typography-page-heading font-inter text-lg font-semibold mb-4 mt-2 p-1 flex-none">
        Pending Notices({entries.length})
      </h3>
      <div className="flex-1 overflow-auto [-ms-overflow-style:'none'] [scrollbar-width:'none'] [&::-webkit-scrollbar]:hidden">
        <div className="h-full">
          <DataTable
            columns={columns}
            data={entries || []}
            stickyHeader={true}
            className="h-full"
          />
        </div>
      </div>
    </div>
  );
}

PendingNotices.propTypes = {
  entries: PropTypes.array.isRequired,
  t: PropTypes.func.isRequired,
};
