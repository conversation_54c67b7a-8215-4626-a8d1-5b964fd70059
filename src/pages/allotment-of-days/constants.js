export const jsxData = {
  data: [
    {
      id: 1,
      date: adjustToCurrentMonth("2024-10-01T00:00:00.000Z"),
      entryType: "government",
      entries: [
        {
          description: "Annual private business review and financial reporting",
          descriptionInLocal:
            "വാർഷിക സ്വകാര്യ ബിസിനസ് അവലോകനവും സാമ്പത്തിക റിപ്പോർട്ടിംഗും",
        },
        {
          description: "fsdfs",
          descriptionInLocal:
            "വാർഷിക സ്വകാര്യ ബിസിനസ് അവലോകനവും സാമ്പത്തിക റിപ്പോർട്ടിംഗും",
        },
      ],
    },
    {
      id: 2,
      date: adjustToCurrentMonth("2024-10-02T00:00:00.000Z"),
      entryType: "government",
      entries: [
        {
          description: "Introduction of new business strategies",
          descriptionInLocal: "പുതിയ ബിസിനസ് തന്ത്രങ്ങളുടെ അവതരണം",
        },
      ],
    },
    {
      id: 3,
      date: adjustToCurrentMonth("2024-10-03T00:00:00.000Z"),
      entryType: "government",
      entries: [
        {
          description: "Holiday meeting to discuss upcoming legislation",
          descriptionInLocal: "ലഭ്യമായ നിയമത്തെക്കുറിച്ചുള്ള സഭാ യോഗം",
        },
      ],
    },
    {
      id: 4,
      date: adjustToCurrentMonth("2024-10-14T00:00:00.000Z"),
      entryType: "government",
      entries: [
        {
          description: "Session to address public queries",
          descriptionInLocal: "പൊതു ചോദ്യങ്ങൾ പരിഗണിക്കുന്ന സബ്‌ഹാ സഷൻ",
        },
      ],
    },
    {
      id: 5,
      date: adjustToCurrentMonth("2024-10-10T00:00:00.000Z"),
      entryType: "private",
      entries: [
        {
          description: "Public consultation on civic issues",
          descriptionInLocal: "പൗര പ്രശ്നങ്ങളിലെ പൊതു ഉപദേശം",
        },
      ],
    },
    {
      id: 6,
      date: adjustToCurrentMonth("2024-10-18T00:00:00.000Z"),
      entryType: "holiday",
      entries: [
        {
          description: "Discussion on national policies and budget allocation",
          descriptionInLocal:
            "ദേശീയ നയങ്ങളും ബജറ്റ് വിനിമയവും സംബന്ധിച്ച ചർച്ച",
        },
      ],
    },
    {
      id: 7,
      date: adjustToCurrentMonth("2024-10-09T00:00:00.000Z"),
      entryType: "private",
      entries: [
        {
          description: "Meeting with stakeholders for project approval",
          descriptionInLocal: "പദ്ധതി അംഗീകാരം ലഭിക്കാൻ പങ്കാളികളുമായി യോഗം",
        },
      ],
    },
    {
      id: 8,
      date: adjustToCurrentMonth("2024-10-17T00:00:00.000Z"),
      entryType: "holiday",
      entries: [
        {
          description: "Approval of community-driven projects",
          descriptionInLocal: "സമൂഹം നയിക്കുന്ന പദ്ധതികളുടെ അംഗീകാരം",
        },
      ],
    },
    {
      id: 9,
      date: adjustToCurrentMonth("2024-10-05T00:00:00.000Z"),
      entryType: "private",
      entries: [
        {
          description: "Session to address issues raised by the opposition",
          descriptionInLocal:
            "പ്രതിപക്ഷം ഉന്നയിച്ച പ്രശ്നങ്ങൾ പരിഗണിക്കുന്ന സഷൻ",
        },
        {
          description: "Approval of community-driven projects",
          descriptionInLocal: "സമൂഹം നയിക്കുന്ന പദ്ധതികളുടെ അംഗീകാരം",
        },
      ],
    },
    {
      id: 10,
      date: adjustToCurrentMonth("2024-10-16T00:00:00.000Z"),
      entryType: "holiday",
      entries: [
        {
          description: "Public outreach program for community engagement",
          descriptionInLocal: "സമൂഹ പങ്കാളിത്തത്തിനായുള്ള പൊതു ബന്ധ പരിപാടി",
        },
      ],
    },
    {
      id: 11,
      date: adjustToCurrentMonth("2024-10-11T00:00:00.000Z"),
      entryType: "private",
      entries: [
        {
          description: "Voting on key reforms in the infrastructure sector",
          descriptionInLocal:
            "അടിത്തറ മേഖലയിലെ പ്രധാന പരിഷ്കാരങ്ങളിൽ വോട്ടിംഗ്",
        },
      ],
    },
    {
      id: 12,
      date: adjustToCurrentMonth("2024-10-04T00:00:00.000Z"),
      entryType: "private",
      entries: [
        {
          description: "Quarterly financial statements review",
          descriptionInLocal: "പാദിക സാമ്പത്തിക റിപ്പോർട്ടുകളുടെ അവലോകനം",
        },
      ],
    },
    {
      id: 13,
      date: adjustToCurrentMonth("2024-10-15T00:00:00.000Z"),
      entryType: "government",
      entries: [
        {
          description: "Introduction of new business strategies",
          descriptionInLocal: "പുതിയ ബിസിനസ് തന്ത്രങ്ങളുടെ അവതരണം",
        },
      ],
    },
    {
      id: 14,
      date: adjustToCurrentMonth("2024-10-13T00:00:00.000Z"),
      entryType: "government",
      entries: [
        {
          description: "Introduction of new business strategies",
          descriptionInLocal: "പുതിയ ബിസിനസ് തന്ത്രങ്ങളുടെ അവതരണം",
        },
      ],
    },
    {
      id: 15,
      date: adjustToCurrentMonth("2024-10-26T00:00:00.000Z"),
      entryType: "private",
      entries: [
        {
          description: "Introduction of new business strategies",
          descriptionInLocal: "പുതിയ ബിസിനസ് തന്ത്രങ്ങളുടെ അവതരണം",
        },
      ],
    },
  ],
};

function adjustToCurrentMonth(dateString) {
  const originalDate = new Date(dateString);
  const originalDay = originalDate.getDate();

  // Get the current year and month
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth(); // Month is 0-based (0 = January)

  // Create a new date with the current year and month
  const updatedDate = new Date(currentYear, currentMonth, originalDay);

  // Handle edge cases where the day exceeds the number of days in the new month
  if (updatedDate.getMonth() !== currentMonth) {
    updatedDate.setDate(0); // Set to last valid day of the previous month
  }

  return updatedDate.toISOString(); // Convert back to ISO string
}

export const placesList = [
  { value: "Aakkulam", label: "Aakkulam, Trivandrum" },
  { value: "Adimalathura", label: "Adimalathura, Trivandrum" },
  { value: "Akathumuri", label: "Akathumuri, Trivandrum" },
  { value: "AKG Nagar", label: "AKG Nagar, Trivandrum" },
  { value: "Amaravila", label: "Amaravila, Trivandrum" },
  { value: "Amboori", label: "Amboori, Trivandrum" },
  { value: "Anad", label: "Anad, Trivandrum" },
  { value: "Anavoor", label: "Anavoor, Trivandrum" },
  { value: "Anchuthengu", label: "Anchuthengu, Trivandrum" },
  { value: "Andoorkonam", label: "Andoorkonam, Trivandrum" },
  { value: "Arayoor", label: "Arayoor, Trivandrum" },
  { value: "Arikkadamukku", label: "Arikkadamukku, Trivandrum" },
  { value: "Aruvikkara", label: "Aruvikkara, Trivandrum" },
  { value: "Aruvippuram", label: "Aruvippuram, Trivandrum" },
  { value: "Aryanadu", label: "Aryanadu, Trivandrum" },
  { value: "Athiyannur", label: "Athiyannur, Trivandrum" },
  { value: "Avanavancherry", label: "Avanavancherry, Trivandrum" },
  { value: "Ayiroor", label: "Ayiroor, Trivandrum" },
  { value: "Azhoor", label: "Azhoor, Trivandrum" },
  { value: "Balaramapuram", label: "Balaramapuram, Trivandrum" },
  { value: "Bonacaud", label: "Bonacaud, Trivandrum" },
  { value: "Chemmaruthy", label: "Chemmaruthy, Trivandrum" },
  { value: "Chempazhanthy", label: "Chempazhanthy, Trivandrum" },
  { value: "Chenkal", label: "Chenkal, Trivandrum" },
  { value: "Cheriya Konni", label: "Cheriya Konni, Trivandrum" },
  { value: "Cherunniyoor", label: "Cherunniyoor, Trivandrum" },
  { value: "Chirayinkeezhu", label: "Chirayinkeezhu, Trivandrum" },
  { value: "Choozhattukotta", label: "Choozhattukotta, Trivandrum" },
  { value: "Dhanuvachapuram", label: "Dhanuvachapuram, Trivandrum" },
  { value: "Edakkode", label: "Edakkode, Trivandrum" },
  { value: "Edava", label: "Edava, Trivandrum" },
  { value: "Iroopara", label: "Iroopara, Trivandrum" },
  { value: "Kadakkavoor", label: "Kadakkavoor, Trivandrum" },
  { value: "Kadinamkulam", label: "Kadinamkulam, Trivandrum" },
  { value: "Kaduvakuzhy", label: "Kaduvakuzhy, Trivandrum" },
  { value: "Kallambalam", label: "Kallambalam, Trivandrum" },
  { value: "Kallar", label: "Kallar, Trivandrum" },
  { value: "Kallara", label: "Kallara, Trivandrum" },
  { value: "Kallikkad", label: "Kallikkad, Trivandrum" },
  { value: "Kalliyoor", label: "Kalliyoor, Trivandrum" },
  { value: "Kanjiramkulam", label: "Kanjiramkulam, Trivandrum" },
  { value: "Karakonam", label: "Karakonam, Trivandrum" },
  { value: "Karakulam", label: "Karakulam, Trivandrum" },
  { value: "Karavaram", label: "Karavaram, Trivandrum" },
  { value: "Karode", label: "Karode, Trivandrum" },
  { value: "Karumkulam", label: "Karumkulam, Trivandrum" },
  { value: "Keezharoor", label: "Keezharoor, Trivandrum" },
  {
    value: "Keezhattingal (village)",
    label: "Keezhattingal (village), Trivandrum",
  },
  { value: "Keezhthonnakkal", label: "Keezhthonnakkal, Trivandrum" },
  { value: "Keleswaram", label: "Keleswaram, Trivandrum" },
  { value: "Keraladithyapuram", label: "Keraladithyapuram, Trivandrum" },
  {
    value: "Kilimanoor Block Panchayat",
    label: "Kilimanoor Block Panchayat, Trivandrum",
  },
  {
    value: "Kilimanoor Gram Panchayat",
    label: "Kilimanoor Gram Panchayat, Trivandrum",
  },
  { value: "Koduvazhannoor", label: "Koduvazhannoor, Trivandrum" },
  { value: "Koliyakode", label: "Koliyakode, Trivandrum" },
  { value: "Kollayil (village)", label: "Kollayil (village), Trivandrum" },
  { value: "Koonthalloor", label: "Koonthalloor, Trivandrum" },
  { value: "Kottamom", label: "Kottamom, Trivandrum" },
  { value: "Kottukal (village)", label: "Kottukal (village), Trivandrum" },
  { value: "Kovalam", label: "Kovalam, Trivandrum" },
  { value: "Kudappanamoodu", label: "Kudappanamoodu, Trivandrum" },
  { value: "Kudavoor", label: "Kudavoor, Trivandrum" },
  { value: "Kulathoor", label: "Kulathoor, Trivandrum" },
  { value: "Kulathummal", label: "Kulathummal, Trivandrum" },
  { value: "Kulaviyode", label: "Kulaviyode, Trivandrum" },
  { value: "Kunnathukal", label: "Kunnathukal, Trivandrum" },
  { value: "Kurisumuttom", label: "Kurisumuttom, Trivandrum" },
  { value: "Kurupuzha", label: "Kurupuzha, Trivandrum" },
  { value: "Kuttichal", label: "Kuttichal, Trivandrum" },
  { value: "Madavoor-Pallickal", label: "Madavoor-Pallickal, Trivandrum" },
  { value: "Malayinkeezhu", label: "Malayinkeezhu, Trivandrum" },
  { value: "Manamboor", label: "Manamboor, Trivandrum" },
  { value: "Manikkal", label: "Manikkal, Trivandrum" },
  { value: "Mannoorkara", label: "Mannoorkara, Trivandrum" },
  { value: "Maranalloor", label: "Maranalloor, Trivandrum" },
  { value: "Marianad", label: "Marianad, Trivandrum" },
  { value: "Meenankal", label: "Meenankal, Trivandrum" },
  { value: "Melthonnakkal", label: "Melthonnakkal, Trivandrum" },
  { value: "Memala", label: "Memala, Trivandrum" },
  { value: "Menamkulam", label: "Menamkulam, Trivandrum" },
  { value: "Moothala", label: "Moothala, Trivandrum" },
  { value: "Mudakkal", label: "Mudakkal, Trivandrum" },
  { value: "Muthana", label: "Muthana, Trivandrum" },
  { value: "Nagaroor", label: "Nagaroor, Trivandrum" },
  { value: "Nannatukavu", label: "Nannatukavu, Trivandrum" },
  { value: "Navaikulam", label: "Navaikulam, Trivandrum" },
  { value: "Nellanad", label: "Nellanad, Trivandrum" },
  { value: "Ooruttambalam", label: "Ooruttambalam, Trivandrum" },
  { value: "Ottasekharamangalam", label: "Ottasekharamangalam, Trivandrum" },
  { value: "Ottoor", label: "Ottoor, Trivandrum" },
  { value: "Pakalkuri", label: "Pakalkuri, Trivandrum" },
  { value: "Pallichal", label: "Pallichal, Trivandrum" },
  { value: "Pallickal", label: "Pallickal, Trivandrum" },
  { value: "Pallippuram", label: "Pallippuram, Trivandrum" },
  { value: "Pallithura", label: "Pallithura, Trivandrum" },
  { value: "Palode", label: "Palode, Trivandrum" },
  { value: "Panachamoodu", label: "Panachamoodu, Trivandrum" },
  { value: "Panavoor", label: "Panavoor, Trivandrum" },
  { value: "Pangode", label: "Pangode, Trivandrum" },
  { value: "Parassala", label: "Parassala, Trivandrum" },
  { value: "Parasuvaikkal", label: "Parasuvaikkal, Trivandrum" },
  { value: "Pazhayakunnummel", label: "Pazhayakunnummel, Trivandrum" },
  { value: "Perayam", label: "Perayam, Trivandrum" },
  { value: "Peringamala", label: "Peringamala, Trivandrum" },
  { value: "Peringammala", label: "Peringammala, Trivandrum" },
  { value: "Perumpazhuthoor", label: "Perumpazhuthoor, Trivandrum" },
  { value: "Perunguzhi", label: "Perunguzhi, Trivandrum" },
  { value: "Pirappancode", label: "Pirappancode, Trivandrum" },
  { value: "Ponganadu", label: "Ponganadu, Trivandrum" },
  { value: "Poothura", label: "Poothura, Trivandrum" },
  { value: "Poovar", label: "Poovar, Trivandrum" },
  { value: "Pothencode", label: "Pothencode, Trivandrum" },
  { value: "Pozhiyoor", label: "Pozhiyoor, Trivandrum" },
  { value: "Pulimath", label: "Pulimath, Trivandrum" },
  { value: "Pullampara", label: "Pullampara, Trivandrum" },
  { value: "Pulluvila", label: "Pulluvila, Trivandrum" },
  { value: "Punnamoodu", label: "Punnamoodu, Trivandrum" },
  { value: "Puthenthope", label: "Puthenthope, Trivandrum" },
  { value: "Puthiyakavu", label: "Puthiyakavu, Trivandrum" },
  { value: "Puthucurichy", label: "Puthucurichy, Trivandrum" },
  {
    value: "Sarkara-Chirayinkeezhu",
    label: "Sarkara-Chirayinkeezhu, Trivandrum",
  },
  { value: "St. Andrews, Kerala", label: "St. Andrews, Kerala, Trivandrum" },
  { value: "Thattathumala", label: "Thattathumala, Trivandrum" },
  { value: "Theakada", label: "Theakada, Trivandrum" },
  { value: "Thennoor", label: "Thennoor, Trivandrum" },
  { value: "Thirupuram", label: "Thirupuram, Trivandrum" },
  { value: "Tholicode", label: "Tholicode, Trivandrum" },
  { value: "Thonnakkal", label: "Thonnakkal, Trivandrum" },
  { value: "Uliyazhathura", label: "Uliyazhathura, Trivandrum" },
  { value: "Uzhamalackal", label: "Uzhamalackal, Trivandrum" },
  { value: "Vakkom", label: "Vakkom, Trivandrum" },
  { value: "Valiyavilapuram", label: "Valiyavilapuram, Trivandrum" },
  { value: "Vamanapuram", label: "Vamanapuram, Trivandrum" },
  { value: "Vattappara", label: "Vattappara, Trivandrum" },
  { value: "Vazhichal", label: "Vazhichal, Trivandrum" },
  { value: "Vedivechankovil", label: "Vedivechankovil, Trivandrum" },
  { value: "Veeranakavu", label: "Veeranakavu, Trivandrum" },
  { value: "Veiloor", label: "Veiloor, Trivandrum" },
  { value: "Vellalloor", label: "Vellalloor, Trivandrum" },
  { value: "Vellanad", label: "Vellanad, Trivandrum" },
  { value: "Vellarada", label: "Vellarada, Trivandrum" },
  { value: "Vembannur", label: "Vembannur, Trivandrum" },
  { value: "Vembayam", label: "Vembayam, Trivandrum" },
  { value: "Venganoor", label: "Venganoor, Trivandrum" },
  { value: "Venjarammoodu", label: "Venjarammoodu, Trivandrum" },
  { value: "Vennicode", label: "Vennicode, Trivandrum" },
  { value: "Venpakal", label: "Venpakal, Trivandrum" },
  { value: "Vettoor", label: "Vettoor, Trivandrum" },
  { value: "Vilappil", label: "Vilappil, Trivandrum" },
  { value: "Vilavoorkkal", label: "Vilavoorkkal, Trivandrum" },
  { value: "Vithura", label: "Vithura, Trivandrum" },
  { value: "Vizhinjam", label: "Vizhinjam, Trivandrum" },
];
