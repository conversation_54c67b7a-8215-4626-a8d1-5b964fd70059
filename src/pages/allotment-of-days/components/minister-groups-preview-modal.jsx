import { useLanguage } from "@/hooks";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogPortal,
  DialogTitle,
} from "@kla-v2/ui-components";
import { X } from "lucide-react";
import PropTypes from "prop-types";
import { DocumentMetadata } from "../../../components/document-metadata";

const MinisterGroupsPreviewModal = ({
  isOpen,
  onClose,
  children,
  documentData,
}) => {
  const { t } = useLanguage();

  const documentMetaData = {
    kla: documentData?.assembly?.toString() || "15",
    documentType: "Allotment of Days",
    createdBy: documentData?.createdBy || "",
    createdOn: new Date().toLocaleDateString("en-GB"),
    session: documentData?.session?.toString() || "",
    name: documentData?.name || "Minister Group",
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose} className="overflow-hidden">
      <DialogPortal>
        <DialogContent className="w-[1140px] h-[921px] max-w-[80vw] max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>
              <p
                className="typography-page-heading mb-4 flex items-start"
                data-testid="minister-groups-preview"
              >
                {t("preview")}
              </p>
              <div className="flex flex-row justify-between">
                <p className="typography-sub-title-heading mb-4">
                  Minister Group December 2024
                </p>
              </div>
              <div className="h-12">
                <DocumentMetadata documentMetadata={documentMetaData} />
              </div>
            </DialogTitle>
          </DialogHeader>
          <div className="p-4 bg-background overflow-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-medium">{t("details")}</h3>
            </div>
            <div className="max-h-[90vh] space-y-2">{children}</div>
          </div>
          <DialogFooter className="flex">
            <DialogClose asChild>
              <Button size="sm" variant="primary" iconPosition="right" icon={X}>
                {t("close")}
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

MinisterGroupsPreviewModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
  documentData: PropTypes.object,
};

export default MinisterGroupsPreviewModal;
