import { useLanguage } from "@/hooks";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogPortal,
  DialogTitle,
} from "@kla-v2/ui-components";
import { X } from "lucide-react";
import PropTypes from "prop-types";
import { DocumentMetadata } from "../../../components/document-metadata";

const CalendarOfSittingsModal = ({
  isOpen,
  onClose,
  children,
  documentdata,
}) => {
  const { t } = useLanguage();

  const documentMetaData = {
    kla: documentdata?.assembly?.toString() || "15",
    session: documentdata?.session?.toString() || "",
    documentType: "Calendar of Sittings",
    currentNo: documentdata?.currentNumber || "",
    createdOn: new Date().toLocaleDateString("en-GB"),
    createdBy: documentdata?.createdBy || "",
    name: "Calendar of Sittings",
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose} className="overflow-hidden">
      <DialogPortal>
        <DialogContent className="w-[900px] h-[921px] max-w-[80vw] max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>
              <p
                className="typography-page-heading mb-4 flex items-start"
                data-testid="preview-submit-popup"
              >
                {t("preview")}
              </p>
              <div className="flex flex-row justify-between">
                <p className="typography-sub-title-heading mb-4">
                  {documentMetaData?.name || "Allotment of Days"}
                </p>
              </div>
              <div className="h-12">
                <DocumentMetadata documentMetadata={documentMetaData} />
              </div>
            </DialogTitle>
          </DialogHeader>
          <div className="p-4 bg-background overflow-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-medium">{t("details")}</h3>
            </div>
            <div>{children}</div>
          </div>
          <DialogFooter className="flex">
            <DialogClose asChild>
              <Button
                size="sm"
                variant="secondary"
                iconPosition="left"
                icon={X}
              >
                {t("close")}
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

CalendarOfSittingsModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
  documentdata: PropTypes.object,
};

export default CalendarOfSittingsModal;
