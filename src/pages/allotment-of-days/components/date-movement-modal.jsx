import PropTypes from "prop-types";
import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogPortal,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  Button,
  RadioGroup,
  RadioGroupItem,
  Label,
} from "@kla-v2/ui-components";
import { Trash2, MoveRight, Loader2 } from "lucide-react";

export default function DateMovementModal({
  isOpen,
  date,
  onClose,
  onMove,
  onDelete,
  allGroups,
  currentGroupName,
}) {
  const [selectedGroupId, setSelectedGroupId] = useState(null);
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const formatDateForDisplay = (dateString) => {
    try {
      if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateString)) {
        return dateString;
      }
      const date = new Date(dateString);
      return date.toLocaleDateString("en-GB", {
        day: "numeric",
        month: "numeric",
        year: "numeric",
      });
    } catch (e) {
      console.error(e);
      return dateString;
    }
  };

  const handleMoveConfirm = () => {
    if (selectedGroupId) {
      setIsProcessing(true);

      // Simulate brief processing delay for better UX
      setTimeout(() => {
        onMove(selectedGroupId);
        setIsProcessing(false);
        onClose();
      }, 300);
    }
  };

  const handleDeleteConfirm = () => {
    setIsProcessing(true);

    // Simulate brief processing delay for better UX
    setTimeout(() => {
      onDelete();
      setIsProcessing(false);
      onClose();
    }, 300);
  };

  if (confirmDelete) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogPortal>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Confirm Delete</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this date:{" "}
                {formatDateForDisplay(date)}?
              </DialogDescription>
            </DialogHeader>

            <DialogFooter className="sm:justify-end gap-2">
              <Button
                variant="neutral"
                onClick={() => setConfirmDelete(false)}
                disabled={isProcessing}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDeleteConfirm}
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 size={16} className="mr-2" />
                    Delete
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </DialogPortal>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogPortal>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Manage Date: {formatDateForDisplay(date)}</DialogTitle>
            <DialogDescription>
              You can move this date from {currentGroupName} to another group or
              delete it
            </DialogDescription>
          </DialogHeader>

          <div className="p-4">
            <h4 className="mb-3 font-medium">Select target group:</h4>
            {allGroups.length > 0 ? (
              <RadioGroup
                value={selectedGroupId}
                onValueChange={setSelectedGroupId}
                className="space-y-2"
              >
                {allGroups.map((group) => (
                  <div key={group.id} className="flex items-center space-x-2">
                    <RadioGroupItem id={`group-${group.id}`} value={group.id} />
                    <Label
                      htmlFor={`group-${group.id}`}
                      className="cursor-pointer"
                    >
                      {group.name}
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            ) : (
              <p className="text-gray-500 italic">
                No other groups available to move this date to.
              </p>
            )}
          </div>

          <DialogFooter className="sm:justify-between">
            <Button
              variant="neutral"
              className="text-destructive border-destructive hover:bg-destructive/10"
              onClick={() => setConfirmDelete(true)}
              disabled={isProcessing}
            >
              <Trash2 size={16} className="mr-2" />
              Delete Date
            </Button>
            <div className="flex gap-2">
              <Button
                variant="neutral"
                onClick={onClose}
                disabled={isProcessing}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleMoveConfirm}
                disabled={
                  !selectedGroupId || isProcessing || allGroups.length === 0
                }
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Moving...
                  </>
                ) : (
                  <>
                    <MoveRight size={16} className="mr-2" />
                    Move
                  </>
                )}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
}

DateMovementModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  date: PropTypes.string,
  onClose: PropTypes.func.isRequired,
  onMove: PropTypes.func.isRequired,
  onDelete: PropTypes.func.isRequired,
  allGroups: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
      name: PropTypes.string.isRequired,
    })
  ).isRequired,
  currentGroupName: PropTypes.string.isRequired,
};
