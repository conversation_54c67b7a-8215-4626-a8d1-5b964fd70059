import { useLanguage } from "@/hooks";
import { AttachToFile } from "@/icons";
import {
  Button,
  Dialog,
  DialogClose,
  Dialog<PERSON>ontent,
  DialogFooter,
  DialogHeader,
  DialogPortal,
  DialogTitle,
} from "@kla-v2/ui-components";
import { File, X } from "lucide-react";
import PropTypes from "prop-types";
import { DocumentMetadata } from "../../../components/document-metadata";

const PreviewSubmitPopup = ({ isOpen, onClose, children, documentdata }) => {
  const { t } = useLanguage();
  const documentMetaData = {
    kla: documentdata?.assembly?.toString() || "15",
    session: documentdata?.session?.toString() || "",
    documentType: "Allotment of Days",
    currentNo: documentdata?.currentNumber || "",
    createdOn: new Date().toLocaleDateString("en-GB"),
    createdBy: documentdata?.createdBy || "",
    name: documentdata?.name || "Allotment of Days",
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose} className="overflow-hidden">
      <DialogPortal>
        <DialogContent className="w-[1140px] h-[921px] max-w-[80vw] max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>
              <p
                className="typography-page-heading mb-4 flex items-start"
                data-testid="preview-submit-popup"
              >
                {t("previewAndAttachToFile")}
              </p>
              <div className="flex flex-row justify-between">
                <p className="typography-sub-title-heading mb-4">
                  {documentMetaData?.name || "Allotment of Days"}
                </p>
                <Button
                  variant="neutral"
                  icon={File}
                  iconPosition="right"
                  type="button"
                  size="sm"
                >
                  {t("preview")}
                </Button>
              </div>
              <div className="h-12">
                <DocumentMetadata documentMetadata={documentMetaData} />
              </div>
            </DialogTitle>
          </DialogHeader>
          <div className="p-4 bg-background overflow-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-medium">{t("details")}</h3>
            </div>
            <div className="max-h-[90vh] space-y-2">{children}</div>
          </div>
          <DialogFooter className="flex">
            <DialogClose asChild>
              <Button
                size="sm"
                variant="secondary"
                iconPosition="left"
                icon={X}
              >
                {t("close")}
              </Button>
            </DialogClose>
            <DialogClose asChild>
              <Button
                size="sm"
                variant="primary"
                iconPosition="right"
                icon={AttachToFile}
              >
                {t("attachToFile")}
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

PreviewSubmitPopup.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
  documentdata: PropTypes.object,
};

export default PreviewSubmitPopup;
