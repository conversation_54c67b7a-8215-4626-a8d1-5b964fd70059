import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { describe, test, beforeEach, expect, vi } from "vitest";
import ApplyUpdatesModal from "./apply-updates-modal";
import { useLanguage } from "@/hooks";
import { toast } from "@kla-v2/ui-components";

vi.mock("@/hooks", () => ({
  useLanguage: vi.fn(),
}));

vi.mock("@kla-v2/ui-components", async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    toast: {
      success: vi.fn(),
      error: vi.fn(),
    },
  };
});

describe("ApplyUpdatesModal Component", () => {
  let onCloseMock;
  let tMock;

  beforeEach(() => {
    onCloseMock = vi.fn();
    tMock = vi.fn((key) => key);
    useLanguage.mockReturnValue({ t: tMock });
    vi.clearAllMocks();
  });

  test("renders modal with correct title and content", () => {
    render(<ApplyUpdatesModal isOpen={true} onClose={onCloseMock} />);

    expect(
      screen.getByText("Updates from Approved Calendar of Sitting")
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        "Updated by Question Section Assistant on 12-10-2024, 2:00 am"
      )
    ).toBeInTheDocument();
  });

  test("renders rows with correct status and group info", () => {
    render(<ApplyUpdatesModal isOpen={true} onClose={onCloseMock} />);

    expect(screen.getByText("10/06/2024")).toBeInTheDocument();
    expect(screen.getByText("12/06/2024")).toBeInTheDocument();
    expect(screen.getByText("21/06/2024")).toBeInTheDocument();

    expect(screen.getAllByText("Canceled")).toHaveLength(2);
    expect(screen.getByText("Newly Added")).toBeInTheDocument();
  });

 

  test("calls onClose and shows toast on Apply", async () => {
    render(<ApplyUpdatesModal isOpen={true} onClose={onCloseMock} />);

    const applyButton = screen.getByText("Apply");
    fireEvent.click(applyButton);

    await waitFor(() => {
      expect(onCloseMock).toHaveBeenCalled();
      expect(toast.success).toHaveBeenCalledWith("Success", {
        description: "Submitted",
      });
    });
  });
});
