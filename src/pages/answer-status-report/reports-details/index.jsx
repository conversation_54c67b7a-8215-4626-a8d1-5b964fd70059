import PropTypes from "prop-types";
import { ExpandableContent, ExpandableTrigger } from "@kla-v2/ui-components";
import { AccordionTitle } from "@/components/accordion-title";
import ReportsContent from "./reports";

const ReportsSection = ({ accordionOrderNo }) => {
  return (
    <>
      <ExpandableTrigger
        className="text-primary font-semibold text-base"
        variant="secondary"
      >
        <AccordionTitle
          accordionOrderNo={accordionOrderNo}
          accordionTitle="Reports"
          isColored={true}
        />
      </ExpandableTrigger>
      <ExpandableContent>
        <div className="w-full flex flex-col">
          <ReportsContent />
        </div>
      </ExpandableContent>
    </>
  );
};

ReportsSection.propTypes = {
  accordionOrderNo: PropTypes.number,
};

export { ReportsSection };
