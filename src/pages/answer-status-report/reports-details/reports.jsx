import MultiHeaderDataTable from "@/components/multi-header-data-table";
import { useAnswerStatus } from "../answer-context";
import { useEffect } from "react";
import { useState } from "react";
const headers = [
  {
    id: "main-header",
    cells: [
      {
        id: "slno-header",
        title: "ക്ര. സംഖ്യ",
        colSpan: 1,
        className: "break-words max-w-[200px]",
      },
      {
        id: "department-header",
        title: "വകുപ്പ്/മന്ത്രിഅ",
        colSpan: 1,
        className: "break-words max-w-[200px]",
      },
      {
        id: "date-header",
        title: "തീയതി",
        colSpan: 1,
        className: "break-words max-w-[200px]",
      },
      {
        id: "answered-header",
        title: "ഉത്തരലഭിച്ചവയുള്ള (നിഷേധപ്രഖ്യാപിതമായ ചോദ്യ നമ്പറുകൾ)",
        colSpan: 1,
        className: "break-words max-w-[400px]",
      },
      {
        id: "pending-header",
        title:
          "വിശദം ശേഖരിച്ചിരിക്കുന്നുവെന്ന് പറഞ്ഞ ചില വിഷയങ്ങൾ കൂടി (നിഷേധപ്രഖ്യാപിതമായ ചോദ്യ നമ്പറുകൾ)",
        colSpan: 1,
        className: "break-words max-w-[700px]",
      },
      {
        id: "not-available-header",
        title: "വിശകലനത്തിന് ആവശ്യമായ വിവരങ്ങൾ എടുക്കാൻ കഴിയാത്തത്",
        colSpan: 1,
        className: "break-words max-w-[300px]",
      },
    ],
  },
];

const columns = [
  {
    id: "slNo",
    accessor: "slNo",
    cell: (value) => <div className="py-2">{value}</div>,
  },
  {
    id: "department",
    accessor: "department",
    cell: (value) => <div className="py-2">{value}</div>,
  },
  {
    id: "dates",
    accessor: "dates",
    cell: (value) => (
      <div className="space-y-1 py-2">
        {(Array.isArray(value) ? value : []).map((date, i) => (
          <div key={i} className="leading-relaxed">
            {date}
          </div>
        ))}
      </div>
    ),
  },
  {
    id: "answeredQuestions",
    accessor: "answeredQuestions",
    cell: (value) => (
      <div className="space-y-1 py-2">
        {(Array.isArray(value) ? value : []).map((q, i) => (
          <div key={i} className="leading-relaxed">
            {q}
          </div>
        ))}
      </div>
    ),
  },
  {
    id: "pendingQuestions",
    accessor: "pendingQuestions",
    cell: (value) => (
      <div className="space-y-1 py-2">
        {(Array.isArray(value) ? value : []).map((q, i) => (
          <div key={i} className="leading-relaxed">
            {q}
          </div>
        ))}
      </div>
    ),
  },
  {
    id: "notAvailable",
    accessor: "notAvailable",
    cell: (value) => <div className="py-2">{value}</div>,
  },
];



const getAssemblyMalayalam = (assembly) => {
  switch (assembly) {
    case 15:
      return "പതിനഞ്ചാം കേരള നിയമസഭ";
    case 16:
      return "പതിനാറാം കേരള നിയമസഭ";
    case 17:
      return "പതിനേഴാം കേരള നിയമസഭ";
    case 18:
      return "പതിനെട്ടാം കേരള നിയമസഭ";
    default:
      return `${assembly}`;
  }
};

const getSessionMalayalam = (session) => {
  switch (session) {
    case 1:
      return "ഒന്നാം സെഷൻ";
    case 2:
      return "രണ്ടാം സെഷൻ";
    case 3:
      return "മൂന്നാം സെഷൻ";
    default:
      return `${session}`;
  }
};

const ReportsContent = () => {
  const { assembly, session, answerReport } = useAnswerStatus();
  const [reportData, setReportData] = useState([]);

  useEffect(() => {
    const formattedData = answerReport.map((item, index) => ({
      slNo: index + 1,
      department: item.designationInLocal,
      dates: item.questionDates || [],
      answeredQuestions:
        item.unstarredQuestionsWithInterimAnswerAndNotFinalAnswer || [],
      pendingQuestions: item.countOfQuestionsAwaitingDelayStatement || [],
      notAvailable: item.unstarredQuestionNumbersWithoutAnswer,
    }));

    setReportData(formattedData);
  }, [answerReport]);
  const isMalayalamRequired =
    [15, 16, 17, 18].includes(Number(assembly)) &&
    [1, 2, 3].includes(Number(session));

  const assemblyTitle = isMalayalamRequired
    ? getAssemblyMalayalam(Number(assembly))
    : `${assembly}`;

  const sessionTitle = isMalayalamRequired
    ? getSessionMalayalam(Number(session))
    : `${session}`;

  if (!assembly || !session) {
    return (
      <div className="text-center py-10">
        {" "}
        Please select KLA and Session above*
      </div>
    );
  }

  return (
    <div className="py-4 px-6">
      <div className="text-center mb-6">
        <div className="mb-8">
          <h2 className="text-center text-[18px] font-inter font-semibold leading-[24px] tracking-[-0.4px] break-words">
            {assemblyTitle}
          </h2>

          <h3 className="text-center text-[18px] font-inter font-semibold leading-[24px] tracking-[-0.4px] break-words">
            {sessionTitle}
          </h3>
        </div>
        <p className="mt-4 text-center text-[14px] font-inter font-semibold leading-[24px] tracking-[-0.32px]">
          ഉത്തരം ലഭിച്ചക്കാനുള്ള ചോദ്യങ്ങളുടെയും വിശദാംശം ശേഖരിച്ചുവരുന്നതെന്ന്
          മറുപടി നൽകിയവയുടേയും വിശദമായ സ്റ്റേറ്റ്മെന്റ്
        </p>
      </div>

      <div className="bg-background-container py-6 px-12">
        <MultiHeaderDataTable
          headers={headers}
          columns={columns}
          data={reportData}
          showNumberedHeader
        />
      </div>
    </div>
  );
};

export default ReportsContent;
