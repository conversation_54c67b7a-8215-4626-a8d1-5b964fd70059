import PropTypes from "prop-types";
import { ExpandableContent, ExpandableTrigger } from "@kla-v2/ui-components";
import { AccordionTitle } from "@/components/accordion-title";
import BasicDetailsContent from "./basic-details-content";

const BasicDetailsSection = ({ accordionOrderNo, id }) => {
  return (
    <>
      <ExpandableTrigger
        className="text-primary font-semibold text-base"
        variant="secondary"
      >
        <AccordionTitle
          accordionOrderNo={accordionOrderNo}
          accordionTitle="Basic Details"
          isColored={true}
        />
      </ExpandableTrigger>
      <ExpandableContent>
        <div className="w-full flex flex-col">
          <BasicDetailsContent id={id} />
        </div>
      </ExpandableContent>
    </>
  );
};

BasicDetailsSection.propTypes = {
  accordionOrderNo: PropTypes.number,
  id: PropTypes.string,
};

export { BasicDetailsSection };
