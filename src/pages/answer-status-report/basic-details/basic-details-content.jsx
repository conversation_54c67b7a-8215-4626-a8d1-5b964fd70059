import { useContext, useEffect } from "react";
import { Form, FormField, FormItem } from "../../../components/ui/form";
import { Button, DropdownSelect, toast } from "@kla-v2/ui-components";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { LanguageContext } from "../../../components/languages/language-context";
import { Save } from "lucide-react";
import PropTypes from "prop-types";
import {
  useGetDocumentByIdQuery,
  useAddAnswerStatusReportMutation,
} from "@/services/answer-status-report";
import { useAnswerStatus } from "../answer-context";

const BasicDetailsContent = ({ id }) => {
  const { t } = useContext(LanguageContext);
  const [addAnswerStatusReport] = useAddAnswerStatusReportMutation();
  const { setAssembly, setSession, setAnswerReport } = useAnswerStatus();
  const {
    data: AnswerStatusReport,
    isLoading: AnswerStatusReportLoading,
    error: AnswerStatusReportError,
    refetch,
  } = useGetDocumentByIdQuery(id, { skip: !id });

  const formSchema = z.object({
    assembly: z.string().nonempty(t("Assembly is required")),
    session: z.string().nonempty(t("Session is required")),
  });

  const form = useForm({
    mode: "onSubmit",
    resolver: zodResolver(formSchema),
    defaultValues: {
      assembly: "",
      session: "",
    },
  });

  useEffect(() => {
    if (AnswerStatusReport) {
      setAssembly(AnswerStatusReport.assembly);
      setSession(AnswerStatusReport.session);
      setAnswerReport(AnswerStatusReport.answerStatusReport);
    }
  }, [AnswerStatusReport, setAssembly, setSession, setAnswerReport]);
  

  const { handleSubmit, setValue } = form;

  const assemblyOptions = [
    { label: "15", value: "15" },
    { label: "16", value: "16" },
    { label: "17", value: "17" },
  ];

  const sessionOptions = [
    { label: "1", value: "1" },
    { label: "2", value: "2" },
    { label: "3", value: "3" },
  ];

  useEffect(() => {
    if (AnswerStatusReport) {
      const { assembly = "", session = "" } = AnswerStatusReport;
      setValue("assembly", assembly);
      setValue("session", session);
    }
  }, [AnswerStatusReport, setValue]);

  useEffect(() => {
    if (id) {
      refetch();
    }
  }, [id, refetch]);

  if (!id) {
    return <div>{t("Loading document ID...")}</div>; // Or a more appropriate placeholder
  }

  if (AnswerStatusReportLoading) {
    return <div>{t("Loading basic details...")}</div>;
  }

  if (AnswerStatusReportError) {
    return (
      <div>
        {t("Error loading basic details:")} {AnswerStatusReportError.message}
      </div>
    );
  }

  const onSubmit = async (data) => {
    try {
      const response = await addAnswerStatusReport({
        data: {
          documentId: id,
          assembly: data.assembly,
          session: data.session,
        },
      }).unwrap();
      setAssembly(data.assembly);
      setSession(data.session);
      setAnswerReport(response.answerStatusReport);

      toast.success(t("Saved successfully"));
    } catch (error) {
      toast.error(t("Error saving data"), error);
    }
  };

  return (
    <div className="py-4 px-6">
      <Form {...form}>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="assembly"
              render={({ field, fieldState }) => (
                <FormItem>
                  <DropdownSelect
                    {...field}
                    className="w-full sm:min-w-[250px] md:min-w-[300px] lg:min-w-[400px]"
                    label={t("KLA")}
                    placeholder={t("Enter assembly number")}
                    error={fieldState.error?.message}
                    options={[{ selectItems: assemblyOptions }]}
                    value={field.value}
                    onValueChange={field.onChange}
                  />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="session"
              render={({ field, fieldState }) => (
                <FormItem>
                  <DropdownSelect
                    {...field}
                    className="w-full sm:min-w-[250px] md:min-w-[300px] lg:min-w-[400px]"
                    label={t("Session")}
                    placeholder={t("Enter session")}
                    error={fieldState.error?.message}
                    options={[{ selectItems: sessionOptions }]}
                    value={field.value}
                    onValueChange={field.onChange}
                  />
                </FormItem>
              )}
            />
          </div>

          <div className="flex justify-end pt-4">
            <Button
              type="submit"
              variant="secondary"
              className="flex items-center gap-2"
            >
              <Save size={16} />
              {t("Save")}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

BasicDetailsContent.propTypes = {
  id: PropTypes.string.isRequired, // or PropTypes.number if it's numeric
};

export default BasicDetailsContent;
