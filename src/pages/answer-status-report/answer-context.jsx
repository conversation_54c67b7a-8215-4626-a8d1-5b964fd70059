import { createContext, useContext, useState } from "react";
import PropTypes from "prop-types";
const AnswerStatusContext = createContext();

export const AnswerStatusProvider = ({ children }) => {
  const [assembly, setAssembly] = useState(null);
  const [session, setSession] = useState(null);
  const [answerReport, setAnswerReport] = useState([]);

  return (
    <AnswerStatusContext.Provider
      value={{
        assembly,
        session,
        answerReport,
        setAssembly,
        setSession,
        setAnswerReport,
      }}
    >
      {children}
    </AnswerStatusContext.Provider>
  );
};

AnswerStatusProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export const useAnswerStatus = () => useContext(AnswerStatusContext);
