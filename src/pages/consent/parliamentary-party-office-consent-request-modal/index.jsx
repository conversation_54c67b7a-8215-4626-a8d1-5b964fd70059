import {
  Button,
  Combobox,
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogPortal,
  DialogTitle,
  DialogTrigger,
  Label,
  RadioGroup,
  RadioGroupItem,
} from "@kla-v2/ui-components";
import { Plus } from "lucide-react";
import MultiSelect from "../multi-select";
import { useState } from "react";
import {
  useGetIndependentMLAsQuery,
  useGetMembersByPartyQuery,
  useGetPartiesQuery,
} from "@/services/parties";
import { useRequestConsentMutation } from "@/services/consent";
import PropTypes from "prop-types";
import { useLocation } from "react-router-dom";
import { useLanguage } from "@/hooks";

function PpoConsentModal({ onConsentRequestSuccess }) {
  const { t } = useLanguage();
  const [selectedOption, setSelectedOption] = useState("PARTY");
  const { data: parties } = useGetPartiesQuery();
  const [selectedParty, setSelectedParty] = useState([]);
  const [selectedMembers, setSelectedMembers] = useState([]);
  const partyOptions =
    parties?.data.map((party) => ({
      label: party.name,
      value: party.id,
    })) || [];
  const { data: membersByParty } = useGetMembersByPartyQuery(selectedParty, {
    skip: !selectedParty || selectedOption === "INDEPENDENT_MLA",
  });
  const { data: independentMLAs } = useGetIndependentMLAsQuery(null, {
    skip: selectedOption !== "INDEPENDENT_MLA",
  });
  const members =
    selectedOption === "INDEPENDENT_MLA"
      ? independentMLAs?.data
      : membersByParty?.data || [];

  const [requestConsent] = useRequestConsentMutation();

  const selectedPartyDetails = parties?.data.find(
    (party) => party.id == selectedParty
  );
  const location = useLocation();
  const handleSendRequest = async () => {
    if (!members.length) return;

    // TODO change after authentication
    const consentType = location.pathname.includes("mla-consent")
      ? "MLA"
      : "PPO";
    const payload = selectedMembers.map((member) => ({
      consentType,
      // TODO memberId
      memberId: "1001",
      memberDisplayName: member.displayName,
      memberDisplayNameInLocal: member.displayName,
      ...(consentType === "MLA"
        ? {
            constituencyName: member.constituencyName,
          }
        : null),
      ...(selectedOption === "PARTY"
        ? {
            politicalPartyId: selectedPartyDetails?.id || "",
            politicalPartyName: selectedPartyDetails?.name || "",
            politicalPartyNameInLocal: selectedPartyDetails?.nameInLocal || "",
          }
        : {}),
      requesterId: "",
    }));

    try {
      const response = await requestConsent(payload).unwrap();
      console.log(response);
      if (onConsentRequestSuccess) {
        onConsentRequestSuccess();
      }
    } catch (err) {
      console.error("Error requesting consent:", err);
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          icon={Plus}
          iconPosition="left"
          size="lg"
          className="h-11 w-[204px]"
          variant="secondary"
        >
          {t("requestConsent")}
        </Button>
      </DialogTrigger>
      <DialogPortal>
        <DialogContent className="flex w-[577px] p-8 flex-col items-start gap-8 rounded-lg border border-[#D0D5DD] bg-white shadow-[0px_4px_12px_rgba(29,90,130,0.04)]">
          <DialogHeader>
            <DialogTitle>{t("requestConsent")}</DialogTitle>
            <DialogDescription className="text-grey-500">
              {t("sendRequestConsent")}
            </DialogDescription>
            <hr className="border border-border-1 opacity-30 w-full" />
          </DialogHeader>

          <div className="w-full">
            <div>
              <Label className="typography-body-text-r-14 text-grey-600">
                {t("form:withdrawFrom")}
              </Label>
              <RadioGroup
                className="flex flex-row gap-4 my-2"
                value={selectedOption}
                onValueChange={(e) => setSelectedOption(e)}
              >
                <RadioGroupItem
                  id="party"
                  label={t("form:party")}
                  value="PARTY"
                />
                <RadioGroupItem
                  id="Independent_mla"
                  label={t("form:independentMla")}
                  value="INDEPENDENT_MLA"
                />
              </RadioGroup>
            </div>
            {selectedOption === "PARTY" && (
              <div className="mt-4">
                <Combobox
                  options={partyOptions}
                  placeholder={t("placeholder:select")}
                  label={t("form:party")}
                  size="lg"
                  value={selectedParty}
                  onValueChange={(value) => setSelectedParty(value)}
                />
              </div>
            )}
            <Label className="typography-body-text-r-14 text-grey-600 mt-4">
              {t("form:selectMember")}
            </Label>
            <div className="mt-4">
              <MultiSelect
                members={members}
                onSelectionChange={setSelectedMembers}
              />
            </div>
          </div>

          <DialogFooter className="flex justify-center w-full gap-4">
            <DialogClose asChild>
              <Button variant="neutral" className>
                {t("cancel")}
              </Button>
            </DialogClose>
            <DialogClose asChild>
              <Button
                variant="primary"
                disabled={!selectedMembers.length}
                onClick={handleSendRequest}
              >
                {t("send")}
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
}

export default PpoConsentModal;
PpoConsentModal.propTypes = {
  onConsentRequestSuccess: PropTypes.func.isRequired,
};
