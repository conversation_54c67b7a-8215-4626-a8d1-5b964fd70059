/**
 * Normalizes row data to ensure consistent field access regardless of API response format
 * @param {Object} row - The data row from the API
 * @returns {Object} - Normalized row with consistent field names
 */
export const normalizeRowData = (row) => {
  if (!row) return {};
  
  return {
    id: row.id,
    currentNumber: row.currentNumber || row.noticeNumber || '',
    type: row.type || row.noticeType || '',
    name: row.name || row.noticeHeading || row.title || '',
    ministerDesignation: row.ministerDesignation || '',
    ministerDesignationInLocal: row.ministerDesignationInLocal || '',
    portfolio: row.portfolio || row.ministerPortfolio || '',
    portfolioInLocal: row.portfolioInLocal || '',
    createdDate: row.createdDate || row.createdAt || '',
    questionDate: row.questionDate || '',
    status: row.status || '',
    starred: row.starred || false,
    // Keep the original row data for any missing fields
    ...row
  };
};

/**
 * Normalizes an array of rows for display in a table
 * @param {Array} rows - Array of data rows from the API
 * @returns {Array} - Normalized rows with consistent field names
 */
export const normalizeTableData = (rows = []) => {
  return rows.map(row => normalizeRowData(row));
};
