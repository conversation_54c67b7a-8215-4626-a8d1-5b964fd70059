import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import PropTypes from 'prop-types';
import Notices from '../other-notices-listing';

Notices.propTypes = {
};

vi.mock('@/hooks', () => ({
  useLanguage: () => ({
    t: (key) => key === 'create' ? 'Create' : key
  })
}));

// Mock the auto-breadcrumb hook - use vi.fn() directly in factory
vi.mock('@/hooks/use-auto-breadcrumb', () => ({
  useAutoBreadcrumb: vi.fn(),
}));

vi.mock('lucide-react', () => {
  const PlusIcon = () => <div data-testid="plus-icon" />;
  PlusIcon.propTypes = {};
  return { PlusIcon };
});

vi.mock('./notice-create-modal', () => {
  const NoticeCreateModal = ({ isOpen, onClose }) => (
    isOpen ? <div data-testid="notice-create-modal">
      <button data-testid="close-modal-button" onClick={onClose}>Close Modal</button>
    </div> : null
  );
  
  NoticeCreateModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired
  };
  
  return { 
    __esModule: true,
    default: NoticeCreateModal 
  };
});

vi.mock('@kla-v2/ui-components', () => {
  const Tabs = ({ children, defaultValue }) => (
    <div data-testid="tabs" data-default-value={defaultValue}>
      {children}
    </div>
  );
  
  Tabs.propTypes = {
    children: PropTypes.node.isRequired,
    defaultValue: PropTypes.string
  };
  
  const TabsList = ({ children, variant }) => (
    <div data-testid="tabs-list" data-variant={variant}>
      {children}
    </div>
  );
  
  TabsList.propTypes = {
    children: PropTypes.node.isRequired,
    variant: PropTypes.string
  };
  
  const TabsTrigger = ({ children, value, variant }) => (
    <div data-testid={`tab-${value}`} data-variant={variant}>
      {children}
    </div>
  );
  
  TabsTrigger.propTypes = {
    children: PropTypes.node.isRequired,
    value: PropTypes.string.isRequired,
    variant: PropTypes.string
  };
  
  const Button = ({ children, variant, icon, iconPosition, onClick }) => (
    <button 
      data-testid="button" 
      data-variant={variant} 
      data-icon-position={iconPosition}
      onClick={onClick}
    >
      {icon && <span data-testid="button-icon" />}
      {children}
    </button>
  );
  
  Button.propTypes = {
    children: PropTypes.node.isRequired,
    variant: PropTypes.string,
    icon: PropTypes.elementType,
    iconPosition: PropTypes.string,
    onClick: PropTypes.func
  };
  
  return { Tabs, TabsList, TabsTrigger, Button };
});

const createMockStore = (initialState = {}) => {
  const mockReducer = (state = initialState, action) => {
    switch (action.type) {
      case 'breadcrumb/setBreadcrumb':
        return { ...state, breadcrumbs: action.payload };
      case 'breadcrumb/resetBreadcrumb':
        return { ...state, breadcrumbs: [] };
      default:
        return state;
    }
  };
  
  return configureStore({
    reducer: mockReducer,
    preloadedState: initialState
  });
};

describe('Notices Component', () => {
  let mockStore;
  
  beforeEach(() => {
    mockStore = createMockStore({ breadcrumbs: [] });
  });
  
  afterEach(() => {
    vi.clearAllMocks();
  });
  
  const renderComponent = (path = '/other-notices') => {
    return render(
      <Provider store={mockStore}>
        <MemoryRouter initialEntries={[path]}>
          <Routes>
            <Route path="/other-notices" element={<Notices />}>
              <Route index element={<div data-testid="my-notices-content">My Notices Content</div>} />
              <Route path="notice-bank" element={<div data-testid="notice-bank-content">Notice Bank Content</div>} />
            </Route>
          </Routes>
        </MemoryRouter>
      </Provider>
    );
  };
  
  it('renders the component correctly with default path', () => {
    renderComponent();
    expect(screen.getByText('Notice List')).toBeInTheDocument();
    expect(screen.getByTestId('button')).toBeInTheDocument();
    expect(screen.getByText('Create')).toBeInTheDocument();
    expect(screen.getByTestId('tabs')).toHaveAttribute('data-default-value', 'tab1');
    expect(screen.getByText('My Notices')).toBeInTheDocument();
    expect(screen.getByText('Notice Bank')).toBeInTheDocument();
  });
  
  it('renders with notice-bank path and sets correct default tab', () => {
    renderComponent('/other-notices/notice-bank');
    expect(screen.getByTestId('tabs')).toHaveAttribute('data-default-value', 'tab2');
  });
  
  it('uses auto-breadcrumb hook', async () => {
    const { useAutoBreadcrumb } = await import('@/hooks/use-auto-breadcrumb');
    
    renderComponent();
    
    // Verify auto-breadcrumb hook is called (replaces manual breadcrumb management)
    expect(useAutoBreadcrumb).toHaveBeenCalled();
  });
  
  it('renders create button with correct icon', () => {
    renderComponent();
    const button = screen.getByTestId('button');
    expect(button).toHaveAttribute('data-variant', 'secondary');
    expect(button).toHaveAttribute('data-icon-position', 'left');
    expect(screen.getByTestId('button-icon')).toBeInTheDocument();
  });
  
  it('renders tabs with correct variant', () => {
    renderComponent();
    expect(screen.getByTestId('tabs-list')).toHaveAttribute('data-variant', 'segmented');
    expect(screen.getByTestId('tab-tab1')).toHaveAttribute('data-variant', 'segmented');
    expect(screen.getByTestId('tab-tab2')).toHaveAttribute('data-variant', 'segmented');
  });
  
  it('uses the translation hook for button text', () => {
    renderComponent();
    expect(screen.getByText('Create')).toBeInTheDocument();
  });

  it('opens the modal when create button is clicked', () => {
    renderComponent();
    const createButton = screen.getByTestId('button');

    expect(screen.queryByTestId('notice-create-modal')).not.toBeInTheDocument();
 
    fireEvent.click(createButton);
    
    expect(screen.getByTestId('notice-create-modal')).toBeInTheDocument();
  });
  
  it('closes the modal when onClose is called', () => {
    renderComponent();
    const createButton = screen.getByTestId('button');
    
    fireEvent.click(createButton);
    expect(screen.getByTestId('notice-create-modal')).toBeInTheDocument();
    
    const closeButton = screen.getByTestId('close-modal-button');
    fireEvent.click(closeButton);
    
    expect(screen.queryByTestId('notice-create-modal')).not.toBeInTheDocument();
  });
});