import { NavLink, Outlet, useLocation } from "react-router-dom";
import { PlusIcon } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON> } from "@kla-v2/ui-components";
import { useLanguage } from "@/hooks";
import NoticeCreateModal from "./notice-create-modal";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";
import { useState } from "react";

function Notices() {
  const { t } = useLanguage();
  const location = useLocation();
  const [isOpen, setIsOpen] = useState(false);

  useAutoBreadcrumb();

  const isNoticeBank = location.pathname.includes("notice-bank");

  const closeModal = () => {
    setIsOpen(false);
  };

  return (
    <div className="px-4 py-3 mx-4 my-2 bg-white rounded-md">
      <div className="flex justify-between items-center">
        <h2 className="typography-page-heading">Notice List</h2>
        <Button
          variant="secondary"
          icon={PlusIcon}
          iconPosition="left"
          onClick={() => setIsOpen(true)}
          data-testid="button"
        >
          {t("create")}
        </Button>
      </div>

      <div>
        {/* Determine the path prefix based on the URL */}
        {(() => {
          const pathPrefix = location.pathname.includes("/ppo/")
            ? "/ppo/my-notices"
            : "/member/my-notices";

          return (
            <Tabs value={isNoticeBank ? "tab2" : "tab1"} data-testid="tabs">
              <TabsList variant="segmented" data-testid="tabs-list">
                <NavLink to={pathPrefix} className={({ isActive }) => isActive && !isNoticeBank ? "active" : ""}>
                  <TabsTrigger value="tab1" variant="segmented" data-testid="tab-tab1">
                    My Notices
                  </TabsTrigger>
                </NavLink>
                <NavLink to={`${pathPrefix}/notice-bank`} className={({ isActive }) => isActive && isNoticeBank ? "active" : ""}>
                  <TabsTrigger value="tab2" variant="segmented" data-testid="tab-tab2">
                    Notice Bank
                  </TabsTrigger>
                </NavLink>
              </TabsList>
            </Tabs>
          );
        })()}
        <Outlet />
      </div>
      {isOpen && <NoticeCreateModal isOpen={isOpen} onClose={closeModal} />}
    </div>
  );
}

export default Notices;