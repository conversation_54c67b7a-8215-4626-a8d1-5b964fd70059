import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { MemoryRouter, useSearchParams } from "react-router-dom";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import PropTypes from "prop-types";
import ShortNoticeBanks from "../other-notices-listing/other-notice-bank";
 
vi.mock("@/hooks", () => ({
  useLanguage: () => ({
    t: (key) => (key.includes(":") ? key.split(":")[1] : key),
  }),
  useDebounce: (value) => value,
  useActiveAssembly: () => ({
    activeAssembly: { assembly: "15", session: "13" },
    isLoading: false,
    error: null,
    refetch: vi.fn(),
  }),
}));
 
vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    useSearchParams: vi.fn(),
  };
});
 
vi.mock("../../services/other-notices-notice-bank", () => ({
  useGetOtherNoticesListQuery: vi.fn().mockReturnValue({
    data: {
      content: [
        {
          id: 1,
          noticeType: "Short Notice",
          currentNumber: "SN-001",
          noticeHeading: "Test Notice 1",
          ministerDesignation: "Finance Minister",
          portfolio: "Finance",
        },
        {
          id: 2,
          noticeType: "Half an Hour",
          currentNumber: "HH-002",
          noticeHeading: "Test Notice 2",
          ministerDesignation: "Home Minister",
          ministerPortfolio: "Home Affairs",
        },
      ],
      totalElements: 2,
      totalPages: 1,
    },
    isLoading: false,
    error: null,
  }),
}));
 
vi.mock("../../services/master-data-management/basic-details-notice", () => ({
  useGetPortfolioQuery: vi.fn().mockReturnValue({
    data: [
      { title: "Finance", id: 1 },
      { title: "Home Affairs", id: 2 },
    ],
  }),
  useGetDesignationQuery: vi.fn().mockReturnValue({
    data: [
      { title: "Finance Minister", id: 1 },
      { title: "Home Minister", id: 2 },
    ],
  }),
}));
 
vi.mock("lucide-react", () => ({
  Pencil: () => <div data-testid="pencil-icon" />,
  Search: () => <div data-testid="search-icon" />,
  Delete: () => <div data-testid="delete-icon" />,
}));
 
vi.mock("@/services/master-data-management/active-assembly", () => ({
  selectActiveAssemblyItems: () => ({ assembly: "15", session: "13" }),
}));

vi.mock("@/services/documents", () => ({
  useLazyGetDocumentByIdQuery: () => [vi.fn().mockResolvedValue({})],
}));

vi.mock("@/utils/document-types/document-types", () => ({
  getFormattedDocumentType: vi.fn().mockReturnValue("Formatted Document Type"),
  noticeTypes: [
    { value: "Short Notice", route: "short-notice" },
    { value: "Half an Hour", route: "half-an-hour" },
  ],
}));

vi.mock("@/utils/loaders/spinner-loader", () => ({
  __esModule: true,
  default: () => <div>Loading...</div>,
}));
 
vi.mock("@kla-v2/ui-components", () => {
  const DataTable = ({ columns, data, onRowClick }) => (
    <div data-testid="data-table">
      <div data-testid="columns">
        {JSON.stringify(columns.map((c) => c.header || c.id))}
      </div>
      <div data-testid="rows">{JSON.stringify(data)}</div>
      <button data-testid="row-click" onClick={() => onRowClick(data[0])}>
        Click Row
      </button>
    </div>
  );
 
  DataTable.propTypes = {
    columns: PropTypes.array.isRequired,
    data: PropTypes.array.isRequired,
    onRowClick: PropTypes.func,
  };
 
  const TableDropdownMenu = ({ row, menuItems }) => (
    <div data-testid="dropdown-menu">
      <span>Actions for row: {row.original.id}</span>
      <div data-testid="menu-items">{menuItems.length} items</div>
    </div>
  );
 
  TableDropdownMenu.propTypes = {
    row: PropTypes.object.isRequired,
    menuItems: PropTypes.array.isRequired,
  };
 
  const Input = ({ placeholder, value, onChange, onKeyDown }) => (
    <input
      data-testid="search-input"
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      onKeyDown={onKeyDown}
    />
  );
 
  Input.propTypes = {
    icon: PropTypes.element,
    placeholder: PropTypes.string,
    value: PropTypes.string,
    onChange: PropTypes.func.isRequired,
    onKeyDown: PropTypes.func,
  };
 
  const FilterDropdown = ({ options, onValueChange, disabled, defaultValue }) => (
    <div
      data-testid="filter-dropdown"
      data-options={options.length}
      data-disabled={disabled}
      data-default-value={JSON.stringify(defaultValue)}
      onClick={() =>
        onValueChange({
          noticeType: { value: { "Notice Type": "Short Notice" } },
        })
      }
    >
      Filter
    </div>
  );
 
  FilterDropdown.propTypes = {
    options: PropTypes.array.isRequired,
    onValueChange: PropTypes.func.isRequired,
    disabled: PropTypes.bool,
    defaultValue: PropTypes.object,
  };
 
  const PaginationSelect = ({ onChange, defaultValue, options }) => (
    <select
      data-testid="pagination-select"
      defaultValue={defaultValue}
      onChange={(e) => onChange(e.target.value)}
    >
      {options.map((option, index) => (
        <option key={index} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
 
  PaginationSelect.propTypes = {
    onChange: PropTypes.func.isRequired,
    defaultValue: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
      .isRequired,
    options: PropTypes.arrayOf(
      PropTypes.shape({
        value: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
          .isRequired,
        label: PropTypes.string.isRequired,
      })
    ).isRequired,
  };
 
  const Paginator = ({ currentPage, totalPages, onPageChange }) => (
    <div
      data-testid="paginator"
      data-current-page={currentPage}
      data-total-pages={totalPages}
    >
      <button
        data-testid="prev-page"
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
      >
        Previous
      </button>
      <span data-testid="page-indicator">
        {currentPage} of {totalPages}
      </span>
      <button
        data-testid="next-page"
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
      >
        Next
      </button>
    </div>
  );
 
  Paginator.propTypes = {
    currentPage: PropTypes.number.isRequired,
    totalPages: PropTypes.number.isRequired,
    onPageChange: PropTypes.func.isRequired,
  };
 
  const toast = {
    error: vi.fn(),
  };
 
  return {
    DataTable,
    TableDropdownMenu,
    Input,
    FilterDropdown,
    PaginationSelect,
    Paginator,
    toast,
  };
});
 
const createMockStore = () => {
  return configureStore({
    reducer: (state = {}) => state,
    preloadedState: {
      masterDataManagement: {
        activeAssembly: {
          assembly: "15",
          session: "13",
        },
      },
    },
  });
};
 
describe("ShortNoticeBanks Component", async () => {
  let mockStore;
  let mockSetSearchParams;
  let mockSearchParams;
 
  const { useGetOtherNoticesListQuery } = await import(
    "../../services/other-notices-notice-bank"
  );
 
  const setupMocks = (initialParams = {}) => {
    mockSearchParams = new URLSearchParams();
    Object.entries(initialParams).forEach(([key, value]) => {
      mockSearchParams.set(key, value);
    });
 
    mockSetSearchParams = vi.fn((params) => {
      const updatedParams = new URLSearchParams(params);
      mockSearchParams = updatedParams;
      return updatedParams;
    });
 
    useSearchParams.mockReturnValue([mockSearchParams, mockSetSearchParams]);
  };
 
  beforeEach(() => {
    mockStore = createMockStore();
    setupMocks();
 
    vi.clearAllMocks();
  });
 
  const renderComponent = () => {
    return render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <ShortNoticeBanks />
        </MemoryRouter>
      </Provider>
    );
  };
 
  it("renders the component correctly with default state", () => {
    renderComponent();
 
    expect(screen.getByTestId("search-input")).toBeInTheDocument();
    expect(screen.getByTestId("filter-dropdown")).toBeInTheDocument();
    expect(screen.getByTestId("data-table")).toBeInTheDocument();
    expect(screen.getByTestId("pagination-select")).toBeInTheDocument();
    expect(screen.getByTestId("paginator")).toBeInTheDocument();
  });

  it("handles search input correctly", async () => {
    renderComponent();
 
    const searchInput = screen.getByTestId("search-input");
    fireEvent.change(searchInput, { target: { value: "test search" } });
 
    await waitFor(() => {
      expect(mockSetSearchParams).toHaveBeenCalled();
 
      const updatedParams = mockSetSearchParams.mock.calls[0][0];
      expect(updatedParams.get("search")).toBe("test search");
      expect(updatedParams.get("page")).toBe("1");
    });
  });
 
  it("applies filters correctly", () => {
    renderComponent();
 
    const filterDropdown = screen.getByTestId("filter-dropdown");
    fireEvent.click(filterDropdown);
 
    expect(useGetOtherNoticesListQuery).toHaveBeenCalledWith(
      expect.objectContaining({
        noticeType: expect.any(String),
      })
    );
  });
 
  it("handles escape key in search input correctly", async () => {
    setupMocks({ search: "initial search" });
    renderComponent();
 
    const searchInput = screen.getByTestId("search-input");
    fireEvent.keyDown(searchInput, { key: "Escape" });
 
    await waitFor(() => {
      expect(mockSetSearchParams).toHaveBeenCalled();
      const updatedParams = mockSetSearchParams.mock.calls[0][0];
      expect(updatedParams.has("search")).toBe(false);
    });
  });
 
  it("handles pagination correctly", () => {
    useGetOtherNoticesListQuery.mockReturnValue({
      data: {
        content: [
          {
            id: 1,
            noticeType: "Short Notice",
            currentNumber: "SN-001",
            noticeHeading: "Test Notice 1",
            ministerDesignation: "Finance Minister",
            portfolio: "Finance",
          },
        ],
        totalElements: 25,
        totalPages: 3,
      },
      isLoading: false,
      error: null,
    });
 
    setupMocks({ page: "1", size: "10" });
    renderComponent();
 
    const paginator = screen.getByTestId("paginator");
    expect(paginator).toBeInTheDocument();
 
    const nextPageButton = screen.getByTestId("next-page");
    fireEvent.click(nextPageButton);
 
    expect(mockSetSearchParams).toHaveBeenCalled();
 
    mockSearchParams.set("page", "2");

    expect(mockSearchParams.get("page")).toBe("2");
  });
 
  it("handles page size change correctly", () => {
    renderComponent();
 
    const pageSizeSelect = screen.getByTestId("pagination-select");
    fireEvent.change(pageSizeSelect, { target: { value: "50" } });
 
    expect(mockSetSearchParams).toHaveBeenCalled();
    const updatedParams = mockSetSearchParams.mock.calls[0][0];
    expect(updatedParams.get("size")).toBe("50");
    expect(updatedParams.get("page")).toBe("1");
  });
 
  it("displays the correct page info", () => {
    const mockData = {
      content: [
        {
          id: 1,
          noticeType: "Short Notice",
          currentNumber: "SN-001",
          noticeHeading: "Test Notice 1",
          ministerDesignation: "Finance Minister",
          portfolio: "Finance",
        },
      ],
      totalElements: 25,
      totalPages: 3,
    };
 
    useGetOtherNoticesListQuery.mockReturnValue({
      data: mockData,
      isLoading: false,
      error: null,
    });
 
    setupMocks({ page: "2", size: "10" });
    renderComponent();
 
    expect(screen.getByText("Showing 11 - 20 of 25")).toBeInTheDocument();
  });
 
  it("processes URL parameters correctly on initial load", () => {
    setupMocks({ page: "3", size: "50", search: "test query" });
    renderComponent();
 
    expect(useGetOtherNoticesListQuery).toHaveBeenCalledWith(
      expect.objectContaining({
        page: 2,
        size: 50,
        search: "test query",
      })
    );
  });
 
  it("processes multiple filters correctly", () => {
    renderComponent();
 
    const filterDropdown = screen.getByTestId("filter-dropdown");
    fireEvent.click(filterDropdown);
 
    expect(useGetOtherNoticesListQuery).toHaveBeenCalledWith(
      expect.objectContaining({
        noticeType: expect.any(String)
      })
    );
  });
});