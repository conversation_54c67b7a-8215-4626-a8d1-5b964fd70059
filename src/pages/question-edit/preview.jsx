import { useEffect, useState } from "react";
import PropTypes from "prop-types";
import {
  useGetQuestionBasicDetailsQuery,
  useGetQuestionNoticeDetailsQuery,
} from "@/services/notice-question-edit";
import { useParams } from "react-router-dom";
import { Badge } from "@kla-v2/ui-components";
import BookletPreviewModal from "./booklet-preview-modal";
import { StarredIcon, UnStarredIcon } from "@/icons/star-icon";
import {
  useGetDesignationQuery,
  useGetPortfolioQuery,
  useGetSubSubjectQuery,
} from "@/services/master-data-management/basic-details-notice";
import AddFillIcon from "@/icons/add-fill-icon";
const Preview = ({ isOpen, onClose }) => {
  const { documentId } = useParams();
  const [previewData, setPreviewData] = useState({
    basicDetails: null,
    noticeDetails: null,
    mappedDetails: null,
  });

  const { data: basicDetails } = useGetQuestionBasicDetailsQuery(documentId, {
    skip: !isOpen,
  });

  const { data: noticeDetails } = useGetQuestionNoticeDetailsQuery(documentId, {
    skip: !isOpen,
  });

  const { data: designationData } = useGetDesignationQuery(undefined, {
    skip: !isOpen,
  });
  const { data: portfolioData } = useGetPortfolioQuery(undefined, {
    skip: !isOpen,
  });
  const { data: subjectData } = useGetSubSubjectQuery(undefined, {
    skip: !isOpen,
  });

  useEffect(() => {
    if (isOpen && basicDetails && noticeDetails) {
      const copiedBasicDetails = JSON.parse(JSON.stringify(basicDetails));
      const copiedNoticeDetails = JSON.parse(JSON.stringify(noticeDetails));

      if (copiedNoticeDetails.clauses) {
        copiedNoticeDetails.clauses = [...copiedNoticeDetails.clauses].sort(
          (a, b) => a.order - b.order
        );
      }

      const mappedDetails = {
        ...copiedBasicDetails,
        designationTitle:
          designationData?.find(
            (d) =>
              d.id.toString() ===
              copiedBasicDetails.ministerDesignationId?.toString()
          )?.title || "Not specified",
        portfolioTitle:
          portfolioData?.find(
            (p) =>
              p.id.toString() === copiedBasicDetails.portfolioId?.toString()
          )?.title || "Not specified",
        subSubjectTitle:
          subjectData?.find(
            (s) =>
              s.id.toString() === copiedBasicDetails.subSubjectId?.toString()
          )?.title || "Not specified",
      };

      setPreviewData({
        basicDetails: copiedBasicDetails,
        noticeDetails: copiedNoticeDetails,
        mappedDetails,
      });
    }
  }, [
    isOpen,
    basicDetails,
    noticeDetails,
    designationData,
    portfolioData,
    subjectData,
  ]);

  const getBadgeStyle = (priority) => {
    switch (priority) {
      case "P1":
        return "border border-blue-200 bg-blue-100 text-blue-500";
      default:
        return "border border-gray-300 text-gray-700";
    }
  };

  return (
    <BookletPreviewModal
      isOpen={isOpen}
      onClose={onClose}
      documentdata={previewData.basicDetails || {}}
    >
      {previewData.mappedDetails && previewData.noticeDetails ? (
        <div className="p-4 bg-white rounded-lg shadow-sm">
          <div className="mb-8">
            <h2 className="text-xl font-bold mb-4">Basic Details</h2>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-sm text-gray-600">KLA</p>
                <p className="font-medium">
                  {previewData.mappedDetails.assembly}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Session</p>
                <p className="font-medium">
                  {previewData.mappedDetails.session}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Notice No.</p>
                <p className="font-medium">
                  {previewData.mappedDetails.noticeNumber}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Date of Registration</p>
                <p className="font-medium">
                  {previewData.mappedDetails.dateOfRegistration}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Question Date</p>
                <p className="font-medium">
                  {previewData.mappedDetails.questionDate}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Minister Portfolio</p>
                <p className="font-medium">
                  {previewData.mappedDetails.portfolioTitle}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Minister Sub Subject</p>
                <p className="font-medium">
                  {previewData.mappedDetails.subSubjectTitle}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Designation</p>
                <p className="font-medium">
                  {previewData.mappedDetails.designationTitle}
                </p>
              </div>
            </div>

            <div className="mb-4">
              <p className="text-sm text-gray-600">Name Of Members</p>
              <div className="flex flex-wrap gap-2 mt-1">
                {previewData.mappedDetails.primaryMember && (
                  <div className="flex items-center bg-white border border-gray-200 rounded-full px-3 py-1">
                    <img
                      src={
                        previewData.mappedDetails.primaryMember.imageUrl ||
                        "https://placehold.co/400"
                      }
                      alt={
                        previewData.mappedDetails.primaryMember
                          .memberDisplayName
                      }
                      className="h-6 w-6 rounded-full object-cover border"
                    />
                    <span className="text-sm ml-2">
                      {
                        previewData.mappedDetails.primaryMember
                          .memberDisplayName
                      }
                    </span>
                    <span className="text-xs text-gray-500 ml-1">
                      -{" "}
                      {previewData.mappedDetails.primaryMember.constituencyName}
                    </span>
                  </div>
                )}

                {previewData.mappedDetails.secondaryMembers &&
                  (Array.isArray(previewData.mappedDetails.secondaryMembers) ? (
                    previewData.mappedDetails.secondaryMembers.map(
                      (member, index) => (
                        <div
                          key={index}
                          className="flex items-center bg-white border border-gray-200 rounded-full px-3 py-1"
                        >
                          <img
                            src={member.imageUrl || "https://placehold.co/400"}
                            alt={member.memberDisplayName}
                            className="h-6 w-6 rounded-full object-cover border"
                          />
                          <span className="text-sm ml-2">
                            {member.memberDisplayName}
                          </span>
                          <span className="text-xs text-gray-500 ml-1">
                            - {member.constituencyName}
                          </span>
                        </div>
                      )
                    )
                  ) : (
                    <div className="flex items-center bg-white border border-gray-200 rounded-full px-3 py-1">
                      <img
                        src={
                          previewData.mappedDetails.secondaryMembers.imageUrl ||
                          "https://placehold.co/400"
                        }
                        alt={
                          previewData.mappedDetails.secondaryMembers
                            .memberDisplayName
                        }
                        className="h-6 w-6 rounded-full object-cover border"
                      />
                      <span className="text-sm ml-2">
                        {
                          previewData.mappedDetails.secondaryMembers
                            .memberDisplayName
                        }
                      </span>
                      <span className="text-xs text-gray-500 ml-1">
                        -{" "}
                        {
                          previewData.mappedDetails.secondaryMembers
                            .constituencyName
                        }
                      </span>
                    </div>
                  ))}
              </div>
            </div>
          </div>

          <div className="mt-8">
            <h2 className="text-xl font-bold mb-4">Notice Details</h2>
            <div className="mb-4 flex justify-between">
              <div className="w-40">
                <label className="block text-sm font-medium text-gray-600 mb-2">
                  Priority
                </label>
                <div className="flex gap-2">
                  <Badge
                    className={`px-4 py-2 w-32 rounded-full ${getBadgeStyle(
                      previewData.noticeDetails.noticePriority
                    )}`}
                  >
                    {previewData.noticeDetails.noticePriority}
                  </Badge>
                </div>
              </div>
              <div className="flex items-center mt-6">
                <span className="text-sm mr-2">
                  {previewData.noticeDetails.starred ? "Starred" : "Unstarred"}
                </span>
                {previewData.noticeDetails.starred ? (
                  <StarredIcon />
                ) : (
                  <UnStarredIcon />
                )}
              </div>
            </div>
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Notice Heading
              </label>
              <div className="w-full border border-gray-300 rounded-lg p-3 text-sm bg-white min-h-20">
                {previewData.noticeDetails.noticeHeading}
              </div>
            </div>
            <div className="mb-4">
              {previewData.noticeDetails.constituencies &&
                previewData.noticeDetails.constituencies.length > 0 && (
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Constituencies
                    </label>
                    <div className="flex flex-wrap gap-2">
                      {previewData.noticeDetails.constituencies.map(
                        (constituency, index) => (
                          <Badge key={index} className="h-8 px-3 text-gray-700">
                            {constituency}
                          </Badge>
                        )
                      )}
                    </div>
                  </div>
                )}

              <label className="text-base font-semibold text-black leading-6 mb-3 block">
                Clauses
              </label>
              {previewData.noticeDetails.clauses &&
                previewData.noticeDetails.clauses
                  .sort((a, b) => a.order - b.order)
                  .map((clause, index) => (
                    <div
                      key={index}
                      className=" bg-white p-3 rounded-md border border-gray-200 min-h-20 relative mb-5"
                    >
                      {clause.tags && clause.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {clause.tags.map((tag, tagIndex) => (
                            <Badge
                              key={tagIndex}
                              className={`${
                                tag.title === "Partially Disallow"
                                  ? "bg-pink-50 border border-pink-100 text-pink-500"
                                  : tag.title === "Disallow"
                                  ? " border border-gray-200 text-gray-300 "
                                  : "bg-blue-50 border border-blue-100 text-blue-500"
                              } text-xs`}
                            >
                              <div className="flex items-center gap-3  h-2">
                                <span>{tag.title}</span>
                                {tag.ruleRef && (
                                  <span className="ml-1 text-cyan-600 text-sm font-semibold">
                                    {tag.ruleRef}
                                  </span>
                                )}
                                {tag.title === "Disallow" && (
                                  <AddFillIcon className="!w-[17px] !h-[19px]" />
                                )}
                              </div>
                            </Badge>
                          ))}
                        </div>
                      )}
                      <div className="relative">
                        <span className="absolute left-0 top-1 font-medium text-sm">
                          ({String.fromCharCode(65 + index)})
                        </span>
                        <p className="pl-6 ml-2" style={{ clear: "both" }}>
                          {clause.content}
                        </p>
                      </div>
                    </div>
                  ))}
            </div>
          </div>
        </div>
      ) : (
        <div className="p-8 text-center">
          <p className="text-gray-500">Loading preview data...</p>
        </div>
      )}
    </BookletPreviewModal>
  );
};

Preview.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default Preview;
