import { DocumentMetadata } from "@/components/document-metadata";
import { useLanguage } from "@/hooks";
import {
  Button,
  Dialog,
  DialogClose,
  Dialog<PERSON>ontent,
  DialogFooter,
  Di<PERSON>Header,
  DialogPortal,
  DialogTitle,
} from "@kla-v2/ui-components";
import { ArrowLeft, Download } from "lucide-react";
import { useNavigate } from "react-router-dom";
import PropTypes from "prop-types";

const BookletPreviewModal = ({ isOpen, onClose, children, documentdata }) => {
  const { t } = useLanguage();
  const navigate = useNavigate();

  const documentMetaData = {
    documentType: documentdata?.documentType || "Notice For Questions",
    createdOn:
      documentdata?.dateOfRegistration ||
      new Date().toLocaleDateString("en-GB"),
    createdBy: documentdata?.createdBy || "",
  };

  const handleGoBack = () => {
    navigate("/section/question-edit-list");
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose} className="overflow-hidden">
      <DialogPortal>
        <DialogContent className="w-[1140px] h-[921px] max-w-[80vw] max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>
              <p
                className="typography-page-heading mb-4 flex items-start"
                data-testid="preview-submit-popup"
              >
                {t("Generated View")}
              </p>
              <div className="h-12">
                <DocumentMetadata documentMetadata={documentMetaData} />
              </div>
            </DialogTitle>
          </DialogHeader>
          <div className="p-4 bg-background overflow-auto">
            <div className="max-h-[90vh] space-y-2">{children}</div>
          </div>
          <DialogFooter className="flex justify-between">
            <DialogClose>
              <Button variant="neutral" onClick={handleGoBack} className="mr-2">
                <ArrowLeft className="mr-1" size={16} />
                Back
              </Button>
            </DialogClose>
            <div className="flex gap-2">
              <Button variant="primary">
                <Download className="mr-1" size={16} />
                Download
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

BookletPreviewModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
  documentdata: PropTypes.object,
};

export default BookletPreviewModal;
