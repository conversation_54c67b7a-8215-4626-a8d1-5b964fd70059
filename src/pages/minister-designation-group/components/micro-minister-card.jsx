import PropTypes from "prop-types";
import { useDraggable } from "@dnd-kit/core";
import { CSS } from "@dnd-kit/utilities";
import { Plus, GripVertical } from "lucide-react";
import { ErrorFillIcon } from "@/icons";

export default function MicroMinisterCard({
  minister,
  onAddToGroup = () => {},
  onRemove = () => {},
  showAddButton = false,
  isOverlay = false,
}) {
  const { attributes, listeners, setNodeRef, transform } = useDraggable({
    id: minister.id,
  });

  const style = transform
    ? { transform: CSS.Translate.toString(transform) }
    : undefined;

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      {minister.fromGroup ? renderGroupMinisterUI() : renderListMinisterUI()}
    </div>
  );

  function renderGroupMinisterUI() {
    const cardClasses = `
      flex items-center bg-white cursor-grab
      ${isOverlay ? "shadow-md" : "hover:bg-gray-50"}
    `;

    return (
      <div className={cardClasses}>
        <div className="flex items-center w-full gap-3">
          <div className="mr-2 text-gray-400 cursor-grab">
            <GripVertical size={16} />
          </div>

          <img
            src={`/mdm-service/api/minister/${minister.id}/photo`}
            alt={minister.displayName}
            className="object-cover rounded-full w-9 h-9"
          />

          <div className="flex items-center gap-1">
            <h3 className="typography-body-text-m-16">
              {minister.primaryDesignation.title}
            </h3>
            <p className="typography-body-text-m-14 text-grey-400">
              {minister.displayName}
            </p>
          </div>

          <button
            onClick={() => onRemove(minister.id)}
            onPointerDown={(e) => e.stopPropagation()}
            className="p-1 ml-2 text-gray-400 rounded-full hover:bg-gray-100 hover:text-gray-600"
            aria-label="Remove from group"
          >
            <ErrorFillIcon size={16} />
          </button>
        </div>
      </div>
    );
  }

  function renderListMinisterUI() {
    const cardClasses = `
      flex items-center p-2 border border-1 rounded-lg bg-white cursor-grab
      ${isOverlay ? "shadow-md" : "hover:bg-grey-100"}
    `;

    return (
      <div className={cardClasses}>
        <div className="flex-shrink-0 mr-2">
          <img
            src={`/mdm-service/api/minister/${minister.id}/photo`}
            alt={minister.displayName}
            className="object-cover rounded-md h-14 w-14"
          />
        </div>

        <div className="flex-grow min-w-0 mr-5">
          <h3 className="truncate typography-body-text-m-16">
            {minister.displayName}
          </h3>
          <p className="truncate typography-body-text-m-12 text-grey-600">
            {minister.primaryDesignation.title}
          </p>
        </div>

        {showAddButton && (
          <button
            type="button"
            onPointerDown={(e) => e.stopPropagation()}
            onClick={() => onAddToGroup(minister)}
            className="flex items-center justify-center flex-shrink-0 w-5 h-5 text-white transition-colors rounded-full bg-secondary"
            aria-label={`Add ${minister.displayName} to group`}
          >
            <Plus size={15} />
          </button>
        )}
      </div>
    );
  }
}

MicroMinisterCard.propTypes = {
  minister: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    displayName: PropTypes.string.isRequired,
    image: PropTypes.string,
    fromGroup: PropTypes.bool,
    primaryDesignation: PropTypes.shape({
      title: PropTypes.string.isRequired,
    }).isRequired,
  }).isRequired,
  onAddToGroup: PropTypes.func,
  onRemove: PropTypes.func,
  showAddButton: PropTypes.bool,
  isOverlay: PropTypes.bool,
};
