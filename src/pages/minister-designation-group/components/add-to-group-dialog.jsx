import { useState } from "react";
import PropTypes from "prop-types";
import {
  Button,
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogPortal,
  DialogTitle,
  DialogDescription,
  DropdownSelect,
} from "@kla-v2/ui-components";

export default function AddToGroupDialog({
  isOpen,
  groups,
  onClose,
  onConfirm,
}) {
  const [selectedGroupId, setSelectedGroupId] = useState("");

  const handleSubmit = (e) => {
    e.preventDefault();
    onConfirm(selectedGroupId);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogPortal>
        <DialogContent className="space-y-4">
          <DialogHeader>
            <DialogTitle>Add to Group</DialogTitle>
            <DialogDescription className="text-grey-500">
              Select below option(s) to Add to group
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="w-full">
              <DropdownSelect
                className="w-full"
                label="Group"
                placeholder="Select Group"
                options={[
                  {
                    selectItems: groups.map((group) => ({
                      label: group.groupName,
                      value: group.id,
                    })),
                  },
                ]}
                onChange={(value) => setSelectedGroupId(value)}
                size="lg"
                defaultValue=""
                error=""
              />
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button variant="neutralButton" onClick={onClose}>
                  Cancel
                </Button>
              </DialogClose>
              <Button
                variant="primary"
                type="submit"
                disabled={!selectedGroupId}
              >
                Confirm
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
}

AddToGroupDialog.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  groups: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
    }),
  ).isRequired,
  onClose: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
};
