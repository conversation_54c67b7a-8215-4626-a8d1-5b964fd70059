import {
  DataTable,
  TableTag,
  Input,
  FilterDropdown,
  PaginationSelect,
  Paginator,
  TableDropdownMenu,
} from "@kla-v2/ui-components";
import { useState, useEffect, useCallback, useMemo, useRef } from "react";
import {
  Search,
  FileClock,
  Star,
  User,
  Users,
  Pencil,
  Eye,
} from "lucide-react";
import { useGetSectionStaffAllQuery } from "@/services/section-staff-all";
import {
  useGetPortfolioQuery,
  useGetDesignationQuery,
} from "@/services/master-data-management/basic-details-notice";
import { useLanguage, useDebounce } from "@/hooks";
import { useSearchParams } from "react-router-dom";
import PropTypes from "prop-types";
import { formatDateYYYYMMDD } from "@/utils";
import SpinnerLoader from "@/utils/loaders/spinner-loader";
import { useSelector } from "react-redux";
import { selectActiveAssemblyItems } from "@/services/master-data-management/active-assembly";

const StatusCell = ({ renderValue }) => {
  const status = renderValue();

  const statusMap = {
    TOBEASSIGNED: (
      <TableTag variant="return" className="w-max">
        To be Assigned
      </TableTag>
    ),
    SUBMITTED: (
      <TableTag variant="approved" className="w-max">
        Submitted
      </TableTag>
    ),
  };

  return statusMap[status] || null;
};

StatusCell.propTypes = {
  renderValue: PropTypes.func,
};

const CategoryCell = ({ renderValue }) => {
  const value = renderValue();
  return value === "unStarred" ? (
    <Star size={17} />
  ) : (
    <Star size={17} fill="gold" color="gold" />
  );
};

CategoryCell.propTypes = {
  renderValue: PropTypes.func,
};

const ClubbedCell = ({ renderValue }) => {
  const value = renderValue();
  return value === "No" ? <User size={17} /> : <Users size={17} />;
};

ClubbedCell.propTypes = {
  renderValue: PropTypes.func,
};

const ActionCell = ({ row }) => {
  const menuItems = [
    {
      label: (
        <div className="flex items-center gap-2">
          <Pencil size={16} /> Check Duplicate
        </div>
      ),
    },
    {
      label: (
        <div className="flex items-center gap-2">
          <Eye size={16} /> View Details
        </div>
      ),
    },
  ];

  return <TableDropdownMenu row={row} menuItems={menuItems} />;
};

ActionCell.propTypes = {
  row: PropTypes.shape({
    original: PropTypes.object.isRequired,
  }).isRequired,
};

function AllTabs() {
  const { t } = useLanguage();
  const [searchParams, setSearchParams] = useSearchParams();
  const [filteredData, setFilteredData] = useState({});
  const [searchInput, setSearchInput] = useState(
    searchParams.get("search") || ""
  );
  const debouncedSearchValue = useDebounce(searchInput, 500);

  const page = parseInt(searchParams.get("page") || "1") - 1;
  const size = parseInt(searchParams.get("size") || "10");
  const searchQuery = searchParams.get("search") || "";
  const activeAssembly = useSelector(selectActiveAssemblyItems);

  const { data: portfolioData } = useGetPortfolioQuery();
  const { data: designationData } = useGetDesignationQuery();

  const filters = useMemo(() => {
        if (activeAssembly?.assembly && activeAssembly?.session) {
          return {
            Assembly: { filter: "Assembly", value: activeAssembly?.assembly },
            Session: { filter: "Session", value: activeAssembly?.session},
          };
        }
        return {};
      }, [activeAssembly?.assembly, activeAssembly?.session]);

        const filteredDataRef = useRef(filteredData);
         useEffect(() => {
          filteredDataRef.current = filteredData;
      }, [filteredData]);

         useEffect(() => {
          if (
            Object.keys(filters).length > 0 &&
            Object.keys(filteredDataRef.current).length === 0
             ) {
          setFilteredData(filters);
         }
      }, [filters]);

  const extractFiltersFromParams = () => {
    const params = {};
    for (const [key, value] of searchParams.entries()) {
      if (!["page", "size", "search"].includes(key)) {
        params[key] = value;
      }
    }
    return params;
  };

  const filterParams = extractFiltersFromParams();

  const processFilters = (filters) => {
    const queryParams = {};

    Object.entries(filters).forEach(([key, filterData]) => {
      const { filter, value } = filterData;

      switch (key) {
          case "clubbed":
            queryParams["clubbed"] =
            typeof value === "object" && value !== null ? value.clubbed || "" : value || "";
            break;
          case "category":
            queryParams["category"] =
            typeof value === "object" && value !== null ? value.category || "" : value || "";
            break;
          case "ministerDesignation":
            queryParams["ministerDesignation"] =
            typeof value === "object" && value !== null ? value.Designation || "" : value || "";
            break;
          case "portfolio":
            queryParams["portfolio"] =
            typeof value === "object" && value !== null ? value.Portfolio || "" : value || "";
            break;
          case "noticePriority":
            queryParams["noticePriority"] =
            typeof value === "object" && value !== null ? value.Priority || "" : value || "";
            break;
          case "status":
            queryParams["status"] =
            typeof value === "object" && value !== null ? value.Status || "" : value || "";
            break;
            case "Assembly":
              queryParams["kla"] =
              typeof value === "object" && value !== null ? value.Assembly || "" : value || "";
              break;
            case "Session":
              queryParams["session"] =
              typeof value === "object" && value !== null? value.Session || "" : value || "";
             break;;
        case "questionDate":
          handleDateFilter(queryParams, filter, value, "questionDate");
          break;
        default:
          break;
      }
    });

    return queryParams;
  };

  const handleDateFilter = (queryParams, filter, value, dateType) => {
    if (filter === "Is" && value) {
      queryParams[`${dateType}StartDate`] = formatDateYYYYMMDD(value);
      queryParams[`${dateType}EndDate`] = formatDateYYYYMMDD(value);
    } else if (filter === "between" && value) {
      if (value.from) {
        queryParams[`${dateType}StartDate`] = formatDateYYYYMMDD(value.from);
      }
      if (value.to) {
        queryParams[`${dateType}EndDate`] = formatDateYYYYMMDD(value.to);
      }
    }
  };

  const {
    data: actionToBeTakenData,
    isLoading: actionToBeTakenLoading,
  } = useGetSectionStaffAllQuery({
    search: searchQuery,
    ...processFilters(filteredData),
    page,
    size,
    ...filterParams,
  });

  const mapQuestionNoticeData = useCallback((data) => {
    return (
      data?.content?.map((item) => ({
        id: item.id,
        noticeNumber: item.noticeNumber,
        clubbed:
          item.secondaryMembers && item.secondaryMembers.length > 0
            ? "Yes"
            : "No",
        category: item.starred ? "Starred" : "unStarred",
        noticeHeading: item.noticeHeading,
        ministerDesignation: item.ministerDesignation,
        portfolio: item.portfolio,
        questionDate: new Date(item.questionDate).toLocaleDateString(),
        noticePriority: item.noticePriority,
        status: item.status,
      })) || []
    );
  }, []);

  const myNoticeTableData = mapQuestionNoticeData(actionToBeTakenData);

  const updateParams = useCallback(
    (newParams) => {
      const updatedParams = new URLSearchParams(searchParams);

      if (
        Object.prototype.hasOwnProperty.call(newParams, "page") ||
        Object.prototype.hasOwnProperty.call(newParams, "size")
      ) {
        if (newParams.page !== undefined) {
          updatedParams.set("page", newParams.page.toString());
        }
        if (newParams.size !== undefined) {
          updatedParams.set("size", newParams.size.toString());
        }

        setSearchParams(updatedParams);
        return;
      }

      if (Object.prototype.hasOwnProperty.call(newParams, "search")) {
        if (newParams.search === "") {
          updatedParams.delete("search");
        } else {
          updatedParams.set("search", newParams.search);
        }
      }

      Object.entries(newParams).forEach(([key, value]) => {
        if (
          key !== "search" &&
          value !== undefined &&
          value !== null &&
          value !== ""
        ) {
          updatedParams.set(key, value.toString());
        }
      });

      updatedParams.set("page", "1");
      setSearchParams(updatedParams);
    },
    [searchParams, setSearchParams]
  );

  const handleSearchInputChange = (event) => {
    setSearchInput(event.target.value);
  };

  const handleSearchKeyDown = (event) => {
    if (event.key === "Escape") {
      setSearchInput("");
      updateParams({ search: "" });
    }
  };

  const handlePageChange = (newPage) => {
    updateParams({ page: newPage + 1 });
  };

  const handlePageSizeChange = (newSize) => {
    updateParams({ size: newSize, page: 1 });
  };

  useEffect(() => {
    if (debouncedSearchValue !== searchQuery) {
      updateParams({ search: debouncedSearchValue });
    }
  }, [debouncedSearchValue, searchQuery, updateParams]);

  const getDisplayedPageInfo = () => {
    const totalElements = actionToBeTakenData?.totalElements;
    if (!totalElements) return "";

    const startIndex = (page + 1) * size - (size - 1);
    const endIndex = Math.min((page + 1) * size, totalElements);

    return `Showing ${startIndex} - ${endIndex} of ${totalElements}`;
  };

  const getFilterOptions = () => {
    const memberOptions = [];

    const clubbedOptions = [
      { label: "Clubbed", value: "Clubbed" },
      { label: "Not Clubbed", value: "notClubbed" },
    ];

    const categoryOptions = [
      { label: "Starred", value: "Starred" },
      { label: "Unstarred", value: "unStarred" },
    ];

    const designationOptions =
      designationData?.map((designation) => ({
        label: designation.title,
        value: designation.title,
      })) || [];

    const portfolioOptions =
      portfolioData?.map((portfolio) => ({
        label: portfolio.title,
        value: portfolio.title,
      })) || [];

    const priorityOptions = [
      { label: "P1", value: "P1" },
      { label: "P2", value: "P2" },
      { label: "P3", value: "P3" },
      { label: "NIL", value: "NIL" },
    ];

    const statusOptions = [
      { label: "To be Assigned", value: "TOBEASSIGNED" },
      { label: "Submitted", value: "SUBMITTED" },
    ];

    const klaOptions = [
      { label: "15", value: "15" },
      { label: "14", value: "14" },
      { label: "13", value: "13" },
    ];

    const sessionOptions = [
      { label: "15", value: "15" },
      { label: "14", value: "14" },
      { label: "13", value: "13" },
    ];

    return {
      memberOptions,
      clubbedOptions,
      categoryOptions,
      designationOptions,
      portfolioOptions,
      priorityOptions,
      statusOptions,
      klaOptions,
      sessionOptions,
    };
  };

  const {
    clubbedOptions,
    categoryOptions,
    designationOptions,
    portfolioOptions,
    priorityOptions,
    statusOptions,
    klaOptions,
    sessionOptions,
  } = getFilterOptions();

  const tableColumns = [
    {
      accessorKey: "noticeNumber",
      header: "Notice No.",
      meta: { className: "min-w-28" },
    },
    {
      accessorKey: "clubbed",
      header: "Clubbed",
      cell: ClubbedCell,
    },
    {
      accessorKey: "category",
      header: "Category",
      cell: CategoryCell,
    },
    {
      accessorKey: "noticeHeading",
      header: "Notice Heading",
      meta: { className: "min-w-48" },
    },
    {
      accessorKey: "ministerDesignation",
      header: "Designation",
    },
    {
      accessorKey: "portfolio",
      header: "Portfolio",
    },
    {
      accessorKey: "questionDate",
      header: "Question Date",
      meta: { className: "min-w-36" },
    },
    {
      accessorKey: "noticePriority",
      header: "Priority",
    },
    {
      accessorKey: "status",
      meta: { className: "text-center capitalize" },
      header: "Status",
      cell: StatusCell,
    },
    {
      header: "Action",
      id: "action",
      meta: { className: "text-center w-[120px]" },
      cell: ActionCell,
    },
  ];

  const questionNoticeFilterOptions = [
    {
      label: t("table:Assembly"),
      submenu: [
        {
          comboboxOptions: klaOptions,
          label: "Assembly",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Assembly",
          truncateStringLength: 6,
          value: "KLA",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "Assembly",
    },
    {
      label: t("table:Session"),
      submenu: [
        {
          comboboxOptions: sessionOptions,
          label: "Session",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Session",
          truncateStringLength: 6,
          value: "Session",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "Session",
    },
    {
      label: t("Clubbed"),
      submenu: [
        {
          comboboxOptions: clubbedOptions,
          label: "Clubbed",
          maxCount: 1,
          size: "lg",
          placeholder: "Select Clubbed or Not Clubbed",
          truncateStringLength: 6,
          value: "clubbed",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "clubbed",
    },
    {
      label: t("Category"),
      submenu: [
        {
          comboboxOptions: categoryOptions,
          label: "Category",
          maxCount: 1,
          size: "lg",
          placeholder: "Select Category",
          truncateStringLength: 6,
          value: "category",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "category",
    },
    {
      label: t("table:Designation"),
      submenu: [
        {
          comboboxOptions: designationOptions,
          label: "Designation",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Designation",
          truncateStringLength: 6,
          value: "Designation",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "ministerDesignation",
    },
    {
      label: t("table:Portfolio"),
      submenu: [
        {
          comboboxOptions: portfolioOptions,
          label: "Portfolio",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Portfolio",
          truncateStringLength: 6,
          value: "Portfolio",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "portfolio",
    },
    {
      label: t("Question Date"),
      submenu: [
        {
          label: "Date is",
          type: "singleDate",
          value: "Is",
        },
        {
          label: "Date Range",
          type: "rangeDate",
          value: "between",
        },
      ],
      type: "radioDateSubmenu",
      value: "questionDate",
    },
    {
      label: t("table:Priority"),
      submenu: [
        {
          comboboxOptions: priorityOptions,
          label: "Priority",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Priority",
          truncateStringLength: 6,
          value: "Priority",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "noticePriority",
    },
    {
      label: "Status",
      submenu: [
        {
          comboboxOptions: statusOptions,
          icon: FileClock,
          label: "Status",
          maxCount: 1,
          placeholder: "Select Status",
          size: "lg",
          truncateStringLength: 6,
          value: "Status",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "status",
    },
  ];

  const paginationOptions = [
    { label: "10 per page", value: "10" },
    { label: "50 per page", value: "50" },
    { label: "100 per page", value: "100" },
  ];

  const renderSearch = () => (
    <div className="relative w-80">
      <Input
        icon={Search}
        className="w-full"
        reserveErrorSpace={false}
        placeholder="Search"
        size="md"
        value={searchInput}
        onChange={handleSearchInputChange}
        onKeyDown={handleSearchKeyDown}
      />
    </div>
  );

  const renderFilter = () => (
    <div className="flex gap-2">
      <FilterDropdown
        badgeContainerClassName=""
        options={questionNoticeFilterOptions}
        onValueChange={(data) => {
          setFilteredData(data);
        }}
        disabled={actionToBeTakenLoading}
        defaultValue={filters}
      />
    </div>
  );

  const renderTableContent = () => {
    if (actionToBeTakenLoading) {
      return <SpinnerLoader />;
    }

    return (
      <>
        <DataTable columns={tableColumns} data={myNoticeTableData} />

        <div className="flex justify-between mt-4">
          <div className="flex gap-4 items-center">
            <span className="typography-body-text-s-14 text-grey-600">
              {getDisplayedPageInfo()}
            </span>
            <PaginationSelect
              onChange={handlePageSizeChange}
              defaultValue={size.toString()}
              options={paginationOptions}
            />
          </div>
          <div>
            <Paginator
              currentPage={page + 1}
              totalPages={actionToBeTakenData?.totalPages || 0}
              onPageChange={(newPage) => handlePageChange(newPage - 1)}
              showPreviousNext={true}
            />
          </div>
        </div>
      </>
    );
  };

  return (
    <>
      <div className="flex justify-between items-center mt-4">
        {renderSearch()}
        {renderFilter()}
      </div>

      <div className="mt-4">{renderTableContent()}</div>
    </>
  );
}

export default AllTabs;
