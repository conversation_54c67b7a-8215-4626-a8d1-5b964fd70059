/* eslint-disable react/prop-types */
import { describe, it, vi, expect, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import AllTabs from './all-tab';

const mockSetSearchParams = vi.fn();
const mockSearchParams = new URLSearchParams();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useSearchParams: () => [mockSearchParams, mockSetSearchParams],
  };
});

const mockUseGetSectionStaffAllQuery = vi.fn();
vi.mock('@/services/section-staff-all', () => ({
  useGetSectionStaffAllQuery: (params) => mockUseGetSectionStaffAllQuery(params)
}));

const mockUseGetPortfolioQuery = vi.fn();
const mockUseGetDesignationQuery = vi.fn();
vi.mock('@/services/master-data-management/basic-details-notice', () => ({
  useGetPortfolioQuery: () => mockUseGetPortfolioQuery(),
  useGetDesignationQuery: () => mockUseGetDesignationQuery()
}));

vi.mock('@/hooks', () => ({
  useLanguage: () => ({ t: (key) => key }),
  useDebounce: (value) => value,
}));

vi.mock('@/utils', () => ({
  formatDateYYYYMMDD: (date) => date,
}));

vi.mock('@/utils/loaders/spinner-loader', () => ({
  default: () => <div data-testid="spinner-loader">Loading...</div>,
}));

const mockActiveAssembly = { assembly: '15', session: '15' };
vi.mock('@/services/master-data-management/active-assembly', () => ({
  selectActiveAssemblyItems: () => mockActiveAssembly,
}));

const createMockStore = (customState = {}) => {
  return configureStore({
    reducer: {
      dummy: (state = {}) => state,
    },
    preloadedState: {
      ...customState
    },
  });
};

vi.mock('@kla-v2/ui-components', () => ({
  DataTable: ({ columns, data }) => (
    <div data-testid="data-table">
      <span>Columns: {columns.length}</span>
      <span>Rows: {data.length}</span>
    </div>
  ),
  TableTag: ({ children, variant }) => (
    <div data-testid="table-tag" data-variant={variant}>
      {children}
    </div>
  ),
  Input: ({ placeholder, value, onChange, onKeyDown }) => (
    <input
      data-testid="search-input"
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      onKeyDown={onKeyDown}
    />
  ),
  FilterDropdown: ({ options, onValueChange, disabled }) => {
    setTimeout(() => {
      onValueChange({ test: { filter: 'test', value: 'test-value' } });
    }, 0);
    
    return (
      <button
        data-testid="filter-dropdown"
        data-options={options?.length || 0}
        data-disabled={disabled}
        onClick={() => onValueChange({ test: { filter: 'test', value: 'test-value' } })}
      >
        Filter
      </button>
    );
  },
  PaginationSelect: ({ onChange, defaultValue, options }) => (
    <select
      data-testid="pagination-select"
      defaultValue={defaultValue}
      onChange={(e) => onChange(e.target.value)}
    >
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  ),
  Paginator: ({ currentPage, totalPages, onPageChange }) => (
    <div data-testid="paginator">
      <button data-testid="prev-page" onClick={() => onPageChange(currentPage - 1)}>Previous</button>
      <span>Page {currentPage} of {totalPages}</span>
      <button data-testid="next-page" onClick={() => onPageChange(currentPage + 1)}>Next</button>
    </div>
  ),
  TableDropdownMenu: ({ menuItems }) => (
    <div data-testid="table-dropdown-menu">
      <span>Actions: {menuItems?.length || 0}</span>
    </div>
  ),
}));

describe('AllTabs Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockSearchParams.delete('page');
    mockSearchParams.delete('size');
    mockSearchParams.delete('search');
    mockSetSearchParams.mockClear();
    
    mockUseGetSectionStaffAllQuery.mockReturnValue({
      data: { content: [], totalElements: 0, totalPages: 0 },
      isLoading: false,
    });
    
    mockUseGetPortfolioQuery.mockReturnValue({
      data: []
    });
    
    mockUseGetDesignationQuery.mockReturnValue({
      data: []
    });
  });
  
  afterEach(() => {
    vi.resetAllMocks();
  });
  
  it('renders the component with loading state', () => {
    mockUseGetSectionStaffAllQuery.mockReturnValue({
      data: null,
      isLoading: true,
    });
    
    render(
      <Provider store={createMockStore()}>
        <MemoryRouter>
          <AllTabs />
        </MemoryRouter>
      </Provider>
    );
    
    expect(screen.getByTestId('spinner-loader')).toBeInTheDocument();
    expect(screen.getByTestId('search-input')).toBeInTheDocument();
    expect(screen.getByTestId('filter-dropdown')).toBeInTheDocument();
  });
  
  it('renders the component with data', () => {
    const mockData = {
      content: [
        {
          id: '1',
          noticeNumber: 'NOTICE-001',
          secondaryMembers: [],
          starred: true,
          noticeHeading: 'Test Notice',
          ministerDesignation: 'Minister',
          portfolio: 'Finance',
          questionDate: '2023-05-15',
          noticePriority: 'P1',
          status: 'SUBMITTED',
        },
        {
          id: '2',
          noticeNumber: 'NOTICE-002',
          secondaryMembers: ['member1', 'member2'],
          starred: false,
          noticeHeading: 'Another Notice',
          ministerDesignation: 'Deputy Minister',
          portfolio: 'Education',
          questionDate: '2023-05-16',
          noticePriority: 'P2',
          status: 'TOBEASSIGNED',
        },
      ],
      totalElements: 2,
      totalPages: 1,
    };
    
    mockUseGetSectionStaffAllQuery.mockReturnValue({
      data: mockData,
      isLoading: false,
    });
    
    render(
      <Provider store={createMockStore()}>
        <MemoryRouter>
          <AllTabs />
        </MemoryRouter>
      </Provider>
    );
    
    expect(screen.getByTestId('data-table')).toBeInTheDocument();
    expect(screen.getByText('Rows: 2')).toBeInTheDocument();
    expect(screen.getByTestId('pagination-select')).toBeInTheDocument();
    expect(screen.getByTestId('paginator')).toBeInTheDocument();
  });
  
  it('handles search input change', async () => {
    render(
      <Provider store={createMockStore()}>
        <MemoryRouter>
          <AllTabs />
        </MemoryRouter>
      </Provider>
    );
    
    const searchInput = screen.getByTestId('search-input');
    await userEvent.type(searchInput, 'test search');
    
    expect(searchInput.value).toBe('test search');
 
    await waitFor(() => {
      expect(mockSetSearchParams).toHaveBeenCalled();
    });
  });
  
  it('handles pagination change', async () => {
    mockSearchParams.set('page', '1');
    mockSearchParams.set('size', '10');
    
    const mockData = {
      content: Array(10).fill().map((_, i) => ({
        id: i.toString(),
        noticeNumber: `NOTICE-00${i}`,
        secondaryMembers: [],
        starred: i % 2 === 0,
        noticeHeading: `Test Notice ${i}`,
        ministerDesignation: 'Minister',
        portfolio: 'Finance',
        questionDate: '2023-05-15',
        noticePriority: 'P1',
        status: 'SUBMITTED',
      })),
      totalElements: 20,
      totalPages: 2,
    };
    
    mockUseGetSectionStaffAllQuery.mockReturnValue({
      data: mockData,
      isLoading: false,
    });
    
    render(
      <Provider store={createMockStore()}>
        <MemoryRouter>
          <AllTabs />
        </MemoryRouter>
      </Provider>
    );
    
    const nextButton = screen.getByTestId('next-page');
    fireEvent.click(nextButton);
    
    expect(mockSetSearchParams).toHaveBeenCalled();
    const updatedParams = new URLSearchParams();
    updatedParams.set('page', '2');
    updatedParams.set('size', '10');
    expect(mockSetSearchParams).toHaveBeenCalledWith(updatedParams);
  });
  
  it('handles page size change', async () => {
    mockSearchParams.set('page', '1');
    mockSearchParams.set('size', '10');
    
    render(
      <Provider store={createMockStore()}>
        <MemoryRouter>
          <AllTabs />
        </MemoryRouter>
      </Provider>
    );
    
    const pageSizeSelect = screen.getByTestId('pagination-select');
    fireEvent.change(pageSizeSelect, { target: { value: '50' } });
    
    expect(mockSetSearchParams).toHaveBeenCalled();
    const updatedParams = new URLSearchParams();
    updatedParams.set('page', '1');
    updatedParams.set('size', '50');
    expect(mockSetSearchParams).toHaveBeenCalledWith(updatedParams);
  });
  
  it('sets initial filters from active assembly', () => {
    render(
      <Provider store={createMockStore()}>
        <MemoryRouter>
          <AllTabs />
        </MemoryRouter>
      </Provider>
    );
    
    expect(mockUseGetSectionStaffAllQuery).toHaveBeenCalledWith(
      expect.objectContaining({
        kla: '15',
        session: '15'
      })
    );
  });
  
  it('displays correct pagination info', () => {
    mockSearchParams.set('page', '1');
    mockSearchParams.set('size', '10');
    
    const mockData = {
      content: Array(10).fill().map((_, i) => ({
        id: i.toString(),
        noticeNumber: `NOTICE-00${i}`,
        secondaryMembers: [],
        starred: false,
        noticeHeading: `Test Notice ${i}`,
        ministerDesignation: 'Minister',
        portfolio: 'Finance',
        questionDate: '2023-05-15',
        noticePriority: 'P1',
        status: 'SUBMITTED',
      })),
      totalElements: 25,
      totalPages: 3,
    };
    
    mockUseGetSectionStaffAllQuery.mockReturnValue({
      data: mockData,
      isLoading: false,
    });
    
    render(
      <Provider store={createMockStore()}>
        <MemoryRouter>
          <AllTabs />
        </MemoryRouter>
      </Provider>
    );
    
    expect(screen.getByText('Showing 1 - 10 of 25')).toBeInTheDocument();
  });
});


