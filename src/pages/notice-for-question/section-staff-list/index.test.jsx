import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import SectionStaffList from '../section-staff-list';
import * as languageHook from '@/hooks';
import { configureStore } from '@reduxjs/toolkit';

vi.mock('@/hooks', () => ({
  useLanguage: vi.fn()
}));

// Mock the auto-breadcrumb hook - use vi.fn() directly in factory
vi.mock('@/hooks/use-auto-breadcrumb', () => ({
  useAutoBreadcrumb: vi.fn(),
}));

vi.mock('@kla-v2/ui-components', () => ({
  Tabs: vi.fn(({ children, defaultValue }) => (
    <div data-value={defaultValue}>{children}</div>
  )),
  TabsList: vi.fn(({ children, variant }) => (
    <div role="tablist" data-variant={variant}>{children}</div>
  )),
  TabsTrigger: vi.fn(({ children, value, variant }) => (
    <div role="tab" data-value={value} data-variant={variant}>{children}</div>
  )),
  Button: vi.fn(({ children, variant, iconPosition }) => (
    <button data-variant={variant} data-icon-position={iconPosition}>{children}</button>
  ))
}));

const createMockStore = () => {
  return configureStore({
    reducer: {
      breadcrumb: (state = {}) => state
    }
  });
};

describe('SectionStaffList', () => {
  let mockStore;
  
  beforeEach(() => {
    mockStore = createMockStore();

    vi.mocked(languageHook.useLanguage).mockReturnValue({
      t: (key) => key === 'Submit' ? 'Submit' : key
    });
  });
  
  afterEach(() => {
    vi.clearAllMocks();
  });
  
  it('renders the component correctly', () => {
    render(
      <Provider store={mockStore}>
        <MemoryRouter initialEntries={['/notice-for-question-list']}>
          <Routes>
            <Route path="/notice-for-question-list/*" element={<SectionStaffList />} />
          </Routes>
        </MemoryRouter>
      </Provider>
    );

    expect(screen.getByText('Notice For Question List')).toBeInTheDocument();
    expect(screen.getByText('Submit')).toBeInTheDocument();
    expect(screen.getByText('Action to be taken')).toBeInTheDocument();
    expect(screen.getByText('All')).toBeInTheDocument();
  });
  
  it('uses auto-breadcrumb hook', async () => {
    const { useAutoBreadcrumb } = await import('@/hooks/use-auto-breadcrumb');
    
    render(
      <Provider store={mockStore}>
        <MemoryRouter initialEntries={['/notice-for-question-list']}>
          <SectionStaffList />
        </MemoryRouter>
      </Provider>
    );

    // Verify the auto-breadcrumb hook is called (replaces manual breadcrumb setup)
    expect(useAutoBreadcrumb).toHaveBeenCalled();
  });
  
  it('renders correct NavLinks for tabs', () => {
    render(
      <Provider store={mockStore}>
        <MemoryRouter initialEntries={['/notice-for-question-list']}>
          <Routes>
            <Route path="/notice-for-question-list/*" element={<SectionStaffList />} />
          </Routes>
        </MemoryRouter>
      </Provider>
    );

    const actionTabLink = screen.getByText('Action to be taken').closest('a');
    expect(actionTabLink).toHaveAttribute('href', '/notice-for-question-list');
    
    const allTabLink = screen.getByText('All').closest('a');
    expect(allTabLink).toHaveAttribute('href', '/notice-for-question-list/all');
  });
  
  it('sets correct default tab value based on location', () => {
    const { unmount } = render(
      <Provider store={mockStore}>
        <MemoryRouter initialEntries={['/notice-for-question-list']}>
          <Routes>
            <Route path="/notice-for-question-list/*" element={<SectionStaffList />} />
          </Routes>
        </MemoryRouter>
      </Provider>
    );

    const tabsElement = screen.getByRole('tablist');
    expect(tabsElement.parentElement).toHaveAttribute('data-value', 'tab1');
    
    unmount();

    render(
      <Provider store={mockStore}>
        <MemoryRouter initialEntries={['/notice-for-question-list/notice-bank']}>
          <Routes>
            <Route path="/notice-for-question-list/*" element={<SectionStaffList />} />
          </Routes>
        </MemoryRouter>
      </Provider>
    );

    const tabsElementNoticeBank = screen.getByRole('tablist');
    expect(tabsElementNoticeBank.parentElement).toHaveAttribute('data-value', 'tab2');
  });
});