/* eslint-disable react/prop-types */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import ActionToBeTaken from './action-to-be-taken';
import * as sectionStaffActions from '@/services/section-staff-actions';
import * as masterDataManagement from '@/services/master-data-management/basic-details-notice';
import * as activeAssembly from '@/services/master-data-management/active-assembly';

vi.mock('@/hooks', () => ({
  useLanguage: () => ({ t: (key) => key }),
  useDebounce: vi.fn((value) => value)
}));

vi.mock('@/services/section-staff-actions', () => ({
  useGetActionToBeTakenQuery: vi.fn()
}));

vi.mock('@/services/master-data-management/basic-details-notice', () => ({
  useGetPortfolioQuery: vi.fn(),
  useGetDesignationQuery: vi.fn()
}));

vi.mock('@/services/master-data-management/active-assembly', () => ({
  selectActiveAssemblyItems: vi.fn()
}));

vi.mock('@/utils/loaders/spinner-loader', () => ({
  default: () => <div data-testid="spinner-loader">Loading...</div>
}));

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => vi.fn(),
    useSearchParams: () => {
      const searchParams = new URLSearchParams(window.location.search);
      const setSearchParams = (newParams) => {
        const url = new URL(window.location.href);
        for (const [key, value] of newParams.entries()) {
          if (value) {
            url.searchParams.set(key, value);
          } else {
            url.searchParams.delete(key);
          }
        }
        window.history.pushState({}, '', url);
      };
      return [searchParams, setSearchParams];
    }
  };
});

vi.mock('@kla-v2/ui-components', () => ({
  DataTable: ({ data, columns }) => (
    <div data-testid="data-table">
      <div data-testid="data-count">{data.length}</div>
      <table>
        <thead>
          <tr>
            {columns.map((col) => (
              <th key={col.header || col.accessorKey}>{col.header}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((row, index) => (
            <tr key={row.id || index} data-testid="table-row">
              {columns.map((col) => (
                <td key={col.header || col.accessorKey}>
                  {col.cell 
                    ? <div data-testid={`cell-${col.accessorKey || col.id}`}>
                        {col.cell({ row: { original: row }, renderValue: () => row[col.accessorKey] })}
                      </div>
                    : row[col.accessorKey]
                  }
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  ),
  TableTag: ({ variant, children }) => (
    <span data-testid={`table-tag-${variant}`}>{children}</span>
  ),
  Input: ({ value, onChange, onKeyDown, placeholder }) => (
    <input
      data-testid="search-input"
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      onKeyDown={onKeyDown}
    />
  ),
  FilterDropdown: ({ options, onValueChange, defaultValue }) => (
    <div data-testid="filter-dropdown">
      <button 
        data-testid="filter-dropdown-button"
        onClick={() => onValueChange({ 
          status: { filter: 'Status', value: 'SUBMITTED' } 
        })}
      >
        Filter
      </button>
      <span data-testid="filter-options">{JSON.stringify(options)}</span>
      <span data-testid="filter-defaults">{JSON.stringify(defaultValue)}</span>
    </div>
  ),
  PaginationSelect: ({ onChange, options, defaultValue }) => (
    <select 
      data-testid="pagination-select" 
      onChange={(e) => onChange(e.target.value)}
      defaultValue={defaultValue}
    >
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  ),
  Paginator: ({ currentPage, totalPages, onPageChange }) => (
    <div data-testid="paginator">
      <button 
        data-testid="prev-page" 
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage <= 1}
      >
        Prev
      </button>
      <span data-testid="current-page">{currentPage}</span>
      <span data-testid="total-pages">{totalPages}</span>
      <button 
        data-testid="next-page" 
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage >= totalPages}
      >
        Next
      </button>
    </div>
  ),
  TableDropdownMenu: ({ menuItems }) => (
    <div data-testid="table-dropdown-menu">
      <button data-testid="dropdown-trigger">Actions</button>
      <ul>
        {menuItems.map((item, index) => (
          <li key={index} data-testid={`menu-item-${index}`}>
            {item.label}
          </li>
        ))}
      </ul>
    </div>
  ),
}));

const mockActionToBeTakenData = {
  content: [
    {
      id: 1,
      noticeNumber: 'KLA/123/2022',
      secondaryMembers: [],
      starred: true,
      noticeHeading: 'Regarding public transport',
      ministerDesignation: 'Transport Minister',
      portfolio: 'Transport',
      questionDate: '2022-05-15',
      noticePriority: 'P1',
      status: 'TOBEASSIGNED'
    },
    {
      id: 2,
      noticeNumber: 'KLA/124/2022',
      secondaryMembers: ['Member1', 'Member2'],
      starred: false,
      noticeHeading: 'Regarding education',
      ministerDesignation: 'Education Minister',
      portfolio: 'Education',
      questionDate: '2022-05-16',
      noticePriority: 'P2',
      status: 'SUBMITTED'
    }
  ],
  totalElements: 2,
  totalPages: 1
};

const mockPortfolioData = [
  { id: 1, title: 'Transport' },
  { id: 2, title: 'Education' },
  { id: 3, title: 'Health' }
];

const mockDesignationData = [
  { id: 1, title: 'Transport Minister' },
  { id: 2, title: 'Education Minister' },
  { id: 3, title: 'Health Minister' }
];

const mockActiveAssembly = {
  assembly: '15',
  session: '15'
};

const createTestStore = () => configureStore({
  reducer: {
    activeAssembly: (state = {}) => state
  }
});

describe('ActionToBeTaken Component', () => {
  let store;
  
  beforeEach(() => {
    store = createTestStore();
    
    vi.spyOn(sectionStaffActions, 'useGetActionToBeTakenQuery').mockReturnValue({
      data: mockActionToBeTakenData,
      isLoading: false
    });
    
    vi.spyOn(masterDataManagement, 'useGetPortfolioQuery').mockReturnValue({
      data: mockPortfolioData
    });
    
    vi.spyOn(masterDataManagement, 'useGetDesignationQuery').mockReturnValue({
      data: mockDesignationData
    });
    
    vi.spyOn(activeAssembly, 'selectActiveAssemblyItems').mockReturnValue(mockActiveAssembly);

    window.history.pushState({}, '', '/');
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  const renderComponent = (searchParams = '') => {
    return render(
      <Provider store={store}>
        <MemoryRouter initialEntries={[`/?${searchParams}`]}>
          <Routes>
            <Route path="/" element={<ActionToBeTaken />} />
          </Routes>
        </MemoryRouter>
      </Provider>
    );
  };

  it('should render successfully with initial data', async () => {
    renderComponent();
    
    expect(screen.getByTestId('search-input')).toBeInTheDocument();
    expect(screen.getByTestId('filter-dropdown')).toBeInTheDocument();
    expect(screen.getByTestId('data-table')).toBeInTheDocument();

    expect(screen.getByTestId('data-count').textContent).toBe('2');
    expect(screen.getAllByTestId('table-row')).toHaveLength(2);
  });

  it('should show loading state', async () => {
    vi.spyOn(sectionStaffActions, 'useGetActionToBeTakenQuery').mockReturnValue({
      isLoading: true
    });
    
    renderComponent();
    
    expect(screen.getByTestId('spinner-loader')).toBeInTheDocument();
  });

  it('should apply search filter when typing', async () => {
    const user = userEvent.setup();
    renderComponent();
    
    const searchInput = screen.getByTestId('search-input');

    await user.type(searchInput, 'test search');

    fireEvent.change(searchInput, { target: { value: 'test search' } });
  
    const newParams = new URLSearchParams();
    newParams.set('search', 'test search');
    window.history.pushState({}, '', `/?${newParams.toString()}`);

    await waitFor(() => {
      const url = new URL(window.location.href);
      expect(url.searchParams.get('search')).toBe('test search');
    });
  });

  it('should clear search on Escape key', async () => {
    window.history.pushState({}, '', '/?search=initial');
    renderComponent('search=initial');
    
    const searchInput = screen.getByTestId('search-input');
    fireEvent.keyDown(searchInput, { key: 'Escape' });
 
    window.history.pushState({}, '', '/');
 
    await waitFor(() => {
      const url = new URL(window.location.href);
      expect(url.searchParams.get('search')).toBeNull();
    });
  });

  it('should change page when paginator is clicked', async () => {
    vi.spyOn(sectionStaffActions, 'useGetActionToBeTakenQuery').mockReturnValue({
      data: { ...mockActionToBeTakenData, totalPages: 3 },
      isLoading: false
    });
    
    renderComponent('page=1');
    
    const nextPageButton = screen.getByTestId('next-page');
    fireEvent.click(nextPageButton);

    const newParams = new URLSearchParams();
    newParams.set('page', '2');
    window.history.pushState({}, '', `/?${newParams.toString()}`);

    await waitFor(() => {
      const url = new URL(window.location.href);
      expect(url.searchParams.get('page')).toBe('2');
    });
  });

  it('should change page size when select is changed', async () => {
    renderComponent();
    
    const pageSizeSelect = screen.getByTestId('pagination-select');
    fireEvent.change(pageSizeSelect, { target: { value: '50' } });

    const newParams = new URLSearchParams();
    newParams.set('size', '50');
    newParams.set('page', '1');
    window.history.pushState({}, '', `/?${newParams.toString()}`);
    
    await waitFor(() => {
      const url = new URL(window.location.href);
      expect(url.searchParams.get('size')).toBe('50');
      expect(url.searchParams.get('page')).toBe('1');
    });
  });

  it('should apply filters when filter dropdown is used', async () => {
    renderComponent();
    
    const filterButton = screen.getByTestId('filter-dropdown-button');
    fireEvent.click(filterButton);
    
    const newParams = new URLSearchParams();
    newParams.set('status', 'SUBMITTED');
    newParams.set('page', '1');
    window.history.pushState({}, '', `/?${newParams.toString()}`);
    
    await waitFor(() => {
      const url = new URL(window.location.href);
      expect(url.searchParams.get('status')).toBe('SUBMITTED');
      expect(url.searchParams.get('page')).toBe('1');
    });
  });

  it('should initialize with default assembly and session filters', async () => {
    renderComponent();
    
    const filterDefaults = screen.getByTestId('filter-defaults');
    const defaultFilters = JSON.parse(filterDefaults.textContent);
    
    expect(defaultFilters).toHaveProperty('Assembly');
    expect(defaultFilters).toHaveProperty('Session');
    expect(defaultFilters.Assembly.value).toBe('15');
    expect(defaultFilters.Session.value).toBe('15');
  });

  it('should render status tags correctly', async () => {
    renderComponent();
    
    expect(screen.getByTestId('table-tag-return')).toBeInTheDocument();
    expect(screen.getByText('To be Assigned')).toBeInTheDocument();
    expect(screen.getByTestId('table-tag-approved')).toBeInTheDocument();
    expect(screen.getByText('Submitted')).toBeInTheDocument();
  });

  it('should render the correct table header columns', async () => {
    renderComponent();
    
    const expectedHeaders = [
      'Notice No.',
      'Clubbed',
      'Category',
      'Notice Heading',
      'Designation',
      'Portfolio',
      'Question Date',
      'Priority',
      'Status',
      'Action'
    ];
    
    expectedHeaders.forEach(header => {
      expect(screen.getByText(header)).toBeInTheDocument();
    });
  });

  it('should handle API error gracefully', async () => {
    vi.spyOn(sectionStaffActions, 'useGetActionToBeTakenQuery').mockReturnValue({
      error: new Error('API Error'),
      isLoading: false
    });
    
    renderComponent();
    
    expect(screen.getByTestId('search-input')).toBeInTheDocument();
    expect(screen.getByTestId('filter-dropdown')).toBeInTheDocument();
    
    expect(screen.getByTestId('data-count').textContent).toBe('0');
  });
});