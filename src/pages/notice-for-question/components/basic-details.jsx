import { AccordionTitle } from "@/components/accordion-title";
import {
  Badge,
  Button,
  Combobox,
  DatePicker,
  ExpandableContent,
  ExpandableTrigger,
  Input,
  toast,
} from "@kla-v2/ui-components";
import {
  Fragment,
  forwardRef,
  useImperativeHandle,
  useMemo,
  useCallback,
} from "react";
import PropTypes from "prop-types";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { useLanguage } from "@/hooks";
import { useForm, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Save } from "lucide-react";
import {
  useGetPrivateMemberBillsQuery,
  useGetPrivateMemberListQuery,
  useGetPrivateMemberResolutionsQuery,
} from "@/services/master-data-management/private-member";
import { Image } from "lucide-react";
import { basicDetailsFormFields } from "./form-fields";
import { BasicDetailsSchema } from "./form-schema";
import { resetFormWithResponseData, shouldShowTick } from "@/utils";
import { useSaveBasicDetailsMutation } from "@/services/notice-for-question-to-private-members";

const BasicDetailsAccordion = forwardRef(
  (
    {
      accordionOrderNo,
      getBasicDetails,
      documentId,
      openNext,
      userProfile,
      refetch,
    },
    ref
  ) => {
    const { t } = useLanguage();
    const [saveBasicDetails] = useSaveBasicDetailsMutation();

    const { data: getResolutions } = useGetPrivateMemberResolutionsQuery();
    const { data: getBills } = useGetPrivateMemberBillsQuery();
    const { data: getList } = useGetPrivateMemberListQuery();
    const formFields = basicDetailsFormFields(t);

    const form = useForm({
      resolver: zodResolver(BasicDetailsSchema(t)),
      shouldUnregister: false,
      defaultValues: {
        [formFields.SUBMITTED_BY.name]: userProfile,
      },
    });

    const { control, trigger, getValues, setValue } = form;

    useMemo(() => {
      if (getBasicDetails) {
        const formattedBasicDetails = {
          ...getBasicDetails,
          ministerDesignationId: getBasicDetails.ministerDesignationId,
          categoryId: getBasicDetails.categoryId?.toString(),
          memberId: getBasicDetails.member?.memberId?.toString(),
        };

        if (
          getBasicDetails.category === "Resolution" &&
          getBasicDetails.nameOfResolution
        ) {
          formattedBasicDetails.nameOfOthersCategory =
            getBasicDetails.resolutionId?.toString();
        } else if (
          getBasicDetails.category === "Bill" &&
          getBasicDetails.nameOfBill
        ) {
          formattedBasicDetails.nameOfOthersCategory =
            getBasicDetails.billId?.toString();
        } else if (getBasicDetails.category === "Others") {
          formattedBasicDetails.nameOfOthersCategory =
            getBasicDetails.nameOfOthersCategory;
        }

        form.reset(formattedBasicDetails);
      }
    }, [getBasicDetails, form]);

    useImperativeHandle(ref, () => ({
      async validateAndSubmit() {
        const isValid = await form.trigger();
        return isValid;
      },
      getValues: () => form.getValues(),
    }));

    const showTick = shouldShowTick(form);

    const resolutionOptions = useMemo(
      () =>
        (getResolutions ?? []).map((item) => ({
          label: item.name,
          value: item.id,
        })),
      [getResolutions]
    );

    const billOptions = useMemo(
      () =>
        (getBills ?? []).map((item) => ({ label: item.name, value: item.id })),
      [getBills]
    );

    const selectedDesignation = useWatch({
      control: form.control,
      name: "category",
    });

    const currentCategoryOptions = useMemo(() => {
      if (selectedDesignation === "Resolution") return resolutionOptions;
      if (selectedDesignation === "Bill") return billOptions;
      return [];
    }, [selectedDesignation, resolutionOptions, billOptions]);

    const inputLabel = useMemo(() => {
      if (selectedDesignation === "Bill") return "Name of Bill";
      if (selectedDesignation === "Others") return "Category Name";
      return "Name of Resolution";
    }, [selectedDesignation]);

    useMemo(() => {
      if (!selectedDesignation || !form.getValues("category")) return;
      const currentCategory = form.getValues("category");
      if (currentCategory !== selectedDesignation) {
        setValue("nameOfOthersCategory", null);
      }
    }, [selectedDesignation, setValue, form]);

    const prepareBasicDetails = useCallback(
      (data) => {
        const noticeType = "NOTICE_FOR_QUESTION_TO_PRIVATE_MEMBERS";
        const basicDetails = {
          type: noticeType,
          questionDate: data?.questionDate,
          category: data?.category,
          member: data?.member,
          submittedBy: data?.submittedBy ?? userProfile,
        };

        if (data?.category === "Others") {
          basicDetails.nameOfOthersCategory = data?.nameOfOthersCategory;
        } else if (data?.category === "Resolution") {
          const selectedResolution = resolutionOptions.find(
            (opt) => String(opt.value) === String(data?.nameOfOthersCategory)
          );
          basicDetails.resolutionId = data?.nameOfOthersCategory;
          basicDetails.nameOfResolution = selectedResolution?.label ?? null;
          basicDetails.nameOfOthersCategory = null;
        } else if (data?.category === "Bill") {
          const selectedBill = billOptions.find(
            (opt) => String(opt.value) === String(data?.nameOfOthersCategory)
          );
          basicDetails.billId = data?.nameOfOthersCategory;
          basicDetails.nameOfBill = selectedBill?.label ?? null;
          basicDetails.nameOfOthersCategory = null;
        }

        return basicDetails;
      },
      [userProfile, resolutionOptions, billOptions]
    );
    const handlePartialSave = async (e) => {
      e.preventDefault();

      try {
        const data = getValues();
        const basicDetails = prepareBasicDetails(data);

        const response = await saveBasicDetails({
          documentId,
          body: basicDetails,
        }).unwrap();

        const formattedResponse = {
          ...response,
          ministerDesignationId: response.ministerDesignationId?.toString(),
          categoryId: response.categoryId?.toString(),
          memberId: response.member?.memberId?.toString(),
        };

        if (response.category === "Resolution" && response.nameOfResolution) {
          formattedResponse.nameOfOthersCategory =
            response.resolutionId?.toString();
        } else if (response.category === "Bill" && response.nameOfBill) {
          formattedResponse.nameOfOthersCategory = response.billId?.toString();
        }

        resetFormWithResponseData(
          form,
          BasicDetailsSchema(t),
          formattedResponse
        );
        toast.success(t("toast.basicDetailsSave"));
        if (refetch) await refetch();
        openNext();
      } catch (error) {
        toast.error(
          error?.data?.detail ||
            error?.data?.title ||
            error?.message ||
            "Failed to save basic details"
        );
      }
    };

    return (
      <Fragment>
        <ExpandableTrigger showTick={showTick} variant="secondary">
          <AccordionTitle
            accordionOrderNo={accordionOrderNo}
            accordionTitle={t("form:basicDetails")}
          />
        </ExpandableTrigger>
        <ExpandableContent className="overflow-auto">
          <Form {...form} className="w-full">
            <form onSubmit={handlePartialSave} noValidate>
              <div className="py-4 px-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <FormField
                    control={control}
                    name={formFields.QUESTIONDATE.name}
                    render={({ field }) => (
                      <FormItem className="md:col-span-1">
                        <FormControl>
                          <DatePicker
                            {...field}
                            {...formFields.QUESTIONDATE}
                            onChange={(value) => {
                              field.onChange(value);
                              trigger(formFields.QUESTIONDATE.name);
                            }}
                            value={field.value}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={control}
                    name={formFields.CATEGORY.name}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Combobox
                            className="w-full"
                            {...field}
                            {...formFields.CATEGORY}
                            value={field.value ?? ""}
                            options={[
                              { label: "Others", value: "Others" },
                              { label: "Resolution", value: "Resolution" },
                              { label: "Bill", value: "Bill" },
                            ]}
                            onValueChange={(val) => {
                              setValue("nameOfOthersCategory", null);
                              field.onChange(val);
                              trigger(formFields.CATEGORY.name);
                            }}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={control}
                    name={formFields.CATEGORY_NAME.name}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          {selectedDesignation === "Others" ? (
                            <Input
                              {...field}
                              {...formFields.CATEGORY_NAME}
                              label={inputLabel}
                              value={field.value ?? ""}
                              onChange={(e) => {
                                field.onChange(e.target.value);
                                trigger(formFields.CATEGORY_NAME.name);
                              }}
                            />
                          ) : (
                            <Combobox
                              className="w-full"
                              label={t(inputLabel)}
                              value={field.value ?? ""}
                              options={currentCategoryOptions}
                              placeholder={t("Select")}
                              loading={
                                selectedDesignation === "Resolution"
                                  ? !getResolutions
                                  : selectedDesignation === "Bill"
                                  ? !getBills
                                  : false
                              }
                              onValueChange={(id) => {
                                field.onChange(id);
                                trigger(formFields.CATEGORY_NAME.name);
                              }}
                            />
                          )}
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                  <FormField
                    control={control}
                    name={formFields.MEMBER.name}
                    render={({ field }) => {
                      const members = getList ?? [];
                      const selectedMemberId = field.value?.memberId ?? "";
                      return (
                        <FormItem>
                          <FormControl>
                            <Combobox
                              className="w-full"
                              value={selectedMemberId}
                              options={members.map((member) => ({
                                label: member.memberDisplayName,
                                value: member.memberId,
                              }))}
                              {...formFields.MEMBER}
                              onValueChange={(val) => {
                                const selected = members.find(
                                  (m) => String(m.memberId) === String(val)
                                );
                                if (selected) {
                                  field.onChange({
                                    memberId: selected.memberId,
                                    memberDisplayName:
                                      selected.memberDisplayName,
                                    memberDisplayNameInLocal:
                                      selected.memberDisplayNameInLocal,
                                    constituencyId: selected.constituencyId,
                                    constituencyName: selected.constituencyName,
                                    constituencyNameInLocal:
                                      selected.constituencyNameInLocal,
                                    constituencyNumber:
                                      selected.constituencyNumber,
                                    politicalPartyId: selected.politicalPartyId,
                                    politicalPartyName:
                                      selected.politicalPartyName,
                                    politicalPartyNameInLocal:
                                      selected.politicalPartyNameInLocal,
                                  });
                                }
                                trigger(formFields.MEMBER.name);
                              }}
                            />
                          </FormControl>
                        </FormItem>
                      );
                    }}
                  />
                  <FormField
                    control={control}
                    name={formFields.SUBMITTED_BY.name}
                    render={() => (
                      <FormItem>
                        <FormControl>
                          <div className="flex flex-col items-start gap-3">
                            <label className="typography-body-text-m-14 text-grey-600 ">
                              Submitted By
                            </label>
                            <Badge
                              className="flex items-center gap-1 p-2"
                              size="sm"
                            >
                              <Image
                                src={`/images/${userProfile?.profileURL}`}
                                alt={userProfile?.displayName}
                                className=" rounded-full"
                              />
                              <span>{userProfile?.displayName}</span>
                              <span className="text-gray-500">
                                -{userProfile?.constituencyName}
                              </span>
                            </Badge>
                          </div>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
                <div className="flex justify-end mt-6">
                  <Button
                    type="submit"
                    variant="secondary"
                    className="flex items-center"
                  >
                    {t("save")}
                    <Save className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        </ExpandableContent>
      </Fragment>
    );
  }
);

BasicDetailsAccordion.displayName = "BasicDetailsAccordion";

BasicDetailsAccordion.propTypes = {
  accordionOrderNo: PropTypes.number,
  documentId: PropTypes.string,
  openNext: PropTypes.func,
  getBasicDetails: PropTypes.object,
  refetch: PropTypes.func.isRequired,
  userProfile: PropTypes.shape({
    profileURL: PropTypes.string,
    displayName: PropTypes.string,
    constituencyName: PropTypes.string,
  }),
};

export default BasicDetailsAccordion;
