import { z } from "zod";
import { basicDetailsFormFields } from "./form-fields";

export const BasicDetailsSchema = (t) => {
  const { QUESTIONDATE, CATEGORY, CATEGORY_NAME, MEMBER, SUBMITTED_BY } =
    basicDetailsFormFields(t);

  return z.object({
    [QUESTIONDATE.name]: z
      .string({ required_error: t("validation:questionDateRequired") })
      .min(1, t("validation:questionDateRequired")),

    [CATEGORY.name]: z
      .string({ required_error: t("validation:categoryRequired") })
      .min(1, t("validation:categoryRequired")),

    [CATEGORY_NAME.name]: z
      .string({ required_error: t("validation:categoryNameRequired") })
      .min(1, t("validation:categoryNameRequired")),

    [MEMBER.name]: z.any().refine((val) => val !== null && val !== undefined, {
      message: t("validation:memberRequired"),
    }),
    [SUBMITTED_BY.name]: z.object({
      memberId: z.number(),
      memberDisplayName: z.string(),
      memberDisplayNameInLocal: z.string(),
      constituencyName: z.string(),
      constituencyNameInLocal: z.string(),
      politicalPartyName: z.string(),
      politicalPartyNameInLocal: z.string(),
    }),
  });
};
