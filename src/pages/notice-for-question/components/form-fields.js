const basicDetailsFormFields = (t) => ({
  QUESTIONDATE: {
    name: "questionDate",
    label: t("form:questionDate"),
    placeholder: t("placeholder:questionDate"),
  },
  CATEGORY: {
    name: "category",
    label: t("form:category"),
    placeholder: t("placeholder:select"),
  },
  CATEGORY_NAME: {
    name: "nameOfOthersCategory",
    label: t("form:categoryName"),
    placeholder: t("placeholder:select"),
  },
  MEMBER: {
    name: "member",
    label: t("form:member"),
    placeholder: t("placeholder:member"),
  },
  SUBMITTED_BY: {
    name: "submittedBy",
    label: t("form:submittedBy"),
  },
});

export { basicDetailsFormFields };
