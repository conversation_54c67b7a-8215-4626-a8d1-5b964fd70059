import { useState } from "react";
import { NavLink, Outlet, useLocation, useNavigate } from "react-router-dom";
import { PlusIcon, Loader2 } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON>, toast } from "@kla-v2/ui-components";
import { useLanguage } from "@/hooks";
import { useCreateNoticeDraftMutation } from "@/services/question-notice";
import { useGetActiveAssemblyQuery } from "@/services/master-data-management/active-assembly";
import { CloseToastButton } from "@/components/ui/close-toast";
import { DOCUMENT_TYPES } from "@/constants/document-constants";
import { useAutoBreadcrumb } from "@/hooks/use-auto-breadcrumb";

function NoticeForQuestion() {
  const { t } = useLanguage();
  const location = useLocation();
  const navigate = useNavigate();
  const [isCreating, setIsCreating] = useState(false);
  const [createNoticeDraft] = useCreateNoticeDraftMutation();
  const { data: activeAssembly } = useGetActiveAssemblyQuery();

 useAutoBreadcrumb();

  /**
   * Handle document creation
   */
  const onSubmit = async () => {
    if (isCreating) return;

    setIsCreating(true);

    // Use active assembly data if available, otherwise use defaults
    const assembly = activeAssembly?.assembly || "15";
    const session = activeAssembly?.session || "13";

    const data = {
      type: DOCUMENT_TYPES.NOTICE_FOR_QUESTION,
      name: "Dhinesh", // TODO: Get user name from profile
      assembly,
      session,
    };

    try {
      const response = await createNoticeDraft(data);

      if (response?.data?.id) {
        const id = response.data.id;
        // Determine the route prefix based on the URL
        const routePrefix = location.pathname.includes("/ppo/")
          ? "/ppo/my-question-notices"
          : "/member/my-question-notices";

        toast.success(t("success"), {
          description: t("documentCreatedSuccessfully"),
        });
        navigate(`${routePrefix}/notice-for-question/${id}`);
      } else {
        handleError(new Error(t("noMatchingDocumentType")));
      }
    } catch (error) {
      handleError(error);
    } finally {
      setIsCreating(false);
    }
  };

  /**
   * Handle errors during document creation
   *
   * @param {Error} error - The error object
   */
  const handleError = (error) => {
    console.error("Error creating document:", error);

    toast.error(t("error"), {
      description: error?.data?.message || error?.message || t("failedToCreateDocument"),
      action: { label: <CloseToastButton /> },
    });
  };

  // Check if we're on the notice bank tab
  const isNoticeBank = location.pathname.includes("notice-bank");

  // Determine the current path prefix based on the URL
  const pathPrefix = location.pathname.includes("/ppo/")
    ? "/ppo/my-question-notices"
    : "/member/my-question-notices";

  return (
    <div className="px-4 py-3 mx-4 my-2 bg-white rounded-md">
      <div className="flex justify-between items-center">
        <h2 className="typography-page-heading">{t("noticeForQuestionList")}</h2>
        <Button
          variant="secondary"
          icon={isCreating ? Loader2 : PlusIcon}
          iconPosition="left"
          onClick={onSubmit}
          disabled={isCreating}
          className={isCreating ? "opacity-70" : ""}
        >
          {isCreating ? t("creating") : t("create")}
        </Button>
      </div>

      <div>
        <Tabs value={isNoticeBank ? "tab2" : "tab1"}>
          <TabsList variant="segmented">
            <NavLink to={pathPrefix} className={({ isActive }) => isActive && !isNoticeBank ? "active" : ""} data-testid="navlink--member-my-question-notices">
              <TabsTrigger value="tab1" variant="segmented">
                {t("myNotices")}
              </TabsTrigger>
            </NavLink>
            <NavLink to={`${pathPrefix}/notice-bank`} className={({ isActive }) => isActive && isNoticeBank ? "active" : ""} data-testid="navlink--member-my-question-notices-notice-bank">
              <TabsTrigger value="tab2" variant="segmented">
                {t("noticeBank")}
              </TabsTrigger>
            </NavLink>
          </TabsList>
        </Tabs>
        <Outlet />
      </div>
    </div>
  );
}

export default NoticeForQuestion;