import { useState, useEffect, useCallback } from "react";
import { useSearchParams } from "react-router-dom";
import PropTypes from "prop-types";
import { Pencil, Search, Delete, FileClock, Star } from "lucide-react";

import {
  DataTable,
  TableDropdownMenu,
  Input,
  FilterDropdown,
  PaginationSelect,
  Paginator,
} from "@kla-v2/ui-components";
import { useLanguage, useDebounce } from "@/hooks";
import { formatDateYYYYMMDD } from "@/utils";
import { useGetNoticeForQuestionListQuery } from "@/services/notice-for-question";
import {
  useGetPortfolioQuery,
  useGetDesignationQuery,
} from "@/services/master-data-management/basic-details-notice";

import { ClubbedCell } from "./components/clubbed-cell";
import SpinnerLoader from "@/utils/loaders/spinner-loader";

const ActionCell = ({ row }) => {
  const menuItems = [
    {
      label: (
        <div className="flex items-center gap-2">
          <Pencil size={16} /> Edit
        </div>
      ),
    },
    {
      label: (
        <div className="flex items-center gap-2">
          <Delete size={16} /> Delete
        </div>
      ),
    },
  ];

  return <TableDropdownMenu row={row} menuItems={menuItems} />;
};

ActionCell.propTypes = {
  row: PropTypes.shape({
    original: PropTypes.object.isRequired,
  }).isRequired,
};

const CategoryCell = ({ renderValue }) => {
  const value = renderValue();
  return value === "unStarred" ? (
    <Star size={17} />
  ) : (
    <Star size={17} fill="gold" color="gold" />
  );
};

CategoryCell.propTypes = {
  renderValue: PropTypes.func,
};

function ActionToBeTakenTab() {
  const { t } = useLanguage();
  const [searchParams, setSearchParams] = useSearchParams();
  const [filteredData, setFilteredData] = useState({});
  const [searchInput, setSearchInput] = useState(
    searchParams.get("search") || ""
  );
  const debouncedSearchValue = useDebounce(searchInput, 500);

  const page = parseInt(searchParams.get("page") || "1", 10) - 1;
  const size = parseInt(searchParams.get("size") || "10", 10);
  const searchQuery = searchParams.get("search") || "";

  const { data: portfolioData } = useGetPortfolioQuery();
  const { data: designationData } = useGetDesignationQuery();

  const extractFiltersFromParams = () => {
    const params = {};
    for (const [key, value] of searchParams.entries()) {
      if (!["page", "size", "search"].includes(key)) {
        params[key] = value;
      }
    }
    return params;
  };

  const filterParams = extractFiltersFromParams();

  const processFilters = (filters) => {
    const queryParams = {};

    Object.entries(filters).forEach(([key, filterData]) => {
      const { filter, value } = filterData;

      switch (key) {
        case "clubbed":
        queryParams["clubbed"] =
        typeof value === "object" && value !== null ? value.clubbed || "" : value || "";
        break;
      case "category":
        queryParams["category"] =
        typeof value === "object" && value !== null ? value.category || "" : value || "";
        break;
        case "members":
          if (value && value.length > 0) {
            queryParams["members"] = value.join(",");
          }
          break;
          case "ministerDesignation":
            queryParams["ministerDesignation"] =
            typeof value === "object" && value !== null ? value.Designation || "" : value || "";
            break;
          case "portfolio":
            queryParams["portfolio"] =
            typeof value === "object" && value !== null ? value.Portfolio || "" : value || "";
            break;
            case "noticePriority":
              queryParams["noticePriority"] =
              typeof value === "object" && value !== null ? value.Priority || "" : value || "";
              break;
            case "status":
              queryParams["status"] =
              typeof value === "object" && value !== null ? value.Status || "" : value || "";
              break;
        case "KLA Session":
          if (value && value.KLA && value.Session) {
            queryParams["kla"] = value.KLA;
            queryParams["session"] = value.Session;
          }
          break;
        case "createdDate":
          handleDateFilter(queryParams, filter, value, "createdDate");
          break;
        case "questionDate":
          handleDateFilter(queryParams, filter, value, "questionDate");
          break;
        default:
          break;
      }
    });

    return queryParams;
  };

  const handleDateFilter = (queryParams, filter, value, dateType) => {
    if (filter === "Is" && value) {
      queryParams[`${dateType}StartDate`] = formatDateYYYYMMDD(value);
      queryParams[`${dateType}EndDate`] = formatDateYYYYMMDD(value);
    } else if (filter === "between" && value) {
      if (value.from) {
        queryParams[`${dateType}StartDate`] = formatDateYYYYMMDD(value.from);
      }
      if (value.to) {
        queryParams[`${dateType}EndDate`] = formatDateYYYYMMDD(value.to);
      }
    }
  };

  const {
    data: noticeForQuestionData,
    isLoading: noticeForQuestionLoading,
    error: noticeForQuestionError,
  } = useGetNoticeForQuestionListQuery({
    search: searchQuery,
    ...processFilters(filteredData),
    page,
    size,
    ...filterParams,
  });

  const mapNoticeForQuestionData = useCallback((data) => {
    return (
      data?.content?.map((item) => ({
        id: item.id,
        noticeNumber: item.noticeNumber,
        clubbed:
          item.secondaryMembers && item.secondaryMembers.length > 0
            ? "Yes"
            : "No",
        secondaryMembers: item.secondaryMembers,
        category: item.starred ? "Starred" : "unStarred",
        noticeHeading: item.noticeHeading,
        ministerDesignation: item.ministerDesignation,
        portfolio: item.portfolio,
        questionDate: new Date(item.questionDate).toLocaleDateString(),
        noticePriority: item.noticePriority,
        status: item.status,
      })) || []
    );
  }, []);

  const noticeForQuestionTableData = mapNoticeForQuestionData(
    noticeForQuestionData
  );

  const updateParams = useCallback(
    (newParams) => {
      const updatedParams = new URLSearchParams(searchParams);

      if (
        Object.prototype.hasOwnProperty.call(newParams, "page") ||
        Object.prototype.hasOwnProperty.call(newParams, "size")
      ) {
        if (newParams.page !== undefined) {
          updatedParams.set("page", newParams.page.toString());
        }
        if (newParams.size !== undefined) {
          updatedParams.set("size", newParams.size.toString());
        }

        setSearchParams(updatedParams);
        return;
      }

      if (Object.prototype.hasOwnProperty.call(newParams, "search")) {
        if (newParams.search === "") {
          updatedParams.delete("search");
        } else {
          updatedParams.set("search", newParams.search);
        }
      }

      Object.entries(newParams).forEach(([key, value]) => {
        if (
          key !== "search" &&
          value !== undefined &&
          value !== null &&
          value !== ""
        ) {
          updatedParams.set(key, value.toString());
        }
      });

      updatedParams.set("page", "1");
      setSearchParams(updatedParams);
    },
    [searchParams, setSearchParams]
  );

  const handleSearchInputChange = (event) => {
    setSearchInput(event.target.value);
  };

  const handleSearchKeyDown = (event) => {
    if (event.key === "Escape") {
      setSearchInput("");
      updateParams({ search: "" });
    }
  };

  const handlePageChange = (newPage) => {
    updateParams({ page: newPage + 1 });
  };

  const handlePageSizeChange = (newSize) => {
    updateParams({ size: newSize, page: 1 });
  };

  useEffect(() => {
    if (debouncedSearchValue !== searchQuery) {
      updateParams({ search: debouncedSearchValue });
    }
  }, [debouncedSearchValue, searchQuery, updateParams]);

  const getDisplayedPageInfo = () => {
    const totalElements = noticeForQuestionData?.totalElements;
    if (!totalElements) return "";

    const startIndex = (page + 1) * size - (size - 1);
    const endIndex = Math.min((page + 1) * size, totalElements);

    return `Showing ${startIndex} - ${endIndex} of ${totalElements}`;
  };

  const getFilterOptions = () => {
    const memberOptions = [];

    const clubbedOptions = [
      { label: "Clubbed", value: "Clubbed" },
      { label: "Not Clubbed", value: "notClubbed" },
    ];

    const categoryOptions = [
      { label: "Starred", value: "Starred" },
      { label: "Unstarred", value: "unStarred" },
    ];

    const designationOptions =
      designationData?.map((designation) => ({
        label: designation.title,
        value: designation.title,
      })) || [];

    const portfolioOptions =
      portfolioData?.map((portfolio) => ({
        label: portfolio.title,
        value: portfolio.title,
      })) || [];

    const priorityOptions = [
      { label: "P1", value: "P1" },
      { label: "P2", value: "P2" },
      { label: "P3", value: "P3" },
      { label: "NIL", value: "NIL" },
    ];

    const statusOptions = [
      { label: "Draft", value: "DRAFT" },
      { label: "Submitted", value: "SUBMITTED" },
      { label: "Approved", value: "APPROVED" },
      { label: "Rejected", value: "REJECTED" },
    ];

    const klaOptions = [
      { label: "15", value: "15" },
      { label: "14", value: "14" },
      { label: "13", value: "13" },
    ];

    const sessionOptions = [
      { label: "15", value: "15" },
      { label: "14", value: "14" },
      { label: "13", value: "13" },
    ];

    return {
      memberOptions,
      clubbedOptions,
      categoryOptions,
      designationOptions,
      portfolioOptions,
      priorityOptions,
      statusOptions,
      klaOptions,
      sessionOptions,
    };
  };

  const {
    memberOptions,
    clubbedOptions,
    categoryOptions,
    designationOptions,
    portfolioOptions,
    priorityOptions,
    statusOptions,
    klaOptions,
    sessionOptions,
  } = getFilterOptions();

  const tableColumns = [
    {
      accessorKey: "noticeNumber",
      header: "Notice No.",
    },
    {
      accessorKey: "clubbed",
      header: "Clubbed",
      cell: ({ row }) => <ClubbedCell row={row} />,
    },
    {
      accessorKey: "category",
      header: "Category",
      cell: CategoryCell,
    },
    {
      accessorKey: "noticeHeading",
      header: "Notice Heading",
      meta: { className: "min-w-48" },
    },
    { accessorKey: "ministerDesignation", header: "Designation" },
    { accessorKey: "portfolio", header: "Portfolio" },
    {
      accessorKey: "questionDate",
      header: "Question Date",
      meta: { className: "min-w-44" },
    },
    { accessorKey: "noticePriority", header: "Priority" },
    {
      accessorKey: "status",
      header: "Status",
      meta: { className: "min-w-32" },
      cell: ({ row }) => {
        const status = row.original.status;
        let statusClass = "bg-gray-100 text-gray-800";

        if (status === "Not Taken") statusClass = "bg-warning-50 text-warning";
        if (status === "In Progress") statusClass = "bg-info-50 text-info";
        if (status === "Completed") statusClass = "bg-success-50 text-success";

        return (
          <div
            className={`px-2 py-1 rounded-full typography-body-text-r-14 text-center ${statusClass}`}
          >
            {status}
          </div>
        );
      },
    },
    {
      header: "Action",
      id: "action",
      cell: ({ row }) => <ActionCell row={row} />,
      meta: { className: "text-center w-[120px]" },
    },
  ];

  const noticeForQuestionFilterOptions = [
    {
      label: "Assembly Session",
      submenu: [
        {
          comboboxOptions: klaOptions,
          label: "Assembly",
          maxCount: 1,
          size: "lg",
          placeholder: "Select Assembly",
          truncateStringLength: 6,
          value: "KLA",
        },
        {
          comboboxOptions: sessionOptions,
          label: "Session",
          placeholder: "Select Session",
          truncateStringLength: 10,
          value: "Session",
        },
      ],
      type: "multipleComboboxSubmenu",
      value: "KLA Session",
    },
    {
      label: t("Clubbed"),
      submenu: [
        {
          comboboxOptions: clubbedOptions,
          label: "Clubbed",
          maxCount: 1,
          size: "lg",
          placeholder: "Select Clubbed or Not Clubbed",
          truncateStringLength: 6,
          value: "clubbed",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "clubbed",
    },
    {
      label: t("Category"),
      submenu: [
        {
          comboboxOptions: categoryOptions,
          label: "Category",
          maxCount: 1,
          size: "lg",
          placeholder: "Select Category",
          truncateStringLength: 6,
          value: "category",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "category",
    },
    {
      label: t("table:Members"),
      submenu: [
        {
          comboboxOptions: memberOptions,
          label: "Members",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Members",
          truncateStringLength: 6,
          value: "Members",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "members",
    },
    {
      label: t("table:Designation"),
      submenu: [
        {
          comboboxOptions: designationOptions,
          label: "Designation",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Designation",
          truncateStringLength: 6,
          value: "Designation",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "ministerDesignation",
    },
    {
      label: t("table:Portfolio"),
      submenu: [
        {
          comboboxOptions: portfolioOptions,
          label: "Portfolio",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Portfolio",
          truncateStringLength: 6,
          value: "Portfolio",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "portfolio",
    },
    {
      label: t("Created Date"),
      submenu: [
        {
          label: "Date is",
          type: "singleDate",
          value: "Is",
        },
        {
          label: "Date Range",
          type: "rangeDate",
          value: "between",
        },
      ],
      type: "radioDateSubmenu",
      value: "createdDate",
    },
    {
      label: t("Question Date"),
      submenu: [
        {
          label: "Date is",
          type: "singleDate",
          value: "Is",
        },
        {
          label: "Date Range",
          type: "rangeDate",
          value: "between",
        },
      ],
      type: "radioDateSubmenu",
      value: "questionDate",
    },
    {
      label: t("table:Priority"),
      submenu: [
        {
          comboboxOptions: priorityOptions,
          label: "Priority",
          maxCount: 3,
          size: "lg",
          placeholder: "Select Priority",
          truncateStringLength: 6,
          value: "Priority",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "noticePriority",
    },
    {
      label: "Status",
      submenu: [
        {
          comboboxOptions: statusOptions,
          icon: FileClock,
          label: "Status",
          maxCount: 1,
          placeholder: "Select Status",
          size: "lg",
          truncateStringLength: 6,
          value: "Status",
        },
      ],
      type: "singleComboboxSubmenu",
      value: "status",
    },
  ];

  const paginationOptions = [
    { label: "10 per page", value: "10" },
    { label: "50 per page", value: "50" },
    { label: "100 per page", value: "100" },
  ];

  const renderTableContent = () => {
    if (noticeForQuestionLoading) {
      return <SpinnerLoader />;
    }

    if (noticeForQuestionError) {
      return (
        <div className="flex justify-center py-8 text-red-500">
          <p>
            Error fetching notices:{" "}
            {noticeForQuestionError.message || "Unknown error"}
          </p>
        </div>
      );
    }

    if (noticeForQuestionTableData.length === 0) {
      return (
        <div className="flex justify-center py-8 text-gray-500">
          <p>No notices found. Try adjusting your search or filters.</p>
        </div>
      );
    }

    return (
      <>
        <DataTable columns={tableColumns} data={noticeForQuestionTableData} />

        <div className="flex justify-between mt-4">
          <div className="flex items-center gap-4">
            <span className="typography-body-text-s-14 text-grey-600">
              {getDisplayedPageInfo()}
            </span>
            <PaginationSelect
              onChange={handlePageSizeChange}
              defaultValue={size.toString()}
              options={paginationOptions}
            />
          </div>
          <div>
            <Paginator
              currentPage={page + 1}
              totalPages={noticeForQuestionData?.totalPages || 0}
              onPageChange={(newPage) => handlePageChange(newPage - 1)}
              showPreviousNext={true}
            />
          </div>
        </div>
      </>
    );
  };

  return (
    <>
      <div className="flex items-center justify-between mt-4">
        <div className="relative w-80">
          <Input
            icon={Search}
            className="w-full"
            reserveErrorSpace={false}
            placeholder="Search"
            size="md"
            value={searchInput}
            onChange={handleSearchInputChange}
            onKeyDown={handleSearchKeyDown}
          />
        </div>
        <div className="flex gap-2">
          <FilterDropdown
            badgeContainerClassName=""
            options={noticeForQuestionFilterOptions}
            onValueChange={(data) => {
              setFilteredData(data);
            }}
            disabled={noticeForQuestionLoading}
          />
        </div>
      </div>

      <div className="mt-4">{renderTableContent()}</div>
    </>
  );
}

ActionToBeTakenTab.propTypes = {
  currentTab: PropTypes.string,
};

export default ActionToBeTakenTab;
