import { useState, useRef, useEffect } from "react";
import { createPortal } from "react-dom";
import { UserRound, Users } from "lucide-react";
import PropTypes from "prop-types";

export const ClubbedCell = ({ row }) => {
  const isClubbed = row.original.clubbed === "Yes";
  const ministers = row.original.secondaryMembers || [];
  const [isHovered, setIsHovered] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const triggerRef = useRef(null);
  const hoverCardRef = useRef(null);

  useEffect(() => {
    if (isHovered && triggerRef.current) {
      const triggerRect = triggerRef.current.getBoundingClientRect();
      const cardHeight = hoverCardRef.current?.offsetHeight || 200;
      const cardWidth = 250;

      // Calculate available space below and above
      const spaceBelow = window.innerHeight - triggerRect.bottom;
      const spaceAbove = triggerRect.top;

      // Default position (below)
      let top = triggerRect.bottom + window.scrollY;
      let left =
        triggerRect.left +
        window.scrollX -
        cardWidth / 2 +
        triggerRect.width / 2;

      // If not enough space below, show above
      if (spaceBelow < cardHeight && spaceAbove > spaceBelow) {
        top = triggerRect.top + window.scrollY - cardHeight - 8;
      }

      // Adjust for left/right edges
      left = Math.max(8, Math.min(left, window.innerWidth - cardWidth - 8));

      setPosition({ top, left });
    }
  }, [isHovered]);

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  return (
    <div className="flex justify-center">
      {isClubbed ? (
        <div className="relative inline-block">
          <div
            ref={triggerRef}
            className="cursor-pointer"
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            <Users className="w-4 h-4 text-primary-600" />
          </div>

          {isHovered &&
            createPortal(
              <div
                ref={hoverCardRef}
                className="fixed z-[9999] p-3 bg-white border border-gray-200 rounded-md shadow-lg transition-opacity duration-200 w-64"
                style={{
                  top: `${position.top}px`,
                  left: `${position.left}px`,
                }}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
              >
                <div className="space-y-2">
                  {ministers.map((minister) => (
                    <div
                      key={minister.ministerId}
                      className="flex items-center"
                    >
                      <div className="flex-shrink-0 mr-3">
                        <img
                          src={`/images/ministers/${minister.ministerId}.jpg`}
                          alt={minister.ministerDisplayName}
                          className="object-cover w-8 h-8 rounded-full"
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.src = "/images/placeholder-minister.jpg";
                          }}
                        />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {minister.ministerDisplayName}
                        </p>
                        <p className="text-xs text-gray-500">
                          {minister.constituency || "No constituency data"}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>,
              document.body
            )}
        </div>
      ) : (
        <UserRound className="w-4 h-4 text-gray-500" />
      )}
    </div>
  );
};

ClubbedCell.propTypes = {
  row: PropTypes.shape({
    original: PropTypes.shape({
      clubbed: PropTypes.string,
      secondaryMembers: PropTypes.arrayOf(
        PropTypes.shape({
          ministerId: PropTypes.string.isRequired,
          ministerDisplayName: PropTypes.string.isRequired,
          constituency: PropTypes.string,
        })
      ),
    }).isRequired,
  }).isRequired,
};
