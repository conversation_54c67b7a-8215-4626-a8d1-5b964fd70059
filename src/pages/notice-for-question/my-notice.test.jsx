import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { MemoryRouter } from "react-router-dom";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import MyNotices from "../notice-for-question/my-notice";
 
const mockQuestionNoticeData = {
  content: [
    {
      id: "1",
      currentNumber: "N001",
      secondaryMembers: [],
      starred: true,
      noticeHeading: "Test Notice",
      ministerDesignation: "Minister of Test",
      ministerDesignationInLocal: "परीक्षण मंत्री",
      portfolio: "Test Portfolio",
      portfolioInLocal: "परीक्षण विभाग",
      createdDate: "2024-03-26T00:00:00Z",
      questionDate: "2024-03-27T00:00:00Z",
      noticePriority: "P1",
      status: "DRAFT",
    },
  ],
  totalElements: 1,
  totalPages: 1,
};
 
const mockUseGetQuestionNoticeListQuery = vi.fn(() => ({
  data: mockQuestionNoticeData,
  isLoading: false,
  error: null,
}));
 
const setSearchParamsMock = vi.fn();
let searchParamsMock = new URLSearchParams();
 
vi.mock("../../services/question-notice", () => ({
  useGetQuestionNoticeListQuery: () => mockUseGetQuestionNoticeListQuery(),
}));
 
vi.mock("../../services/master-data-management/basic-details-notice", () => ({
  useGetPortfolioQuery: vi.fn(() => ({
    data: [{ title: "Test Portfolio" }, { title: "Education" }],
  })),
  useGetDesignationQuery: vi.fn(() => ({
    data: [{ title: "Test Designation" }, { title: "Minister of Education" }],
  })),
}));
 
vi.mock("@/hooks", () => ({
  useLanguage: () => ({ t: (key) => key }),
  useDebounce: (value) => value,
}));
 
vi.mock("@/utils", () => ({
  formatDateYYYYMMDD: () => "2024-03-27",
  formatDate: () => "27-Mar-2024",
}));

vi.mock("@/utils/loaders/spinner-loader", () => ({
  default: () => <div>Loading notices...</div>,
}));
 
vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    useSearchParams: () => [searchParamsMock, setSearchParamsMock],
    useNavigate: () => vi.fn(),
  };
});

const mockActiveAssemblyItems = {
  assembly: "15",
  session: "14",
};

vi.mock("@/services/master-data-management/active-assembly", () => ({
  selectActiveAssemblyItems: () => mockActiveAssemblyItems,
}));
 
describe("MyNotices Component", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    searchParamsMock = new URLSearchParams();
    mockUseGetQuestionNoticeListQuery.mockReturnValue({
      data: mockQuestionNoticeData,
      isLoading: false,
      error: null,
    });
  });
 
  const renderComponent = () => {
    const store = configureStore({
      reducer: {
        mockReducer: (state = { activeAssembly: mockActiveAssemblyItems }) => state,
      },
      middleware: (getDefaultMiddleware) => 
        getDefaultMiddleware({
          thunk: {
            extraArgument: {
              selectActiveAssemblyItems: () => mockActiveAssemblyItems,
            },
          },
        }),
    });

    return render(
      <Provider store={store}>
        <MemoryRouter>
          <MyNotices />
        </MemoryRouter>
      </Provider>
    );
  };
 
  it("shows loading state", async () => {
    mockUseGetQuestionNoticeListQuery.mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
    });
 
    renderComponent();
 
    expect(screen.getByText("Loading notices...")).toBeInTheDocument();
  });
 
  it("shows error state", async () => {
    mockUseGetQuestionNoticeListQuery.mockReturnValue({
      data: null,
      isLoading: false,
      error: { message: "Failed to fetch data" },
    });
 
    renderComponent();
 
    await waitFor(() => {
      expect(screen.queryByText("Loading notices...")).not.toBeInTheDocument();
    });
  });
 
  it("shows empty state when no data is available", () => {
    mockUseGetQuestionNoticeListQuery.mockReturnValue({
      data: { content: [], totalElements: 0, totalPages: 0 },
      isLoading: false,
      error: null,
    });
 
    renderComponent();
    expect(screen.queryByText("Test Notice")).not.toBeInTheDocument();
  });
 
  it("handles page change", async () => {
    mockUseGetQuestionNoticeListQuery.mockReturnValue({
      data: {
        ...mockQuestionNoticeData,
        totalPages: 3,
      },
      isLoading: false,
      error: null,
    });
 
    renderComponent();
 
    await waitFor(() => {
      const paginationButtons = screen.getAllByRole("button");
      const nextPageButton = paginationButtons.find(button => 
        button.textContent === "Next" || button.textContent.includes("›"));
      
      if (nextPageButton) {
        fireEvent.click(nextPageButton);
      } else {
        const allButtons = screen.getAllByRole("button");
        fireEvent.click(allButtons[allButtons.length - 1]);
      }
    });
 
    expect(setSearchParamsMock).toHaveBeenCalled();
  });
 
  it("properly renders status cell with appropriate status tag", async () => {
    renderComponent();
 
    await waitFor(() => {
      const statusCells = screen.getAllByText("Draft");
      expect(statusCells.length).toBeGreaterThan(0);
    });
  });
 
  it("displays pagination information correctly", async () => {
    mockUseGetQuestionNoticeListQuery.mockReturnValue({
      data: {
        ...mockQuestionNoticeData,
        totalElements: 25,
        totalPages: 3,
      },
      isLoading: false,
      error: null,
    });
 
    renderComponent();
 
    await waitFor(() => {
      const paginationText = screen.getAllByText(/Showing 1 - 10 of 25/i);
      expect(paginationText.length).toBeGreaterThan(0);
    });
  });
 
  it("applies URL search params correctly", async () => {
    searchParamsMock = new URLSearchParams();
    searchParamsMock.set("search", "test query");
    searchParamsMock.set("page", "2");
    searchParamsMock.set("size", "50");
    searchParamsMock.set("status", "APPROVED");
 
    renderComponent();
 
    await waitFor(() => {
      const inputs = screen.getAllByRole("textbox");
      const searchInput = inputs.find(input => 
        input.getAttribute("placeholder") === "Search");
      
      if (searchInput) {
        expect(searchInput.value).toBe("test query");
      } else {
        expect(setSearchParamsMock).toHaveBeenCalled();
      }
    });
  });
});