import { describe, test, expect, vi, beforeEach } from "vitest";
import { formSchema } from "./form-schema";
import { getFormFields } from "./form-fields";

vi.mock("./form-fields", () => ({
  getFormFields: vi.fn()
}));

describe("formSchema function", () => {
  let tMock;
  
  beforeEach(() => {
    vi.resetAllMocks();
    
    tMock = vi.fn((key) => {
      const translations = {
        "validation:documentType": "Document type is required",
        "validation:documentName": "Document name is required"
      };
      return translations[key] || key;
    });
    
    getFormFields.mockReturnValue({
      DOCUMENT_TYPE: { name: "type" },
      DOCUMENT_NAME: { name: "name" }
    });
  });

  test("creates a valid schema that validates correctly", () => {
    const schema = formSchema(tMock);
    
    expect(getFormFields).toHaveBeenCalledWith(tMock);
    
    const validData = { type: "Some Type", name: "Some Name" };
    const validResult = schema.safeParse(validData);
    expect(validResult.success).toBe(true);
    expect(validResult.data).toEqual(validData);
  });
  
  test("rejects empty document type", () => {
    const schema = formSchema(tMock);
    
    const invalidData = { type: "", name: "Some Name" };
    const invalidResult = schema.safeParse(invalidData);
    
    expect(invalidResult.success).toBe(false);
    expect(invalidResult.error.errors[0].message).toBe("Document type is required");
  });
  
  test("rejects empty document name", () => {
    const schema = formSchema(tMock);
    
    const invalidData = { type: "Some Type", name: "" };
    const invalidResult = schema.safeParse(invalidData);
    
    expect(invalidResult.success).toBe(false);
    expect(invalidResult.error.errors[0].message).toBe("Document name is required");
  });
  
  test("rejects whitespace-only strings", () => {
    const schema = formSchema(tMock);
    
    const invalidData = { type: "   ", name: "Some Name" };
    const invalidResult = schema.safeParse(invalidData);
    
    expect(invalidResult.success).toBe(false);
    expect(invalidResult.error.errors[0].message).toBe("Document type is required");
  });
});