import { describe, test, expect } from "vitest";
import { getFormFields } from "./form-fields";

describe("getFormFields function", () => {
  test("returns correct form field structure with translations", () => {
    const tMock = (key) => {
      const translations = {
        "form:documentType": "Document Type",
        "form:documentName": "Document Name",
        "placeholder:documentType": "Select document type",
        "placeholder:documentName": "Enter document name"
      };
      return translations[key] || key;
    };

    const result = getFormFields(tMock);

    expect(result).toEqual({
      DOCUMENT_TYPE: {
        name: "type",
        label: "Document Type",
        placeholder: "Select document type",
      },
      DOCUMENT_NAME: {
        name: "name",
        label: "Document Name",
        placeholder: "Enter document name",
      }
    });
  });

  test("handles missing translations gracefully", () => {

    const tMock = (key) => key;

    const result = getFormFields(tMock);

    expect(result).toHaveProperty("DOCUMENT_TYPE");
    expect(result).toHaveProperty("DOCUMENT_NAME");
    
    expect(result.DOCUMENT_TYPE.name).toBe("type");
    expect(result.DOCUMENT_NAME.name).toBe("name");
    
    expect(result.DOCUMENT_TYPE.label).toBe("form:documentType");
    expect(result.DOCUMENT_NAME.placeholder).toBe("placeholder:documentName");
  });
});