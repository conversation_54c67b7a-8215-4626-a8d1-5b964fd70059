import { z } from "zod";
import { getFormFields } from "./form-fields";

const nonEmptyString = (errorMessage) =>
  z.string().trim().nonempty(errorMessage);

function formSchema(t) {
  const { DOCUMENT_TYPE, DOCUMENT_NAME } = getFormFields(t);

  const formSchemaObject = z.object({
    [DOCUMENT_TYPE.name]: nonEmptyString(t("validation:documentType")),

    [DOCUMENT_NAME.name]: nonEmptyString(t("validation:documentName")),
  });

  return formSchemaObject;
}

export { formSchema };
