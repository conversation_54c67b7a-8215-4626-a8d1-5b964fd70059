import PropTypes from "prop-types";
import { Users, User } from "lucide-react";
import { RotateCcw } from "lucide-react";
import { Checkbox, Button } from "@kla-v2/ui-components";
import { StarredIcon } from "@/icons/star-icon";
import { useDroppable } from "@dnd-kit/core";

const getPriorityBadgeStyle = (priority) => {
  switch (priority) {
    case "P1":
      return "border border-primary bg-blue-100 text-primary typography-body-text-s-11 w-[83px] h-8 flex items-center justify-center rounded-full";
    default:
      return "border bg-gray-100 text-gray-700 typography-body-text-s-11 w-[83px] h-8 flex items-center justify-center rounded-full";
  }
};

const SelectedBallotCard = ({
  selectedEntries,
  pendingNotices,
  onRemove,
  maxNotices = 30,
}) => {
  const totalSelectedNotices = selectedEntries ? selectedEntries.length : 0;

  const { setNodeRef } = useDroppable({
    id: "selected-ballot-dropzone",
  });

  const handleClear = () => {
    if (onRemove && typeof onRemove === "function") {
      selectedEntries?.forEach((entry) => onRemove(entry.id));
    }
  };

  const generateMockNotices = (entry, count = 3) => {
    if (entry && entry.noticeHeading) {
      return Array(count)
        .fill()
        .map((_, index) => ({
          id: `notice-${entry.id}-${index}`,
          text: entry.noticeHeading, 
          clubbed: entry.clubbed || index % 2 === 0 ? "false" : "true",
          starred: true,
          priority: ["P1", "P1", "P1"][index % 3],
        }));
    }
    if (pendingNotices && pendingNotices.length > 0) {
      return pendingNotices.slice(0, count).map((notice, index) => ({
        id: `notice-${entry.id}-${index}`,
        text: notice.noticeHeading || "Untitled Notice",
        clubbed: notice.clubbed || false ? "true" : "false",
        starred: true,
        priority: notice.priority || ["P1", "P1", "P1"][index % 3],
      }));
    }
    return Array(count)
      .fill()
      .map((_, index) => ({
        id: `notice-${entry.id}-${index}`,
        text: `Notice ${index + 1} for ${entry.memberDisplayName}`,
        clubbed: index % 2 === 0 ? "false" : "true",
        starred: true,
        priority: ["P1", "P1", "P1"][index % 3],
      }));
  };

  return (
    <div className="w-full p-2 selected-ballot-container">
      <div className="flex items-center justify-between mb-4 p-2">
        <div className="flex-1">
          <div className="text-primary typography-body-text-s-18 ">
            Setting Up
          </div>
          <p className="text-gray-400 text-sm mt-1">
            Drag and drop members into Question Setting UP
          </p>
        </div>
        <div className="flex items-center">
          <div className="text-gray-400 typography-body-text-s-18 mr-4">
            {totalSelectedNotices}/{maxNotices}
          </div>
          <Button
            variant="neutral"
            onClick={handleClear}
            className="flex items-center"
          >
            <RotateCcw size={16} className="text-gray-400" />
            <span className="mr-1">Clear</span>
          </Button>
        </div>
      </div>

      {!selectedEntries || selectedEntries.length === 0 ? (
        <div
          ref={setNodeRef}
          className="border rounded-lg h-80 min-h-[300px] w-full empty-state"
        ></div>
      ) : (
        <div ref={setNodeRef} className="w-full overflow-auto min-h-[300px]">
          {selectedEntries.map((entry, entryIndex) => (
            <div
              key={entry.id}
              className="mt-4 border rounded-lg overflow-hidden bg-white shadow-sm"
            >
              <div className="p-3 border-b bg-gray-50">
                <div className="flex items-center">
                  <div className="w-6 h-6 rounded-full bg-success-100 flex items-center justify-center text-primary-80 font-semibold text-sm mr-3">
                    {entryIndex + 1}
                  </div>
                  <div className="w-8 h-8 mr-3">
                    <img
                      src={"https://placehold.co/400"}
                      alt={entry.memberDisplayName}
                      className="h-13 w-14 rounded-full object-cover border"
                    />
                  </div>
                  <div className="flex-1 flex items-center">
                    <h3 className="font-medium mr-1">
                      {entry.memberDisplayName}
                    </h3>
                    <span className="text-xs text-gray-600">
                      {entry.constituencyName}
                    </span>
                    <span className="text-xs text-gray-600 mx-1"></span>
                    <span className="text-xs text-gray-600">
                      {entry.politicalPartyName}
                    </span>
                  </div>
                  <div className="ml-auto text-sm font-semibold">
                    {entry.totalNotices} Notices
                  </div>
                </div>
              </div>

              <div className="divide-y">
                {generateMockNotices(entry).map((notice) => (
                  <div key={notice.id} className="px-3 py-2 flex items-center">
                    <Checkbox className="mr-3" name={`notice-${notice.id}`} />
                    <div className="flex items-center mx-2">
                      {notice.clubbed === "false" ? (
                        <User size={17} />
                      ) : (
                        <Users size={17} />
                      )}
                    </div>
                    <div className="flex items-center flex-1">
                      <div className="mr-2">
                        <StarredIcon />
                      </div>
                      <span className="text-sm">{notice.text}</span>
                    </div>
                    <div className="flex items-center ml-auto">
                      <div
                        className={`px-3 py-1 rounded-full text-xs font-medium mr-2 text-primary ${getPriorityBadgeStyle(
                          notice.priority
                        )}`}
                      >
                        {notice.priority}
                      </div>
                      <span className="text-primary typography-body-text-s-11 cursor-pointer">
                        View
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

SelectedBallotCard.propTypes = {
  selectedEntries: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
      memberDisplayName: PropTypes.string,
      constituencyName: PropTypes.string,
      politicalPartyName: PropTypes.string,
      ballotOrder: PropTypes.number,
      totalNotices: PropTypes.number,
      noticeHeading: PropTypes.string, 
      clubbed: PropTypes.bool,
    })
  ),
  onRemove: PropTypes.func,
  pendingNotices: PropTypes.array,
  maxNotices: PropTypes.number,
};

SelectedBallotCard.defaultProps = {
  pendingNotices: [],
  onRemove: () => {},
  maxNotices: 30,
};

export default SelectedBallotCard;