import PropTypes from "prop-types";
import { useDraggable } from "@dnd-kit/core";

const DraggableItem = ({ entry, isEnabled }) => {
  const { attributes, listeners, setNodeRef, isDragging } = useDraggable({
    id: entry.id,
    data: {
      entry,
    },
    disabled: !isEnabled,
  });

  const style = isDragging ? { opacity:0.3 } : undefined;
  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`flex mt-2 p-2 mb-2 rounded border border-border-1 bg-white ${
        isEnabled
          ? "cursor-move hover:shadow-md transition-shadow"
          : "cursor-not-allowed opacity-50"
      }`}
      {...attributes}
      {...(isEnabled ? listeners : {})}
    >
      <div className="relative flex w-[54px] h-[60px]">
        <div className="absolute -top-3 -left-3 w-6 h-6 bg-white rounded-full flex items-center border-2 border-pink-500 justify-center text-black font-medium text-sm z-10 ml-1">
          {entry.ballotOrder}
        </div>
        <img
          src={"https://placehold.co/400"}
          alt={entry.memberDisplayName}
          className="h-14 w-14 rounded-lg object-cover border"
        />
      </div>
      <div className="flex flex-col ml-3 flex-1">
        <div className="flex justify-between items-start">
          <h4 className="text-gray-900 font-medium">{entry.memberDisplayName}</h4>
        </div>
        <span className="text-gray-600 text-sm">{entry.constituencyName}</span>
        <p className="text-gray-500 text-xs">{entry.politicalPartyName}</p>
      </div>
      <span className="text-xl font-bold mt-3">{entry.totalNotices}</span>
    </div>
  );
};

DraggableItem.propTypes = {
  entry: PropTypes.shape({
    id: PropTypes.string.isRequired,
    ballotOrder: PropTypes.number.isRequired,
    memberDisplayName: PropTypes.string.isRequired,
    constituencyName: PropTypes.string,
    politicalPartyName: PropTypes.string,
    totalNotices: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  }).isRequired,
  isEnabled: PropTypes.bool.isRequired,
};

const DraggableCardList = ({ groupEntries, selectedEntries }) => {
  if (!groupEntries || groupEntries.length === 0) {
    return <div>No ballot entries available</div>;
  }

  if (!Array.isArray(selectedEntries)) {
    console.error("selectedEntries is not an array:", selectedEntries);
    return <div>Error: Selected entries data is invalid</div>;
  }

  const availableEntries = groupEntries.filter((groupEntry) => {
    return !selectedEntries.some(
      (selectedEntry) => selectedEntry.id === groupEntry.id
    );
  });

  if (availableEntries.length === 0) {
    return (
      <div className="text-gray-500 text-center p-4">
        All entries have been assigned
      </div>
    );
  }

  const getNextBallotOrder = () => {
    const sortedOrders = [...availableEntries].sort(
      (a, b) => a.ballotOrder - b.ballotOrder
    );
    return sortedOrders[0].ballotOrder;
  };

  const nextBallotOrder = getNextBallotOrder();

  return (
    <div>
      {availableEntries.map((entry) => {
        const isEnabled = entry.ballotOrder === nextBallotOrder;
        return <DraggableItem key={entry.id} entry={entry} isEnabled={isEnabled} />;
      })}
    </div>
  );
};

DraggableCardList.propTypes = {
  groupEntries: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      ballotOrder: PropTypes.number.isRequired,
      memberDisplayName: PropTypes.string.isRequired,
      constituencyName: PropTypes.string,
      politicalPartyName: PropTypes.string,
      totalNotices: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    })
  ).isRequired,
  selectedEntries: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
    })
  ),
};

DraggableCardList.defaultProps = {
  selectedEntries: [],
};

export default DraggableCardList;
