import { DataTable } from "@kla-v2/ui-components";
import PropTypes from "prop-types";

export default function BallotResultTable({ ballotEntries, t }) {
  const columns = [
    { accessorKey: "ballotOrder", header: t("Ballot Order") },
    {
      accessorKey: "memberDisplayName",
      header: t("Members"),
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
           <img
            src={`/mdm-service/api/private-member-list/${row.original.id}/photo`}
            alt={row.original.memberDisplayName}
            className="w-8 h-8 rounded-full object-cover"
          />
          <span>{row.original.memberDisplayName}</span>
        </div>
      ),
    },
    { accessorKey: "constituencyName", header: t("Constituency") },
    { accessorKey: "politicalPartyName", header: t("Political Party") },
  ];

  return (
    <div className="w-full px-5 pb-2 flex flex-col h-full">
      <h3 className="typography-page-heading font-inter text-lg font-semibold mb-2 mt-2 p-1 flex-none">
        Balloting Result:
      </h3>
      <div className="flex-1 flex flex-col min-h-0">
        <div className="overflow-auto w-full h-full [-ms-overflow-style:'none'] [scrollbar-width:'none'] [&::-webkit-scrollbar]:hidden">
            <DataTable columns={columns} data={ballotEntries || []} stickyHeader={true} />
        </div>
      </div>
    </div>
  );
}

BallotResultTable.propTypes = {
  ballotEntries: PropTypes.array.isRequired,
  t: PropTypes.func.isRequired,
};