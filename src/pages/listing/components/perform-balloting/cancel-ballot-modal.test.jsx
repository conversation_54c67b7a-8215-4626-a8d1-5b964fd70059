import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import CancelBallotModal from './cancel-ballot-modal';

vi.mock('@/hooks', () => ({
  useLanguage: () => ({
    t: (key) => {
      const translations = {
        cancelBalloting: 'Cancel Balloting',
        chooseWorkFlowActions: 'Choose Workflow Actions',
        cancel: 'Cancel',
        proceed: 'Proceed'
      };
      return translations[key] || key;
    }
  })
}));

vi.mock('@/services/perform-balloting', () => ({
  useCancelBallotMutation: () => {
    const cancelBallot = vi.fn().mockReturnValue({
      unwrap: () => Promise.resolve({})
    });
    return [cancelBallot, { isLoading: false }];
  }
}));

vi.mock('@kla-v2/ui-components', async () => {
  const actual = await vi.importActual('@kla-v2/ui-components');
  return {
    ...actual,
    toast: {
      success: vi.fn(),
      error: vi.fn()
    }
  };
});

describe('CancelBallotModal', () => {
  const defaultProps = {
    isOpen: true,
    onClose: vi.fn(),
    ballotId: 'ballot-123',
    onCancelConfirm: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('renders correctly when open', () => {
    render(<CancelBallotModal {...defaultProps} />);

    expect(screen.getByText('Cancel Balloting')).toBeInTheDocument();
    expect(screen.getByText('Choose Workflow Actions')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Type Reason')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
    expect(screen.getByText('Proceed')).toBeInTheDocument();
  });

  it('does not render when isOpen is false', () => {
    const { container } = render(<CancelBallotModal {...defaultProps} isOpen={false} />);

    expect(container.querySelector('[role="dialog"]')).not.toBeInTheDocument();
  });

  it('disables Proceed button when reason is empty', () => {
    render(<CancelBallotModal {...defaultProps} />);

    const proceedButton = screen.getByText('Proceed');
    expect(proceedButton).toBeDisabled();
  });

  it('enables Proceed button when reason is entered', async () => {
    render(<CancelBallotModal {...defaultProps} />);

    const reasonInput = screen.getByPlaceholderText('Type Reason');
    await userEvent.type(reasonInput, 'Test reason');

    const proceedButton = screen.getByText('Proceed');
    expect(proceedButton).not.toBeDisabled();
  });

  it('clears reason input when modal is reopened', async () => {
    const { rerender } = render(<CancelBallotModal {...defaultProps} isOpen={false} />);

    rerender(<CancelBallotModal {...defaultProps} isOpen={true} />);
    const reasonInput = screen.getByPlaceholderText('Type Reason');
    await userEvent.type(reasonInput, 'Test reason');
    expect(reasonInput).toHaveValue('Test reason');

    rerender(<CancelBallotModal {...defaultProps} isOpen={false} />);
    rerender(<CancelBallotModal {...defaultProps} isOpen={true} />);

    const updatedReasonInput = screen.getByPlaceholderText('Type Reason');
    expect(updatedReasonInput).toHaveValue('');
  });

  it('calls onClose when Cancel button is clicked', async () => {
    render(<CancelBallotModal {...defaultProps} />);

    const cancelButton = screen.getByText('Cancel');
    await userEvent.click(cancelButton);

    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('calls cancelBallot with correct arguments when Proceed is clicked', async () => {
    render(<CancelBallotModal {...defaultProps} />);

    const reasonInput = screen.getByPlaceholderText('Type Reason');
    await userEvent.type(reasonInput, 'Test reason');

    const proceedButton = screen.getByText('Proceed');
    await userEvent.click(proceedButton);

    expect(defaultProps.onCancelConfirm).toHaveBeenCalledWith('Test reason');

  });

  it('shows success toast and calls onCancelConfirm when cancellation succeeds', async () => {
    render(<CancelBallotModal {...defaultProps} />);
    const reasonInput = screen.getByPlaceholderText('Type Reason');
    await userEvent.type(reasonInput, 'Test reason');
    const proceedButton = screen.getByText('Proceed');
    await userEvent.click(proceedButton);
  });

  it('shows error toast and calls onClose when cancellation fails', async () => {
    render(<CancelBallotModal {...defaultProps} />);
    const reasonInput = screen.getByPlaceholderText('Type Reason');
    await userEvent.type(reasonInput, 'Test reason');
    const proceedButton = screen.getByText('Proceed');
    await userEvent.click(proceedButton);
  });

  it('shows "Processing..." text when request is loading', async () => {
    render(<CancelBallotModal {...defaultProps} />);
  });
});