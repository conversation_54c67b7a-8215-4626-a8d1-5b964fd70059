import { render, screen, fireEvent } from "@testing-library/react";
import { vi } from "vitest";
import PerformBalloting from "./index";
import { <PERSON>rowserRouter } from "react-router-dom";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import * as performBallotingService from "@/services/perform-balloting";
import * as documentsService from "@/services/documents";
import { describe, test, beforeEach, expect, afterEach } from "vitest";

vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    useSearchParams: () => [
      new URLSearchParams({ questionDate: "2025-04-01" }),
      vi.fn(),
    ],
    useParams: () => ({ documentId: "12345" }),
    useNavigate: () => vi.fn(),
  };
});

vi.mock("@/hooks", () => ({
  useLanguage: () => ({ t: (key) => key }),
}));

// Mock the auto-breadcrumb hook - use vi.fn() directly in factory
vi.mock('@/hooks/use-auto-breadcrumb', () => ({
  useAutoBreadcrumb: vi.fn(),
}));

vi.mock("@kla-v2/ui-components", async () => {
  const PropTypes = await import("prop-types");

  const DatePicker = ({ onChange, value }) => (
    <input
      data-testid="date-picker"
      type="date"
      value={value || ""}
      onChange={(e) => onChange(new Date(e.target.value))}
    />
  );

  DatePicker.propTypes = {
    onChange: PropTypes.func.isRequired,
    value: PropTypes.string,
  };

  const Button = ({
    onClick,
    children,
    disabled,
    "data-testid": dataTestId,
  }) => (
    <button
      data-testid={
        dataTestId ||
        `button-${children.toString().replace(/\s+/g, "-").toLowerCase()}`
      }
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );

  Button.propTypes = {
    onClick: PropTypes.func.isRequired,
    children: PropTypes.node.isRequired,
    disabled: PropTypes.bool,
    "data-testid": PropTypes.string,
  };

  return {
    DatePicker,
    Button,
    toast: {
      error: vi.fn(),
    },
  };
});

vi.mock("@/components/document-metadata/index", () => ({
  DocumentMetadata: () => (
    <div data-testid="document-metadata">Document Metadata</div>
  ),
}));

vi.mock("@/icons/add-custom-icon", () => ({
  default: () => <div data-testid="add-custom-icon">Add Custom Icon</div>,
}));

vi.mock("./minister-card", () => ({
  default: ({ memberDisplayName }) => (
    <div data-testid="minister-card">{memberDisplayName}</div>
  ),
}));

vi.mock("./ballot-result-table", () => ({
  default: () => <div data-testid="ballot-result-table">Ballot Results</div>,
}));

vi.mock("./cancel-ballot-modal", () => ({
  default: ({ isOpen, onClose, onCancelConfirm }) =>
    isOpen ? (
      <div data-testid="cancel-ballot-modal">
        <button onClick={() => onCancelConfirm("Test cancellation reason")}>
          Confirm Cancel
        </button>
        <button onClick={onClose}>Close</button>
      </div>
    ) : null,
}));

const mockStore = configureStore({
  reducer: {
    breadcrumb: (state = {}, action) => {
      switch (action.type) {
        case "breadcrumb/setBreadcrumb":
          return { ...state, breadcrumbs: action.payload };
        case "breadcrumb/resetBreadcrumb":
          return { ...state, breadcrumbs: [] };
        default:
          return state;
      }
    },
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

vi.mock("@/services/perform-balloting", () => ({
  useGetBallotPerformListQuery: vi.fn(),
  usePerformBallotingMutation: vi.fn(),
  useGetRandomizedBallotResultQuery: vi.fn(),
  useCancelBallotMutation: vi.fn().mockReturnValue([
    vi.fn().mockResolvedValue({
      unwrap: () => Promise.resolve({})
    }),
    { isLoading: false }
  ]),
}));

vi.mock("@/services/documents", () => ({
  useGetMetadataDetailsQuery: vi.fn(),
}));

const renderWithProviders = (component) => {
  return render(
    <Provider store={mockStore}>
      <BrowserRouter>{component}</BrowserRouter>
    </Provider>
  );
};

describe("PerformBalloting Component", () => {
  beforeEach(() => {
    performBallotingService.useGetBallotPerformListQuery.mockReturnValue({
      data: [
        {
          id: "1",
          memberDisplayName: "John Doe",
          constituencyName: "North District",
          politicalPartyName: "Party A",
        },
        {
          id: "2",
          memberDisplayName: "Jane Smith",
          constituencyName: "South District",
          politicalPartyName: "Party B",
        },
      ],
    });

    documentsService.useGetMetadataDetailsQuery.mockReturnValue({
      data: {
        kla: 15,
        session: 3,
        documentType: "Ballot",
        createdOn: "2025-03-15",
        createdBy: "Admin",
      },
    });

    performBallotingService.usePerformBallotingMutation.mockReturnValue([
      vi.fn().mockResolvedValue({
        unwrap: () =>
          Promise.resolve({
            id: "ballot-123",
            ballotEntries: [
              {
                ballotOrder: 1,
                id: "1",
                memberDisplayName: "John Doe",
                constituencyName: "North District",
                politicalPartyName: "Party A",
              },
              {
                ballotOrder: 2,
                id: "2",
                memberDisplayName: "Jane Smith",
                constituencyName: "South District",
                politicalPartyName: "Party B",
              },
            ],
          }),
      }),
      { isLoading: false },
    ]);

    performBallotingService.useGetRandomizedBallotResultQuery.mockReturnValue({
      data: null,
      isSuccess: false,
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  test("renders component with initial state", () => {
    renderWithProviders(<PerformBalloting />);

    expect(screen.getByTestId("document-metadata")).toBeInTheDocument();
    expect(screen.getByTestId("date-picker")).toBeInTheDocument();
    expect(
      screen.getByText("Notice Submitted Members (2)")
    ).toBeInTheDocument();
  });

  test("shows cancel ballot modal and handles cancellation", async () => {
    performBallotingService.useGetRandomizedBallotResultQuery.mockReturnValue({
      data: {
        id: "ballot-123",
        ballotEntries: [
          { ballotOrder: 1, id: "1", memberDisplayName: "John Doe" },
          { ballotOrder: 2, id: "2", memberDisplayName: "Jane Smith" },
        ],
      },
      isSuccess: true,
    });

    renderWithProviders(<PerformBalloting />);
    const closeButton = screen.getByTestId("button-close");
    fireEvent.click(closeButton);
  });

  test("loads ballot results from URL params", async () => {
    vi.mock("react-router-dom", async () => {
      const actual = await vi.importActual("react-router-dom");
      return {
        ...actual,
        useSearchParams: vi.fn().mockReturnValue([
          new URLSearchParams({
            questionDate: "2025-04-01",
            ballotId: "12345",
          }),
        ]),
      };
    });

    performBallotingService.useGetRandomizedBallotResultQuery.mockReturnValue({
      data: {
        id: "ballot-123",
        ballotEntries: [
          { ballotOrder: 1, id: "1", memberDisplayName: "John Doe" },
          { ballotOrder: 2, id: "2", memberDisplayName: "Jane Smith" },
        ],
      },
      isSuccess: true,
    });

    renderWithProviders(<PerformBalloting />);
  });
});