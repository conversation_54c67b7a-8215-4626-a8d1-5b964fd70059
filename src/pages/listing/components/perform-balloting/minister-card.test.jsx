import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import MinisterCard from './minister-card';

describe('MinisterCard', () => {
  const defaultProps = {
    memberDisplayName: '<PERSON>',
    constituencyName: 'North District',
    politicalPartyName: 'Progressive Party',
    id: '12345'
  };

  beforeEach(() => {
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('renders the MinisterCard with all required props', () => {
    render(<MinisterCard {...defaultProps} />);

    expect(screen.getByText('<PERSON>')).toBeDefined();
    expect(screen.getByText('North District')).toBeDefined();
    expect(screen.getByText('Progressive Party')).toBeDefined();

    const imageElement = screen.getByAltText('<PERSON>');
    expect(imageElement).toBeDefined();
  });

  it('handles different prop values correctly', () => {
    const newProps = {
      memberDisplayName: '<PERSON>',
      constituencyName: 'South District',
      politicalPartyName: 'Conservative Party',
      id: '67890'
    };

    render(<MinisterCard {...newProps} />);

    expect(screen.getByText('Jane Smith')).toBeDefined();
    expect(screen.getByText('South District')).toBeDefined();
    expect(screen.getByText('Conservative Party')).toBeDefined();
    
  });

  it('applies correct styling to elements', () => {
    render(<MinisterCard {...defaultProps} />);

    const container = screen.getByText('John Doe').closest('div').parentElement;
    expect(container.className).toContain('flex mt-2 p-2 mb-2 rounded border border-border-1 bg-white');

    const nameElement = screen.getByText('John Doe');
    expect(nameElement.className).toContain('text-gray-950 text-sm font-medium');
    
    const constituencyElement = screen.getByText('North District');
    expect(constituencyElement.className).toContain('text-slate-600 text-xs font-medium mt-1');
    
    const partyElement = screen.getByText('Progressive Party');
    expect(partyElement.className).toContain('text-gray-500 text-xs font-normal mt-1');
  });
});