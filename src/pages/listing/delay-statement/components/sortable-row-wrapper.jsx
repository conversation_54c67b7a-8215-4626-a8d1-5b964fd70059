import { DndContext, closestCenter } from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { GripHorizontal } from "lucide-react";
import { cn } from "@/utils";
import {
  Table,
  TableBody,
  TableCell,
  TableDropdownMenu,
  TableHead,
  TableHeader,
  TableRow,
} from "@kla-v2/ui-components";
import PropTypes from "prop-types";
import { useState } from "react";
import RemovefromList from "./remove-from-list";
import AddDelayStatement from "./add-delay-statement";

import PreviewSubmit from "./preview-modal";
import { useEffect } from "react";
import { useReorderDelayStatementEntryMutation } from "@/services/delay-statement";
import { useLanguage } from "@/hooks";

const Tag = ({ text, highlight = false }) => (
  <span
    className={`inline-block px-2 py-1 text-sm rounded-full ${
      highlight ? "bg-pink-500 text-white" : "bg-gray-200"
    }`}
  >
    {text}
  </span>
);
Tag.propTypes = {
  text: PropTypes.string.isRequired,
  highlight: PropTypes.bool,
};

function SortableRow({ id, row, docId, data }) {
  const [isPreview, setIsPreview] = useState(false);
  const [isRemove, setIsRemove] = useState(false);
  const [isAdd, setIsAdd] = useState(false);
  const { t } = useLanguage();

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    setActivatorNodeRef,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };
  const documentMeta = [
    {
      metadata: {
        assembly: "13",
        session: "15",
        documentType: "Delay Statement",
        currentNo: "fd",
        createdOn: "3948",
        createdBy: "",
        name: "Delay Statement",
      },
    },
  ];

  const onSubmit = async () => {
    try {
      setIsPreview(true);
    } catch (error) {
      console.error("Submission Error:", error);
    }
  };
  const handleRemove = async () => {
    try {
      setIsRemove(true);
    } catch (error) {
      console.error("Submission Error:", error);
    }
  };
  const ActionCell = () => {
    const menuItems = [
      {
        label: <div className="flex items-center gap-2">{t("table:view")}</div>,
        onClick: () => onSubmit(),
      },
      {
        label: (
          <div className="flex items-center gap-2">
            {t("table:removefromList")}
          </div>
        ),
        onClick: () => {
          handleRemove();
        },
      },
    ].filter(Boolean);

    return <TableDropdownMenu menuItems={menuItems} />;
  };

  return (
    <>
      <TableRow ref={setNodeRef} style={style} className="relative group">
        <TableCell className="relative border px-4 py-2 text-center">
          {row.assembly}
          <div
            ref={setActivatorNodeRef}
            {...attributes}
            {...listeners}
            className={cn(
              "absolute -left-2 top-1/2 -translate-y-1/2 z-1000",
              "w-4 h-10 flex items-center justify-center",
              "bg-white border border-gray-300 rounded-full shadow-sm",
              "text-gray-400 cursor-grab"
            )}
          >
            <GripHorizontal size={14} className="" />
          </div>
        </TableCell>
        <TableCell className="border px-4 py-2 text-center">
          {row.session}
        </TableCell>
        <TableCell className="border px-4 py-2">
          <div className="flex flex-wrap gap-1">
            <span
              onClick={() => setIsPreview(true)}
              className={cn(
                "bg-grey-100 text-black hover:bg-secondary hover:text-white px-2 py-1 rounded-full text-sm cursor-pointer transition-colors"
              )}
            >
              {row?.questionNumber}
            </span>
          </div>
        </TableCell>
        <TableCell className="border px-4 py-2 text-center">
          <ActionCell />
        </TableCell>
      </TableRow>
      <PreviewSubmit
        isOpen={isPreview}
        onClose={() => setIsPreview(false)}
        metaData={documentMeta[0].metadata}
        modalData={data}
      />
      <RemovefromList
        isOpen={isRemove}
        onClose={() => setIsRemove(false)}
        documentId={id}
        entryId={docId}
      />
      <AddDelayStatement isOpen={isAdd} onClose={() => setIsAdd(false)} />
    </>
  );
}

export default function DraggableTable({ data, docId, tableData }) {
  const [items, setItems] = useState(data);
  const [entries, setEntries] = useState(data?.delayStatementEntries || []);
  const { t } = useLanguage();

  useEffect(() => {
    setItems(data);
  }, [data]);

  useEffect(() => {
    setEntries(data.delayStatementEntries || []);
  }, [data]);

  const [ReorderEntry] = useReorderDelayStatementEntryMutation();
  const handleDragEnd = async (event) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = entries.findIndex((i) => i.id === active.id);
      const newIndex = entries.findIndex((i) => i.id === over?.id);
      const newEntries = arrayMove(entries, oldIndex, newIndex);

      setEntries(newEntries);

      const payload = {
        ministerWiseLayingListId: data.id,
        delayStatementEntries: newEntries.map((entry, index) => ({
          id: entry.id,
          order: index + 1,
        })),
        documentId: docId,
      };

      await ReorderEntry(payload).unwrap();
    }
  };

  return (
    <div className="overflow-x-auto">
      <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
        <SortableContext
          items={items?.delayStatementEntries?.map((i) => i.id)}
          strategy={verticalListSortingStrategy}
        >
          <Table className="table-auto border-collapse border-spacing-0 w-full">
            <TableHeader className="bg-blue-500 text-white">
              <TableRow>
                <TableHead className="border px-4 py-2">നിയമസഭ</TableHead>
                <TableHead className="border px-4 py-2">സമ്മേളനം</TableHead>
                <TableHead className="border px-4 py-2">ചോദ്യ നമ്പർ</TableHead>
                <TableHead className="border px-4 py-2">
                  {t("table:actions")}
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {entries.map((row) => (
                <SortableRow
                  key={row.id}
                  id={row.id}
                  row={{
                    ...row,
                    assembly: data.assembly,
                    session: data.session,
                  }}
                  docId={docId}
                  data={tableData}
                />
              ))}
            </TableBody>
          </Table>
        </SortableContext>
      </DndContext>
    </div>
  );
}

DraggableTable.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      assembly: PropTypes.string.isRequired,
      session: PropTypes.string.isRequired,
      questions: PropTypes.arrayOf(PropTypes.string),
    })
  ).isRequired,
  docId: PropTypes.string,
  tableData: PropTypes.node,
};
SortableRow.propTypes = {
  id: PropTypes.string.isRequired,
  docId: PropTypes.string,
  data: PropTypes.node,
  row: PropTypes.shape({
    assembly: PropTypes.number,
    session: PropTypes.number,
    questionNumber: PropTypes.arrayOf(PropTypes.string),
  }).isRequired,
};
