import { useLanguage } from "@/hooks";
import { useAddDelayStatementEntryMutation } from "@/services/delay-statement";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Button,
  DataTable,
  Dialog,
  DialogClose,
  Dialog<PERSON>ontent,
  <PERSON>alogFooter,
  DialogHeader,
  DialogPortal,
  DialogTitle,
  FileUploader,
  Input,
  RadioGroup,
  RadioGroupItem,
  TableCheckbox,
  toast,
  Toaster,
} from "@kla-v2/ui-components";
import { Eye } from "lucide-react";
import { addDelayStatementList } from "../../../../../mock/db/add-delay-list";
import PropTypes from "prop-types";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

function AddDelayStatement({ isOpen, onClose }) {
  const { t } = useLanguage();
  const [mode, setMode] = useState("list");
  const [AddEntry] = useAddDelayStatementEntryMutation();
  const [uploadedFiles, setUploadedFiles] = useState([]);

  const delayStatementSchema = z.object({
    assembly: z.string().min(1, "KLA is required"),
    session: z.string().min(1, "Session is required"),
    questionNumber: z.string().min(1, "Question number is required"),
    noticeForQuestionTitle: z.string().min(1, "Heading is required"),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(delayStatementSchema),
    defaultValues: {
      assembly: "",
      session: "",
      questionNumber: "",
      noticeForQuestionTitle: "",
    },
  });

  const onSubmit = async (data) => {
    const id = "d1fdd7a6-9e83-4c14-9c03-8f45ec58d8ad";
    const payload = {
      assembly: Number(data.assembly),
      session: Number(data.session),
      questionNumber: Number(data.questionNumber),
      noticeForQuestionTitle: data.noticeForQuestionTitle,
    };

    try {
      await AddEntry({ id, data: payload }).unwrap();
      toast.success("Delay Statement Entry Added");
    } catch (err) {
      console.error(err);
      toast.error("Failed to Add Entry");
    }
  };

  return (
    <div>
      <Dialog open={isOpen} onOpenChange={onClose} className="overflow-hidden">
        <DialogPortal>
          <DialogContent className="max-w-[980px]  flex flex-col">
            <DialogHeader>
              <DialogTitle>
                <p
                  className="typography-page-heading mb-2"
                  data-testid="preview-submit-popup"
                >
                  {t("addItemstoDelayStatementList")}
                </p>
                <p className="typography-body-text-r-14 mb-2">
                  {t("selectbelowitemstoaddinDelayStatementlist")}
                </p>
              </DialogTitle>
            </DialogHeader>
            <div className="flex-1 h-auto overflow-y-auto">
              <div className="flex items-center space-x-2 ">
                <RadioGroup
                  value={mode}
                  onValueChange={setMode}
                  className="flex items-center space-x-6 py-3"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="list" id="list" />
                    <label htmlFor="list">{t("list")}</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="manual" id="manual" />
                    <label htmlFor="manual">{t("manual")}</label>
                  </div>
                </RadioGroup>
              </div>

              <div>
                {mode === "list" ? (
                  <DataTable
                    columns={[
                      {
                        id: "switch",
                        header: () => <span>Serial No.</span>,
                        cell: ({ row }) => (
                          <div className="flex items-center gap-2">
                            <TableCheckbox id={`row-${row.index}`} />
                            <label htmlFor={`row-${row.index}`}>
                              {String(row.index + 1).padStart(2, "0")}
                            </label>
                          </div>
                        ),
                        meta: {
                          className: "w-[150px]",
                        },
                      },

                      {
                        accessorKey: "kla",
                        header: t("kla"),
                        meta: {
                          className: "w-[150px]",
                        },
                      },
                      {
                        accessorKey: "klaSession",
                        header: t("table:klaSession"),
                      },
                      {
                        accessorKey: "questionHeading",
                        header: t("table:questionHeading"),
                      },
                      {
                        cell: () => {
                          <Eye className="text-grey-500 bg-grey" />;
                        },
                        header: "",
                        id: "action",
                        meta: {
                          className: "text-center w-[30px]",
                        },
                      },
                    ]}
                    data={addDelayStatementList}
                  />
                ) : (
                  <form onSubmit={handleSubmit(onSubmit)} className="">
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <Input
                          label={t("kla")}
                          maxLength={2}
                          placeholder="Enter KLA Here"
                          {...register("assembly")}
                        />
                        {errors.assembly && (
                          <p className="text-error-500 text-sm">
                            {errors.assembly.message}
                          </p>
                        )}
                      </div>

                      <div>
                        <Input
                          label={t("session")}
                          maxLength={2}
                          placeholder="Enter Session Here"
                          {...register("session")}
                        />
                        {errors.session && (
                          <p className="text-error-500 text-sm">
                            {errors.session.message}
                          </p>
                        )}
                      </div>

                      <div>
                        <Input
                          label={t("questionNumber")}
                          placeholder="Question Number"
                          {...register("questionNumber")}
                        />
                        {errors.questionNumber && (
                          <p className="text-error-500 text-sm">
                            {errors.questionNumber.message}
                          </p>
                        )}
                      </div>

                      <div>
                        <Input
                          label={t("questionHeading")}
                          placeholder="Type Heading Here"
                          {...register("noticeForQuestionTitle")}
                        />
                        {errors.noticeForQuestionTitle && (
                          <p className="text-error-500 text-sm">
                            {errors.noticeForQuestionTitle.message}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="w-[900px]">
                      <Toaster />
                      <FileUploader
                        accept={{
                          "application/pdf": [],
                        }}
                        labelText={t("uploadAnswers&DelayStatement")}
                        maxFileCount={10}
                        maxSize={26214400}
                        value={uploadedFiles}
                        onChange={setUploadedFiles}
                        onError={(err) => toast.error(err.message)}
                        onUpload={() => {}}
                      />
                    </div>
                  </form>
                )}
              </div>
            </div>
            <DialogFooter className="flex">
              <DialogClose asChild>
                <div className="flex justify-between gap-2">
                  <Button variant="neutral" size="sm">
                    <span>{t("cancel")}</span>
                  </Button>
                  <Button
                    size="sm"
                    variant="primary"
                    // disabled={true}
                    onClick={handleSubmit}
                  >
                    {mode === "list" ? t("addSelected") : t("addToList")}
                  </Button>
                </div>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </div>
  );
}
AddDelayStatement.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  submitDocument: PropTypes.func,
  documentId: PropTypes.string.isRequired,
  metaData: PropTypes.object.isRequired,
  children: PropTypes.node,
};
export default AddDelayStatement;
