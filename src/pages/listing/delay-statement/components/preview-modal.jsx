import { DocumentMetadata } from "@/components/document-metadata";
import {
  <PERSON><PERSON>,
  <PERSON>alog,
  DialogClose,
  Dialog<PERSON>ontent,
  <PERSON><PERSON>Footer,
  DialogHeader,
  DialogPortal,
  DialogTitle,
  ExpandableAccordion,
  ExpandableContent,
  ExpandableHeader,
  ExpandableItem,
  toast,
} from "@kla-v2/ui-components";
import PropTypes from "prop-types";
import { useLanguage, useUserProfile } from "@/hooks";
import { XIcon } from "lucide-react";
import { useState } from "react";
import ArrowRight from "@/icons/arrow-right-icon";
import ReturnModal from "./return-modal";

const PreviewSubmit = ({
  isOpen,
  onClose,
  documentId,
  submitDocument,
  metaData,
  modalData,
}) => {
  const { t } = useLanguage();
  const [accordionValue, setAccordionValue] = useState([]);
  const [isReturn, setIsReturn] = useState(false);
  const { userProfile } = useUserProfile();

  const showErrorToast = (errorMessage) => {
    toast.error("Error submitting document", {
      description: errorMessage,
      action: {
        label: <XIcon className="size-5 text-grey-400 hover:text-foreground" />,
      },
    });
  };

  const handleSubmit = async () => {
    try {
      await submitDocument({ documentId });
      onClose();
    } catch (err) {
      showErrorToast(err?.message);
    }
  };

  const handleRemove = async () => {
    try {
      setIsReturn(true);
    } catch (error) {
      console.error("Submission Error:", error);
    }
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose} className="overflow-hidden">
        <DialogPortal>
          <DialogContent className="max-w-[1080px] h-[700px] flex flex-col gap-2">
            <DialogHeader>
              <DialogTitle>
                <p
                  className="typography-page-heading mb-4"
                  data-testid="preview-submit-popup"
                >
                  {t("preview")}
                </p>
                <p className="typography-sub-title-heading mb-4">
                  {metaData?.name}
                </p>
                <div className="h-12">
                  <DocumentMetadata documentMetadata={metaData} />
                </div>
              </DialogTitle>
            </DialogHeader>
            <div className="bg-background flex-1 h-auto overflow-y-auto">
              <div>
                <ExpandableAccordion
                  type="multiple"
                  value={accordionValue}
                  onValueChange={setAccordionValue}
                  className="w-full flex flex-col gap-4 flex-grow px-4 py-4"
                >
                  <ExpandableItem value="item-1">
                    <ExpandableHeader index={1} label="Basic Details" />
                    <ExpandableContent>
                      <div className="w-full flex flex-col gap-6 ">
                        {modalData.ministerWiseLayingDocuments.map(
                          (minister) => (
                            <div
                              key={minister.id}
                              className="flex flex-col gap-6"
                            >
                              {minister.delayStatementEntries.map((entry) => (
                                <div
                                  key={entry.id}
                                  className="p-6 bg-muted/50 rounded-md shadow-sm"
                                >
                                  {/* First Row: 3 Columns */}
                                  <div className="grid grid-cols-3 gap-x-8 gap-y-6">
                                    <div>
                                      <div className="text-gray-500 typography-body-text-r-14">
                                        {t("kla")}
                                      </div>
                                      <div className="text-black typography-body-text-r-14">
                                        {minister.assembly}
                                      </div>
                                    </div>
                                    <div>
                                      <div className="text-gray-500 typography-body-text-r-14">
                                        {t("session")}
                                      </div>
                                      <div className="text-black typography-body-text-r-14">
                                        {minister.session}
                                      </div>
                                    </div>
                                    <div>
                                      <div className="text-gray-500 typography-body-text-r-14">
                                        {t("questionNo")}
                                      </div>
                                      <div className="text-black typography-body-text-r-14">
                                        {entry.questionNumber}
                                      </div>
                                    </div>
                                    <div>
                                      <div className="text-gray-500 typography-body-text-r-14">
                                        {t("dateofRegistration")}
                                      </div>
                                      <div className="text-black typography-body-text-r-14">
                                        {entry.noticeForQuestionTitle}
                                      </div>
                                    </div>
                                    <div>
                                      <div className="text-gray-500 typography-body-text-r-14">
                                        {t("questionDate")}
                                      </div>
                                      <div className="text-black typography-body-text-r-14">
                                        {entry.questionDate}
                                      </div>
                                    </div>
                                    <div>
                                      <div className="text-gray-500 typography-body-text-r-14">
                                        {t("designation")}
                                      </div>
                                      <div className="text-black typography-body-text-r-14">
                                        {minister.designation}
                                      </div>
                                    </div>
                                  </div>

                                  <div className="grid grid-cols-2 gap-x-8 gap-y-6 mt-6">
                                    <div>
                                      <div className="text-gray-500 typography-body-text-r-14">
                                        {t("ministerSubject/Portfolio")}
                                      </div>
                                      <div className="text-black typography-body-text-r-14">
                                        {minister.ministerSubject}
                                      </div>
                                    </div>
                                    <div>
                                      <div className="text-gray-500 typography-body-text-r-14">
                                        {t("ministerSubSubject")}
                                      </div>
                                      <div className="text-black typography-body-text-r-14">
                                        {minister.ministerSubSubject}
                                      </div>
                                    </div>
                                  </div>
                                  <div className="text-gray-500 typography-body-text-r-14">
                                    {t("nameofMember")}
                                  </div>
                                  <div className="grid grid-cols-2 gap-x-8 gap-y-6 mt-6">
                                    {minister.nameofMember.map(
                                      (memberName, index) => (
                                        <div
                                          key={index}
                                          className="flex items-center gap-2 rounded-full border px-3 py-1 shadow-sm bg-white w-[270px]"
                                        >
                                          <img
                                            src={userProfile?.profileURL}
                                            alt={memberName}
                                            className="w-8 h-8 rounded-full"
                                          />
                                          <span className="text-gray-500 typography-body-text-r-14">
                                            {userProfile?.memberDisplayName}
                                          </span>
                                          <span className="text-gray-500 typography-body-text-r-14">
                                            • {userProfile?.constituencyName}
                                          </span>
                                        </div>
                                      )
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          )
                        )}
                      </div>
                    </ExpandableContent>
                  </ExpandableItem>

                  <ExpandableItem value="item-2">
                    <ExpandableHeader index={2} label={t("questionDetails")} />
                    <ExpandableContent></ExpandableContent>
                  </ExpandableItem>
                </ExpandableAccordion>
              </div>
            </div>

            <DialogFooter className="flex">
              <DialogClose asChild>
                <Button size="md" variant="secondary" onClick={handleSubmit}>
                  {t("close")}
                </Button>
              </DialogClose>
              <DialogClose asChild>
                <Button variant="primary" size="md" onClick={handleRemove}>
                  <span>{t("return")}</span>
                  <ArrowRight size={24} />
                </Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </DialogPortal>
      </Dialog>
      <ReturnModal isOpen={isReturn} onClose={() => setIsReturn(false)} />
    </>
  );
};

PreviewSubmit.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  submitDocument: PropTypes.func,
  documentId: PropTypes.string,
  metaData: PropTypes.object.isRequired,
  children: PropTypes.node,
  modalData: PropTypes.node,
  ministerWiseLayingDocuments: PropTypes.node,
};

export default PreviewSubmit;
