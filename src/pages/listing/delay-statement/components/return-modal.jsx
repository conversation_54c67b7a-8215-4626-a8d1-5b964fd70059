import { useLanguage } from "@/hooks";
import PropTypes from "prop-types";
import {
  <PERSON>ton,
  <PERSON>alog,
  DialogClose,
  <PERSON>alog<PERSON>ontent,
  DialogFooter,
  DialogHeader,
  DialogPortal,
  DialogTitle,
  Textarea,
} from "@kla-v2/ui-components";
import { useState } from "react";

function ReturnModal({ isOpen, onClose }) {
  const [isReturn, setIsReturn] = useState("");
  const { t } = useLanguage();

  return (
    <div>
      <Dialog open={isOpen} onOpenChange={onClose} className="overflow-hidden">
        <DialogPortal>
          <DialogContent className="max-w-[640px]  flex flex-col gap-2">
            <DialogHeader>
              <DialogTitle>
                <p
                  className="typography-page-heading mb-4"
                  data-testid="preview-submit-popup"
                >
                  {t("Return Answered Question")}
                </p>
                <p className="typography-body-text-r-14 mb-4">
                  {t("Are you sure you want to Forward the Document?")}
                </p>
              </DialogTitle>
            </DialogHeader>
            <div className="flex-1 h-auto overflow-y-auto">
              <label className="flex items-center gap-1 mb-1 text-sm font-medium text-gray-700">
                {t("Add Reason")}
                <button
                  type="button"
                  className="text-grey-600 hover:text-grey-800"
                  title="Explain the reason for returning this document"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-4 h-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M12 20c4.418 0 8-3.582 8-8s-3.582-8-8-8-8 3.582-8 8 3.582 8 8 8z"
                    />
                  </svg>
                </button>
              </label>
              <Textarea
                placeholder="Type your reason here."
                className="h-[100px]"
                value={isReturn}
                onChange={(e) => setIsReturn(e.target.value)}
              />
            </div>

            <DialogFooter className="flex justify-center">
              <DialogClose asChild>
                <div className="flex  gap-2">
                  <Button variant="neutral" size="md">
                    <span>{t("cancel")}</span>
                  </Button>
                  <Button variant="primary" size="md">
                    <span>{t("return")}</span>
                  </Button>
                </div>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </div>
  );
}

export default ReturnModal;
ReturnModal.propTypes = {
  isOpen: PropTypes.bool,
  onClose: PropTypes.func,
};
