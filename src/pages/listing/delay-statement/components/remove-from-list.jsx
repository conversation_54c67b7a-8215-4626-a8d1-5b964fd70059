import { useLanguage } from "@/hooks";
import { useRemoveDelayStatementEntryMutation } from "@/services/delay-statement";
import {
  Button,
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogPortal,
  DialogTitle,
  Textarea,
} from "@kla-v2/ui-components";
import PropTypes from "prop-types";
import { useState } from "react";

function RemovefromList({ isOpen, onClose, entryId, documentId }) {
  const { t } = useLanguage();
  const [removeDelayEntry, { isLoading }] =
    useRemoveDelayStatementEntryMutation();
  const [reason, setReason] = useState("");

  const handleConfirm = () => {
    if (!entryId || !documentId || !reason) {
      console.error("Missing entryId, documentId or reason.");
      return;
    }
    removeDelayEntry(
      {
        id: entryId,
        documentId,
        reason,
      },
      {
        onSuccess: () => {
          console.log(`Removed entry ${entryId} from document ${documentId}`);
          onClose();
        },
        onError: (error) => {
          console.error("Failed to remove entry:", error);
        },
      }
    );
  };
  return (
    <div>
      <Dialog open={isOpen} onOpenChange={onClose} className="overflow-hidden">
        <DialogPortal>
          <DialogContent className="max-w-[640px]  flex flex-col">
            <DialogHeader>
              <DialogTitle>
                <p
                  className="typography-page-heading mb-4"
                  data-testid="preview-submit-popup"
                >
                  {t("removefromList")}
                </p>
                <p className="typography-body-text-r-14 mb-4">
                  {t("layingListDeleteConfirmationMessage")}
                </p>
              </DialogTitle>
            </DialogHeader>
            <div className="flex-1 h-auto overflow-y-auto">
              <Textarea
                label={t("reason")}
                placeholder="Type your reason here."
                className="h-[100px]"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
              />
            </div>
            <DialogFooter className="flex">
              <DialogClose asChild>
                <div className="flex justify-between gap-2">
                  <Button variant="neutral" size="sm">
                    <span>{t("cancel")}</span>
                  </Button>
                  <Button
                    size="sm"
                    variant="primary"
                    onClick={handleConfirm}
                    disabled={isLoading}
                  >
                    {isLoading ? t("removing") : t("confirm")}
                  </Button>
                </div>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </div>
  );
}
RemovefromList.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  submitDocument: PropTypes.func,
  documentId: PropTypes.string.isRequired,
  metaData: PropTypes.object.isRequired,
  children: PropTypes.node,
  entryId: PropTypes.string,
  rowid: PropTypes.string,
};
export default RemovefromList;
