import PropTypes from "prop-types";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import ClauseCard from "./clause-cards";

export default function ClauseCardsList({
  clauses,
  onClauseTextChange,
  onDeleteClause,
}) {
  return (
    <SortableContext
      items={clauses.map((c) => c.id)}
      strategy={verticalListSortingStrategy}
    >
      <div className="flex flex-col gap-2 overflow-y-auto max-h-[500px] items-center">
        {clauses
          .sort((a, b) => a.order - b.order) 
          .map((clause, index) => (
            <ClauseCard
              key={clause.id}
              clause={clause}
              index={index}
              onClauseTextChange={onClauseTextChange}
              onDeleteClause={onDeleteClause}
            />
          ))}
      </div>
    </SortableContext>
  );
}

ClauseCardsList.propTypes = {
  clauses: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      text: PropTypes.string.isRequired,
    })
  ).isRequired,
  onClauseTextChange: PropTypes.func.isRequired,
  onAddClause: PropTypes.func,
  onDeleteClause: PropTypes.func.isRequired,
};
