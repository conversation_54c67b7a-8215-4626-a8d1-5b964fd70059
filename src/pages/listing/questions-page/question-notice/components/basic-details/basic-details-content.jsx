import { useState, useContext, useEffect } from "react";
import {
  Form,
  FormField,
  FormItem,
} from "../../../../../../components/ui/form";
import {
  <PERSON><PERSON>,
  DatePicker,
  Combobox,
  toast,
  MultiSelectDropdown,
} from "@kla-v2/ui-components";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { BasicDetailsSchema } from "@/components/ui/form/form-schema";
import { LanguageContext } from "../../../../../../components/languages/language-context";
import { Save } from "lucide-react";
import { useCreateBasicDetailsMutation } from "@/services/question";
import { NoticeContext } from "../../index";
import {
  useGetDesignationQuery,
  useGetPortfolioQuery,
  useGetSubSubjectQuery,
  useGetConsentMembersQuery,
} from "@/services/master-data-management/basic-details-notice";

const BasicDetailsPageContent = () => {
  const { t } = useContext(LanguageContext);
  const contextValue = useContext(NoticeContext);
  const formSchema = BasicDetailsSchema(t);
  const [createQuestionNotice] = useCreateBasicDetailsMutation();
  const [designation, setDesignation] = useState([]);
  const [portfolios, setPortfolios] = useState([]);
  const [subSubjects, setSubSubjects] = useState([]);
  const [ministers, setMinisters] = useState([]);
  const id = contextValue.id;
  const { data: designationData } = useGetDesignationQuery();
  const { data: portfolioData } = useGetPortfolioQuery();
  const { data: subjectData } = useGetSubSubjectQuery();
  const { data: memberData } = useGetConsentMembersQuery();

  useEffect(() => {
    if (designationData) {
      const formattedDesignations = designationData.map((item) => ({
        label: item.title,
        value: item.id.toString(),
      }));
      setDesignation(formattedDesignations);
    }
  }, [designationData]);

  useEffect(() => {
    if (portfolioData) {
      const formattedPortfolios = portfolioData.map((item) => ({
        label: item.title,
        value: item.id.toString(),
      }));
      setPortfolios(formattedPortfolios);
    }
  }, [portfolioData]);

  useEffect(() => {
    if (subjectData) {
      const formattedSubject = subjectData.map((item) => ({
        label: item.title,
        value: item.id.toString(),
      }));
      setSubSubjects(formattedSubject);
    }
  }, [subjectData]);

  useEffect(() => {
    if (memberData) {
      const formattedMembers = memberData.map((item) => ({
        label: item.memberDisplayName,
        value: item.memberId,
        group: item.constituencyName,
        subLabel: item.politicalPartyName,
        selected: false,
      }));
      setMinisters(formattedMembers);
    }
  }, [memberData]);

  const form = useForm({
    mode: "onSubmit",
    resolver: zodResolver(formSchema),
    defaultValues: contextValue.basicDetails,
  });

  const { handleSubmit } = form;

  const onSubmit = async (data) => {
    if (!data.members || data.members.length === 0) {
      toast.error("Error", {
        description: "Please select at least one member.",
      });
      return;
    }

    if (data.members.length > 4) {
      toast.error("Error", {
        description:
          "You can only select up to 4 members (1 Primary + 3 Secondary).",
      });
      return;
    }

    const selectedMembers = memberData.filter((item) =>
      data.members.includes(item.memberId)
    );

    if (!selectedMembers[0]) {
      toast.error("Error", {
        description: "Invalid member selection.",
      });
      return;
    }

    const primaryMember = {
      memberId: selectedMembers[0].memberId,
      memberDisplayName: selectedMembers[0].memberDisplayName,
      memberDisplayNameInLocal: selectedMembers[0].memberDisplayNameInLocal,
      constituencyName: selectedMembers[0].constituencyName,
      constituencyNameInLocal: selectedMembers[0].constituencyNameInLocal,
      politicalPartyName: selectedMembers[0].politicalPartyName,
      politicalPartyNameInLocal: selectedMembers[0].politicalPartyNameInLocal,
    };

    const secondaryMembers =
      selectedMembers.length > 1
        ? selectedMembers.slice(1, 4).map((m) => ({
            memberId: m.memberId,
            memberDisplayName: m.memberDisplayName,
            memberDisplayNameInLocal: m.memberDisplayNameInLocal,
            constituencyName: m.constituencyName,
            constituencyNameInLocal: m.constituencyNameInLocal,
            politicalPartyName: m.politicalPartyName,
            politicalPartyNameInLocal: m.politicalPartyNameInLocal,
          }))
        : [];

    const updatedBasicDetails = {
      ...contextValue.basicDetails,
      questionDate: data.questionDate,
      ministerDesignationId: data.designation,
      portfolioId: data.ministerPortfolio,
      subSubjectId: data.ministerSubSubject,
      primaryMember,
      secondaryMembers,
    };

    contextValue.setBasicDetails(updatedBasicDetails);

    try {
      const response = await createQuestionNotice({
        id,
        data: updatedBasicDetails,
      }).unwrap();

      if (response) {
        toast.success("Success", {
          description: response?.message || "Details added successfully",
        });

        contextValue.setIsBasicDetailsComplete(true);
        contextValue.setAccordionValue((prev) =>
          prev.filter((item) => item !== "item-1")
        );
      } else {
        toast.error("Error", {
          description: response?.message || "An error occurred",
        });
      }
    } catch (error) {
      console.error("Submission failed:", error);
      toast.error("Error", {
        description: "Failed to submit data.",
      });
    }
  };

  return (
    <div className="py-4 px-6">
      <Form {...form} className="w-full">
        <form onSubmit={handleSubmit(onSubmit)} className="w-full space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Question Date */}
            <FormField
              control={form.control}
              name="questionDate"
              render={({ field, fieldState }) => (
                <FormItem className="md:col-span-1">
                  <DatePicker
                    {...field}
                    label={t("Question Date")}
                    placeholder={t("Select Date")}
                    onChange={field.onChange}
                    value={field.value}
                    error={fieldState.error?.message}
                  />
                </FormItem>
              )}
            />

            {/* Designation */}
            <FormField
              control={form.control}
              name="designation"
              render={({ fieldState }) => (
                <FormItem className="md:col-span-2">
                  <Combobox
                    className="w-full min-w-[500px]"
                    label={t("Designation")}
                    value={form.watch("designation") || ""}
                    onValueChange={form.setValue.bind(null, "designation")}
                    options={designation} // Use formatted options
                    placeholder={t("Select")}
                    error={fieldState.error?.message}
                  />
                </FormItem>
              )}
            />

            {/* Minister Portfolio */}
            <FormField
              control={form.control}
              name="ministerPortfolio"
              render={({ field, fieldState }) => (
                <FormItem className="md:col-span-1">
                  <Combobox
                    className="w-full min-w-[100px]"
                    label={t("Minister Portfolio")}
                    value={field.value || ""}
                    onValueChange={field.onChange}
                    options={portfolios} // Use formatted options
                    placeholder={t("Select")}
                    error={fieldState.error?.message}
                  />
                </FormItem>
              )}
            />

            {/* Minister Sub Subject */}
            <FormField
              control={form.control}
              name="ministerSubSubject"
              render={({ field, fieldState }) => (
                <FormItem className="md:col-span-1">
                  <Combobox
                    label={t("Minister Sub Subject (Optional)")}
                    value={field.value || ""}
                    onValueChange={field.onChange}
                    options={subSubjects}
                    placeholder={t("Select")}
                    error={fieldState.error?.message}
                  />
                </FormItem>
              )}
            />

            {/* Name of Member(s) */}
            <FormField
              control={form.control}
              name="members"
              render={({ field, fieldState }) => (
                <FormItem className="md:col-span-2">
                  <label className="typography-body-text-m-14 text-grey-600">
                    {t("Name of Member(s)")}
                  </label>
                  <MultiSelectDropdown
                    options={ministers}
                    onSelectionChange={(selected) => {
                      field.onChange(selected.map((m) => m.value));
                    }}
                    addLabel="Add Members"
                    maxCount={3}
                  />

                  {fieldState.error?.message && (
                    <p className="text-red-500 text-sm">
                      {fieldState.error.message}
                    </p>
                  )}
                </FormItem>
              )}
            />
          </div>

          {/* Add Members Button */}

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button
              type="submit"
              variant="secondary"
              className="flex items-center"
            >
              Save <Save className="w-4 h-4" />
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default BasicDetailsPageContent;
