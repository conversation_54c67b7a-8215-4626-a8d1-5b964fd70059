import PropTypes from "prop-types";
import { useContext } from "react";
import { ExpandableContent, ExpandableTrigger } from "@kla-v2/ui-components";
import { AccordionTitle } from "@/components/accordion-title";
import BasicDetailsPageContent from "./basic-details-content";
import { NoticeContext } from "../../index";

const BasicDetailsPage = ({
  accordionOrderNo,
}) => {
  const contextValue = useContext(NoticeContext);

  return (
    <>
      <ExpandableTrigger
        showTick={contextValue.isBasicDetailsComplete}
        className="text-primary font-semibold text-base"
        variant="secondary"
      >
        <AccordionTitle
          accordionOrderNo={accordionOrderNo}
          accordionTitle="Basic Details"
          isColored={true}
        />
      </ExpandableTrigger>
      <ExpandableContent>
        <div className="w-full flex flex-col">
          <BasicDetailsPageContent />
        </div>
      </ExpandableContent>
    </>
  );
};

BasicDetailsPage.propTypes = {
  accordionOrderNo: PropTypes.number,
};

export { BasicDetailsPage };
