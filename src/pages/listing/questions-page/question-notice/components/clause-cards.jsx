import PropTypes from "prop-types";
import { GripVertical } from "lucide-react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Textarea } from "@kla-v2/ui-components";
import CornerIcon from "@/icons/corner-icon";
import CompareIcon from "@/icons/compare-icon";
import DeleteClauseIcon from "@/icons/delete-icon";

const ClauseCard = ({
  clause,
  index,
  onClauseTextChange,
  onDeleteClause,
  isReadOnly,
}) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({
      id: clause.id,
      disabled: isReadOnly,
    });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const handleTextChange = (e) => {
    const text = e.target.value;
    if (text.trim().split(/\s+/).length <= 75) {
      onClauseTextChange(index, text);
    } else {
      const truncatedText = text.split(/\s+/).slice(0, 75).join(' ');
      onClauseTextChange(index, truncatedText);
      e.target.value = truncatedText;
    }
  };
  
  return (
    <div
      ref={setNodeRef}
      style={style}
      className="relative border border-gray-300 rounded-lg text-gray-500 text-sm focus-within:border-blue-500 w-[99%] flex justify-center overflow-visible p-3"
    >
      <div className="absolute left-[-6px] top-1/2 -translate-y-1/2 flex items-center">
        {!isReadOnly && (
          <div
            {...listeners}
            {...attributes}
            className="text-gray-400 cursor-grab"
            style={{
              display: "flex",
              width: "16px",
              padding: "9px 0px",
              justifyContent: "center",
              alignItems: "center",
              borderRadius: "32px",
              border: "1px solid var(--Border-Colors-Border_1 ,)",
              background: "var(--Base-White-Color, white)",
            }}
          >
            <GripVertical size={16} />
          </div>
        )}
      </div>

      <span className="absolute top-5 left-5 text-black font-bold">
        ({clause.id})
      </span>
      <Textarea
        className="w-full border-none focus:outline-none resize-none text-black font-bold text-sm p-2 px-10"
        rows="4"
        value={clause.text}
        onChange={handleTextChange}
      />

      <div className="absolute bottom-3 right-3 flex items-center gap-2">
        <CornerIcon />
        <div className="text-gray-500 text-sm">
          {clause.text.trim().split(/\s+/).filter(word => word).length}/75
        </div>
      </div>

      <div className="absolute top-3 right-3 flex items-center gap-2">
        <CompareIcon />
        {!isReadOnly && (
          <button onClick={() => onDeleteClause(clause.id)} type="button">
            <DeleteClauseIcon />
          </button>
        )}
      </div>
    </div>
  );
};

ClauseCard.propTypes = {
  clause: PropTypes.shape({
    id: PropTypes.string.isRequired,
    text: PropTypes.string.isRequired,
  }).isRequired,
  index: PropTypes.number.isRequired,
  onClauseTextChange: PropTypes.func.isRequired,
  onDeleteClause: PropTypes.func.isRequired,
  isReadOnly: PropTypes.bool,
};

export default ClauseCard;