import PropTypes from "prop-types";
import { useContext } from "react";
import { ExpandableContent, ExpandableTrigger } from "@kla-v2/ui-components";
import { AccordionTitle } from "@/components/accordion-title";
import NoticePageContent from "./notice-details-content";
import { NoticeContext } from "../../index";

const NoticeDetailsPage = ({ accordionOrderNo }) => {
  const contextValue = useContext(NoticeContext);
  return (
    <>
      <ExpandableTrigger
        showTick={contextValue.isNoticeDetailsComplete}
        className="text-primary font-semibold text-base"
        variant="secondary"
      >
        <AccordionTitle
          accordionOrderNo={accordionOrderNo}
          accordionTitle="Notice Details"
          isColored={true}
        />
      </ExpandableTrigger>
      <ExpandableContent>
        <div className="w-full flex flex-col">
          <NoticePageContent  />
        </div>
      </ExpandableContent>
    </>
  );
};

NoticeDetailsPage.propTypes = {
  accordionOrderNo: PropTypes.number.isRequired,
  setAccordionValue: PropTypes.func,
};

export { NoticeDetailsPage };
