import { AccordionMetadata } from "@/components/accordion-metadata";
import { AccordionTitle } from "@/components/accordion-title";
import { CloseToastButton } from "@/components/ui/close-toast";
import { Form, FormField, FormItem } from "@/components/ui/form";
import { useLanguage } from "@/hooks";
import { useUserProfile } from "@/hooks/user-profile";
import { useSaveBasicDetailsMutation } from "@/services/half-an-hour-discussion";
import { useGetDesignationQuery } from "@/services/master-data-management/basic-details-notice";
import { useGetPlaceListQuery } from "@/services/master-data-management/place-list";
import { useGetQuestionNumbersQuery } from "@/services/question-notice";
import { resetFormWithResponseData, shouldShowTick } from "@/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  AlertBar,
  Button,
  Combobox,
  DatePicker,
  DropdownSelect,
  ExpandableContent,
  ExpandableTrigger,
  toast,
} from "@kla-v2/ui-components";
import { Save } from "lucide-react";
import PropTypes from "prop-types";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
  useMemo,
} from "react";
import { useForm } from "react-hook-form";
import { useParams } from "react-router-dom";
import { BasicDetailsSchema } from "./form-schema";

const BasicDetailsPage = forwardRef(
  (
    { accordionOrderNo, Metadata, basicDetailsData, openNext, refetchDocument },
    ref
  ) => {
    const { t } = useLanguage();
    const [saveBasicDetails, { isLoading }] = useSaveBasicDetailsMutation();
    const { data: designationData } = useGetDesignationQuery();
    const { userProfile } = useUserProfile();
    const { id } = useParams();
    const [designation, setDesignation] = useState([]);
    const { data: placesList } = useGetPlaceListQuery();
    const [questionNumberList, setQuestionNumberList] = useState([]);
    const [questionID, setquestionID] = useState("");

    const memberPlaces = useMemo(() => {
      const options = [];

      if (userProfile?.constituencyName) {
        options.push({
          label: userProfile.constituencyName,
          value: userProfile.constituencyName,
        });
      }

      // Add places from the API if available
      if (placesList && Array.isArray(placesList)) {
        placesList.forEach(place => {
          if (place && place.label && place.value) {
            options.push(place);
          }
        });
      }

      return options;
    }, [placesList, userProfile]);

    const QestionCategory = [
      { label: "Starred", value: "STARRED" },
      { label: "Un-Starred", value: "UNSTARRED" },
    ];

    const form = useForm({
      mode: "onSubmit",
      resolver: zodResolver(BasicDetailsSchema(t)),
      defaultValues: {
        ministerDesignationId: "",
        questionCategory: "",
        discussionDate: "",
        questionDate: "",
        questionId: "",
        place: "",
        placeInLocal: "",
      },
    });
    
    useImperativeHandle(ref, () => ({
      async validateAndSubmit() {
        const isValid = await form.trigger();
        return isValid;
      },
    }));
    
    const showTick = shouldShowTick(form);
    const watchFields = form.watch("questionDate");

    const { data: questionNumbersData } = useGetQuestionNumbersQuery(
      watchFields || basicDetailsData?.questionDate, 
      { skip: !watchFields && !basicDetailsData?.questionDate }
    );

    useEffect(() => {
      if (basicDetailsData) {
        form.reset(basicDetailsData);
      }
    }, [basicDetailsData, form]);

    useEffect(() => {
      if (designationData?.length > 0) {
        const formattedDesignations = designationData.map((item) => ({
          label: item.title,
          value: item.id.toString(),
        }));
        setDesignation(formattedDesignations);
      }

      if (questionNumbersData?.length > 0) {
        const formattedList = questionNumbersData.map((item) => ({
          label: item.questionNumber,
          value: item.id.toString(),
        }));
        setQuestionNumberList(formattedList);
      }
    }, [designationData, questionNumbersData]);

    const onSubmit = async (e) => {
      e.preventDefault();
      const data = form.getValues();
      const updatedBasicDetails = {
        type: "NOTICE_FOR_HALF_AN_HOUR_DISCUSSION",
        assembly: Metadata.assembly,
        session: Metadata.session,
        ministerDesignationId: Number(data.ministerDesignationId),
        // Add ministerDesignation text field
        ministerDesignation: designation.find(d => d.value === data.ministerDesignationId)?.label || "",
        ministerId: userProfile.ministerId,
        place: data.place,
        placeInLocal: data.place,
        discussionDate: data.discussionDate,
        questionDate: data.questionDate,
        questionId: data.questionId,
        questionNumber: questionID,
        questionCategory: data.questionCategory,
        member: {
          memberId: userProfile.ministerId,
          memberDisplayName: userProfile.displayName,
          memberDisplayNameInLocal: userProfile.displayNameInLocal,
          constituencyName: userProfile.constituencyName,
          constituencyNameInLocal: userProfile.constituencyNameInLocal,
          politicalPartyName: userProfile.politicalPartyName,
          politicalPartyNameInLocal: userProfile.politicalPartyNameInLocal,
        },
      };

      try {
        const response = await saveBasicDetails({
          id,
          data: updatedBasicDetails,
        }).unwrap();
        
        // Format response data to match form fields
        const formattedResponse = {
          ...response,
          ministerDesignationId: response.ministerDesignationId?.toString(),
          questionId: response.questionId?.toString(),
        };
        
        resetFormWithResponseData(form, BasicDetailsSchema(t), formattedResponse);
        toast.success(t("toast.documentSave"));
        refetchDocument();
        openNext();
      } catch (error) {
        toast.error("Submission failed", {
          description: error?.data?.title || error?.data?.detail || error?.message,
          action: { label: <CloseToastButton /> },
        });
      }
    };

    return (
      <>
        <ExpandableTrigger
          showTick={showTick}
          className="text-primary font-semibold text-base"
          variant="secondary"
        >
          <AccordionTitle
            accordionOrderNo={accordionOrderNo}
            accordionTitle={t("basicDetails")}
            isColored={true}
          />
        </ExpandableTrigger>
        <ExpandableContent>
          <div className="w-full flex flex-col">
            <div className="py-4 px-6">
              <Form {...form} className="w-full">
                <form
                  onSubmit={(e) => onSubmit(e)}
                  noValidate
                  className="w-full space-y-6"
                >
                  <AccordionMetadata accordionMetadata={Metadata} />
                  <div className="md:grid grid-cols-1 md:grid-cols-3 gap-6">
                    <FormField
                      control={form.control}
                      name="ministerDesignationId"
                      render={({ field, fieldState }) => (
                        <FormItem className="md:col-span-1">
                          <Combobox
                            {...field}
                            className="md:col-span-1 w-full"
                            aria-label="ministerDesignationId"
                            label={t("form:ministerDesignation")}
                            options={designation} // Use formatted options
                            onValueChange={(value) => {
                              field.onChange(value);
                              const selectedDesignation = designation.find(
                                (minister) => minister.value === value
                              );
                              if (selectedDesignation) {
                                field.onChange(selectedDesignation.value);
                                form.trigger("ministerDesignationId");
                              }
                            }}
                            placeholder={t("placeholder:ministerDesignation")}
                            error={fieldState.error?.message}
                          />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="place"
                      render={({ field, fieldState }) => (
                        <FormItem className="md:col-span-1">
                          <Combobox
                            {...field}
                            className="md:col-span-1 w-full"
                            label={t("Place")}
                            error={fieldState.error?.message}
                            options={memberPlaces || []}
                            onValueChange={(value) => {
                              field.onChange(value);
                              const selectedDesignation = memberPlaces.find(
                                (minister) => minister.value === value
                              );
                              if (selectedDesignation) {
                                field.onChange(selectedDesignation.value);
                                form.setValue(
                                  "placeInLocal",
                                  selectedDesignation.label
                                );
                                form.trigger("place");
                              }
                            }}
                            placeholder={t("placeholder:Place")}
                          />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="discussionDate"
                      render={({ field, fieldState }) => (
                        <FormItem className="md:col-span-1">
                          <DatePicker
                            {...field}
                            label={t("form:discussionDate")}
                            placeholder={t("placeholder:discussionDate")}
                            error={fieldState.error?.message}
                            onChange={(value) => {
                              field.onChange(value);
                              form.trigger("discussionDate");
                            }}
                          />
                        </FormItem>
                      )}
                    />

                    <div className="col-span-3">
                      <AlertBar text={t("requiredTwoDays")} type="warning" />
                    </div>

                    <FormField
                      control={form.control}
                      name="questionDate"
                      render={({ field, fieldState }) => (
                        <FormItem className="md:col-span-1">
                          <DatePicker
                            {...field}
                            label={t("form:selectDate")}
                            placeholder={t("placeholder:selectDate")}
                            error={fieldState.error?.message}
                            onChange={(value) => {
                              field.onChange(value);
                              form.trigger("questionDate");
                            }}
                          />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="questionCategory"
                      render={({ field, fieldState }) => (
                        <FormItem className="md:col-span-1">
                          <DropdownSelect
                            {...field}
                            className="md:col-span-1 w-full"
                            label={t("form:questionCategory")}
                            error={fieldState.error?.message}
                            options={[{ selectItems: QestionCategory }]}
                            placeholder={t("placeholder:questionCategory")}
                            onChange={(value) => {
                              field.onChange(value);
                              form.trigger("questionCategory");
                            }}
                          />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="questionId"
                      render={({ field, fieldState }) => (
                        <FormItem className="md:col-span-1">
                          <Combobox
                            {...field}
                            className="md:col-span-1 w-full"
                            label={t("form:questionNumber")}
                            options={questionNumberList}
                            placeholder={t("placeholder:questionNumber")}
                            onValueChange={(value) => {
                              field.onChange(value);
                              const selectedQuestion = questionNumberList.find(
                                (minister) => minister.value === value
                              );
                              if (selectedQuestion) {
                                setquestionID(selectedQuestion.label);
                                form.trigger("questionId");
                              }
                            }}
                            error={fieldState.error?.message}
                          />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="flex justify-end">
                    <Button
                      type="submit"
                      variant="secondary"
                      icon={Save}
                      iconPosition="right"
                      disabled={isLoading}
                    >
                      {isLoading ? "Saving..." : "Save"}
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          </div>
        </ExpandableContent>
      </>
    );
  }
);
BasicDetailsPage.displayName = "BasicDetailsPage";

BasicDetailsPage.propTypes = {
  accordionOrderNo: PropTypes.number,
  Metadata: PropTypes.object,
  basicDetailsData: PropTypes.object,
  openNext: PropTypes.func.isRequired,
  refetchDocument: PropTypes.func.isRequired,
};

export { BasicDetailsPage };
