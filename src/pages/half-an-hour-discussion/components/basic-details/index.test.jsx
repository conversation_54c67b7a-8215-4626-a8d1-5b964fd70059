import { renderApp } from "@/testing/utils";
import { ExpandableAccordion, ExpandableItem } from "@kla-v2/ui-components";
import { screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it, vi } from "vitest";
import { BasicDetailsPage } from ".";

describe("Basic- Details Accordion", () => {
  const mockData = {
    designation: "Chief Minister",
    ministerDesignationId: 2,
    questionCategory: "STARRED",
    discussionDate: "2025-04-30",
    questionDate: "2025-04-30",
    questionId: 10,
    place: "Trivandrum",
    placeInLocal: "Adimalathura, Trivandrum",
  };

  const user = userEvent.setup();
  const openNext = vi.fn();

  const renderComponent = (data) =>
    renderApp(
      <ExpandableAccordion type="multiple">
        <ExpandableItem value="basic-details">
          <BasicDetailsPage
            accordionOrderNo={1}
            Metadata={data}
            basicDetailsData={data}
            openNext={openNext}
          />
        </ExpandableItem>
      </ExpandableAccordion>
    );

  it("renders the accordion with the correct title", async () => {
    await renderComponent();
    expect(screen.getByText("Basic Details")).toBeInTheDocument();
  });

  it("displays all fields in a component", async () => {
    await renderComponent(mockData);

    const trigger = screen.getByText("Basic Details");
    await user.click(trigger);

    expect(screen.getByText("Minister Designation")).toBeInTheDocument();
    expect(screen.getByText("Discussion Date")).toBeInTheDocument();
    expect(screen.getByText("Question Date")).toBeInTheDocument();
    expect(screen.getByText("Question Category")).toBeInTheDocument();
    expect(screen.getByText("Question Number")).toBeInTheDocument();
  });

  it("check the save button is there or not", async () => {
    await renderComponent(mockData);

    const accordionTitle = screen.getByText("Basic Details");
    await user.click(accordionTitle);

    const saveButton = screen.getByRole("button", { name: /Save/i });
    expect(saveButton).toBeInTheDocument();
  });
});
