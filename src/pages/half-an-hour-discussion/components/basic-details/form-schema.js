import { z } from "zod";

export const BasicDetailsSchema = (t) =>
  z.object({
    ministerDesignationId: z
      .string()
      .min(1, { message: t("validation:ministerDesignationRequired") }),
    questionCategory: z
      .string()
      .min(1, { message: t("validation:questionCategoryRequired") }),
    discussionDate: z
      .string()
      .min(1, { message: t("validation:discussionDateRequired") }),
    questionDate: z
      .string()
      .min(1, { message: t("validation:questionDateRequired") }),
    questionId: z.string().min(1, { message: t("validation:questionIdRequired") }),
    place: z.string().min(1, { message: t("validation:placeRequired") }),
    placeInLocal: z.string().optional(),
  });
