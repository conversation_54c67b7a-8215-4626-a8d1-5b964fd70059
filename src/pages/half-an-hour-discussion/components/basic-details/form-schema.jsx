import { z } from "zod";

const coerceToString = (value) => (value ? String(value) : value);

const requiredString = (message) =>
  z.string({ invalid_type_error: message }).min(1, message);

export const BasicDetailsSchema = (t) =>
  z.object({
    ministerDesignationId: z.preprocess(
      coerceToString,
      requiredString("Designation is required")
    ),
    questionCategory: z.preprocess(
      coerceToString,
      requiredString("Question Category is required")
    ),
    discussionDate: requiredString(t("validation:invalidDateValue")),
    questionDate: requiredString(t("validation:invalidDateValue")),
    questionId: z.preprocess(
      coerceToString,
      requiredString("Question Number is required")
    ),
    place: z.preprocess(coerceToString, requiredString("Place is required")),
  });
