import { renderApp } from "@/testing/utils";
import { ExpandableAccordion, ExpandableItem } from "@kla-v2/ui-components";
import { screen } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import { NoticeDetails } from ".";
import userEvent from "@testing-library/user-event";
describe("Notice Details Accordion", () => {
  const user = userEvent.setup();

  const renderComponent = (data) =>
    renderApp(
      <ExpandableAccordion type="multiple">
        <ExpandableItem value="explanatory-details">
          <NoticeDetails accordionOrderNo={3} noticeDetails={data} />
        </ExpandableItem>
      </ExpandableAccordion>
    );

  it("renders the accordion with the correct title", async () => {
    await renderComponent();
    expect(screen.getByText("Notice Details")).toBeInTheDocument();
  });

  it("render all the fields on component", async () => {
    await renderComponent();

    const accordionTitle = screen.getByText("Notice Details");
    await user.click(accordionTitle);

    expect(screen.getByText("Notice Heading")).toBeInTheDocument();
    expect(screen.getByText("Description")).toBeInTheDocument();
  });

  it("check the save button is there or not", async () => {
    await renderComponent();
    const accordionTitle = screen.getByText("Notice Details");
    await user.click(accordionTitle);
    const saveButton = screen.getByRole("button", { name: /Save/i });
    expect(saveButton).toBeInTheDocument();
    await user.click(saveButton);
  });
});
