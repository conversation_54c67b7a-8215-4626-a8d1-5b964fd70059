import { AccordionTitle } from "@/components/accordion-title";
import RichTextToPDF from "@/components/richTextToPDF";
import { Form, FormField, FormItem } from "@/components/ui/form";
import { useDebounce, useLanguage } from "@/hooks";
import { useSaveExplanatoryNoteMutation } from "@/services/half-an-hour-discussion";
import { shouldShowTick } from "@/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Button,
  Checkbox,
  ExpandableContent,
  ExpandableTrigger,
  RichTextEditor,
  toast,
} from "@kla-v2/ui-components";
import { Expand, Save } from "lucide-react";
import PropTypes from "prop-types";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from "react";
import { useForm } from "react-hook-form";
import { useParams } from "react-router-dom";
import { ExplanatoryFormSchema } from "./form-schema";
import { CloseToastButton } from "@/components/ui/close-toast";

const ExplanatoryNote = forwardRef(
  (
    { accordionOrderNo, explanatoryDetails, refetchDocument, openNext },
    ref
  ) => {
    const { t } = useLanguage();
    const [saveExplanatoryNote, { isLoading }] = useSaveExplanatoryNoteMutation();
    const [explanatoryNoteText, setExplanatoryNoteText] = useState("");
    const textDebounced = useDebounce(explanatoryNoteText);
    const { id } = useParams();
    const form = useForm({
      mode: "onSubmit",
      resolver: zodResolver(ExplanatoryFormSchema(t)),
      defaultValues: {
        explanatoryNote: "",
        hasDigitalSignature: false,
      },
    });
    const {
      formState: { errors },
    } = form;

    useMemo(async () => {
      if (textDebounced) {
        // Don't auto-save if the content hasn't actually changed
        const currentText = form.getValues().explanatoryNote;
        if (currentText === textDebounced) return;
        
        const updatedData = {
          explanatoryNote: textDebounced,
          hasDigitalSignature: form.getValues().hasDigitalSignature,
        };

        try {
          await saveExplanatoryNote({
            id,
            data: updatedData,
          }).unwrap();
          refetchDocument();
        } catch (error) {
          console.error("Auto-save failed:", error);
        }
      }
    }, [textDebounced, id, saveExplanatoryNote, refetchDocument, form]);

    useImperativeHandle(ref, () => ({
      async validateAndSubmit() {
        const isValid = await form.trigger();
        return isValid;
      },
    }));
    
    const showTick = shouldShowTick(form);

    useEffect(() => {
    if (explanatoryDetails) {
    // Format explanatory details to handle JSON parsing properly
      const formattedExplanatory = {
          ...explanatoryDetails,
        // Ensure explanatoryNote is a string even if it was previously stringified
        explanatoryNote: typeof explanatoryDetails.explanatoryNote === 'object' 
          ? JSON.stringify(explanatoryDetails.explanatoryNote) 
          : explanatoryDetails.explanatoryNote
      };
      form.reset(formattedExplanatory);
    }
  }, [explanatoryDetails, form]);

    const onSubmit = async (e) => {
      e.preventDefault();
      const values = form.getValues();
      const updatedData = {
        explanatoryNote: values.explanatoryNote,
        hasDigitalSignature: values.hasDigitalSignature,
      };

      try {
        const response = await saveExplanatoryNote({
          id,
          data: updatedData,
        }).unwrap();

        if (response) {
          // Format the response for proper form handling
          const formattedResponse = {
            ...response,
            explanatoryNote: typeof response.explanatoryNote === 'object' 
              ? JSON.stringify(response.explanatoryNote) 
              : response.explanatoryNote
          };
          
          // Reset form with formatted response
          form.reset(formattedResponse);
          toast.success(t("toast.documentSave"));
          refetchDocument();
          openNext();
        } else {
          toast.error("Error", {
            description: response?.message || "An error occurred",
          });
        }
      } catch (error) {
        toast.error("Submission failed", {
          description: error?.data?.detail || error?.data?.title || error?.message,
          action: { label: <CloseToastButton /> },
        });
      }
    };

    return (
      <>
        <ExpandableTrigger
          showTick={showTick}
          className="text-primary font-semibold text-base"
          variant="secondary"
        >
          <AccordionTitle
            accordionOrderNo={accordionOrderNo}
            accordionTitle={t("explanatoryNote")}
            isColored={true}
          />
        </ExpandableTrigger>
        <ExpandableContent>
          <div className="w-full flex flex-col">
            <div className="flex flex-col gap-4 p-6">
              <Form className="w-full">
                <form onSubmit={(e) => onSubmit(e)}>
                  <div className="lg:grid lg:grid-cols-5 gap-4">
                    <div className="flex-col col-span-3 mb-4">
                      <div className="relative">
                        <FormField
                          control={form.control}
                          name="explanatoryNote"
                          render={({ field }) => (
                            <FormItem className="mb-2">
                              <RichTextEditor
                                {...field}
                                aria-label="Explanatory Note"
                                defaultValue={
                                  field.value && typeof field.value === 'string'
                                    ? JSON.parse(field.value)
                                    : {}
                                }
                                fullScreenControl={
                                  <div className="p-2.5 h-full">
                                    <Expand className="size-4 cursor-pointer" />
                                  </div>
                                }
                                onChange={(val) => {
                                  field.onChange(JSON.stringify(val));
                                  form.trigger("explanatoryNote");
                                  setExplanatoryNoteText(JSON.stringify(val));
                                }}
                              />
                            </FormItem>
                          )}
                        />
                        {isLoading && (
                          <div className="absolute right-2 bottom-2 typography-body-text-r-12 text-gray-500">
                            Saving...
                          </div>
                        )}
                        {errors.explanatoryNote && (
                          <p className="h-4 typography-body-text-r-14 text-error my-2">
                            {errors.explanatoryNote.message}
                          </p>
                        )}
                      </div>

                      <FormField
                        control={form.control}
                        name="hasDigitalSignature"
                        render={({ field }) => (
                          <FormItem className="md:col-span-1">
                            <Checkbox
                              {...field}
                              label={t("form:digitalSignature")}
                              onChange={(val) => {
                                field.onChange(val);
                                form.trigger("hasDigitalSignature");
                              }}
                            />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="col-span-2 w-full mb-2">
                      <RichTextToPDF
                        jsonString={explanatoryDetails?.explanatoryNote}
                      />
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button
                      type="submit"
                      variant="secondary"
                      icon={Save}
                      iconPosition="right"
                      disabled={isLoading}
                    >
                      {isLoading ? "Saving..." : "Save"}
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          </div>
        </ExpandableContent>
      </>
    );
  }
);
ExplanatoryNote.displayName = "ExplanatoryNotePage";
ExplanatoryNote.propTypes = {
  accordionOrderNo: PropTypes.number,
  explanatoryDetails: PropTypes.object,
  refetchDocument: PropTypes.func.isRequired,
  openNext: PropTypes.func.isRequired,
};

export { ExplanatoryNote };