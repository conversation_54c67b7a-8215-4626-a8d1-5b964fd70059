import { DocumentMetadata } from "@/components/document-metadata";
import { BasicDetailsReadOnly } from "@/components/read-only/basic-details-read-only";
import { ExplanatoryReadOnly } from "@/components/read-only/explanatory-read-only";
import { NoriceDetailsReadOnly } from "@/components/read-only/notice-details-read-only";
import {
  Button,
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogPortal,
  DialogTitle,
  ExpandableAccordion,
  ExpandableItem,
} from "@kla-v2/ui-components";
import { Check, Pencil } from "lucide-react";
import PropTypes from "prop-types";
import { useState } from "react";

export default function PreviewDialog({
  onClose,
  onConfirm,
  documentMetadata,
  isOpen,
}) {
  const [accordionValue, setAccordionValue] = useState([]);
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogPortal>
        <DialogContent className="w-[1140px] max-w-[80vw]">
          <DialogHeader>
            <DialogTitle>
              <div className="typography-page-heading">Preview & Confrim</div>
            </DialogTitle>
            <DialogDescription>
              <div className="flex items-center justify-between my-4">
                <h3 className="typography-sub-title-heading">
                  {documentMetadata?.noticeType}
                </h3>
              </div>
              <DocumentMetadata documentMetadata={documentMetadata} />
            </DialogDescription>
          </DialogHeader>
          <div className="bg-background p-4 rounded-lg max-h-[80vh] overflow-auto">
            <div className="mb-4">
              <h3 className="text-xl font-medium mb-2">Details</h3>
              <ExpandableAccordion
                type="multiple"
                value={accordionValue}
                onValueChange={setAccordionValue}
                className="w-full flex flex-col gap-4 flex-grow"
              >
                <ExpandableItem value={`item-1`}>
                  <BasicDetailsReadOnly
                    accordionOrderNo={1}
                    data={documentMetadata}
                  />
                </ExpandableItem>
                <ExpandableItem value={`item-2`}>
                  <NoriceDetailsReadOnly
                    accordionOrderNo={3}
                    data={documentMetadata}
                  />
                </ExpandableItem>
                <ExpandableItem value={`item-3`}>
                  <ExplanatoryReadOnly
                    accordionOrderNo={2}
                    data={documentMetadata}
                  />
                </ExpandableItem>
              </ExpandableAccordion>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button
                icon={Pencil}
                iconPosition="left"
                size="lg"
                variant="secondary"
              >
                Edit
              </Button>
            </DialogClose>
            <Button
              icon={Check}
              iconPosition="right"
              size="lg"
              variant="primary"
              onClick={onConfirm}
            >
              Confirm
            </Button>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
}

PreviewDialog.propTypes = {
  onClose: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
  documentMetadata: PropTypes.shape({
    name: PropTypes.string.isRequired,
    noticeType: PropTypes.string,
  }).isRequired,
  isOpen: PropTypes.bool.isRequired,
};
