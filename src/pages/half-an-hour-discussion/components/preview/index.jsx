import { useLanguage } from '@/hooks';
import PropTypes from 'prop-types';
import {
  Button,
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@kla-v2/ui-components';

const PreviewDialog = ({ isOpen, onClose, onConfirm, documentMetadata }) => {
  const { t } = useLanguage();
  
  const previewData = [
    {
      label: t('Assembly'),
      value: documentMetadata?.assembly,
    },
    {
      label: t('Session'),
      value: documentMetadata?.session,
    },
    {
      label: t('Notice Type'),
      value: documentMetadata?.noticeType,
    },
    {
      label: t('Question Date'),
      value: documentMetadata?.questionDate,
    },
    {
      label: t('Discussion Date'),
      value: documentMetadata?.discussionDate,
    },
    {
      label: t('Place'),
      value: documentMetadata?.place,
    },
    {
      label: t('Minister Designation'),
      value: documentMetadata?.ministerDesignation,
    },
    {
      label: t('Notice Heading'),
      value: documentMetadata?.noticeHeading,
    },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-screen-lg">
        <DialogHeader>
          <DialogTitle>{t('Document Preview')}</DialogTitle>
        </DialogHeader>

        <div className="p-4 space-y-4">
          <div className="text-lg font-semibold mb-4">{t('Document Details')}</div>
          <div className="grid md:grid-cols-2 gap-4">
            {previewData.map((item, index) => (
              <div key={index} className="flex flex-col">
                <span className="text-gray-600 text-sm">{item.label}</span>
                <span className="font-medium">
                  {item.value || '-'}
                </span>
              </div>
            ))}
          </div>
          
          {documentMetadata?.description && (
            <div className="mt-6">
              <div className="text-lg font-semibold mb-2">{t('Description')}</div>
              <div className="border p-4 rounded-md bg-gray-50">
                <div dangerouslySetInnerHTML={{ __html: JSON.parse(documentMetadata.description)?.content }} />
              </div>
            </div>
          )}
          
          {documentMetadata?.explanatoryNote && (
            <div className="mt-6">
              <div className="text-lg font-semibold mb-2">{t('Explanatory Note')}</div>
              <div className="border p-4 rounded-md bg-gray-50">
                <div dangerouslySetInnerHTML={{ __html: JSON.parse(documentMetadata.explanatoryNote)?.content }} />
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline">{t('Cancel')}</Button>
          </DialogClose>
          <Button onClick={onConfirm}>{t('Submit')}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

PreviewDialog.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
  documentMetadata: PropTypes.object.isRequired,
};

export default PreviewDialog;