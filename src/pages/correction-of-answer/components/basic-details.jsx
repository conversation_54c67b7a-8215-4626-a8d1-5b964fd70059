import { formatDateYYYYMMDD } from "@/utils";
import { Label, Badge } from "@kla-v2/ui-components";
import PropTypes from "prop-types";

const InfoField = ({ label, value }) => (
  <div className="flex flex-col gap-2">
    <span className="text-gray-600 typography-body-text-r-14">{label}</span>
    <span className="text-base">{value}</span>
  </div>
);

const MemberBadge = ({ member }) => (
  <Badge variant="outline" className="flex items-center gap-1 px-3 py-1.5">
    <img
      src={`/mdm-service/api/minister/${member.id}/photo`}
      alt={member.memberDisplayName}
      className="object-cover w-6 h-6 rounded-full"
    />
    <span>{member.memberDisplayName}</span>
    <span className="text-gray-400">({member.constituencyName})</span>
  </Badge>
);

export const BasicDetails = ({ noticeData }) => {
  const {
    assembly,
    session,
    noticeNumber,
    createdAt,
    questionDate,
    ministerDesignation,
    portfolio,
    subSubject,
    primaryMember,
    secondaryMembers = [],
  } = noticeData || {};

  return (
    <div className="flex flex-col gap-6 p-6 space-y-6">
      <div className="grid grid-cols-3 gap-6">
        <InfoField label="KLA" value={assembly} />
        <InfoField label="Session" value={session} />
        <InfoField label="Question Number" value={noticeNumber} />
        <InfoField
          label="Date of Registration"
          value={formatDateYYYYMMDD(createdAt)}
        />
        <InfoField
          label="Question Date"
          value={formatDateYYYYMMDD(questionDate)}
        />
        <InfoField label="Designation" value={ministerDesignation} />
        <InfoField label="Minister Subject / Portfolio" value={portfolio} />
        <InfoField label="Minister Sub Subject" value={subSubject} />
      </div>

      <div className="flex flex-col gap-2">
        <Label>Name of Members</Label>
        <div className="flex flex-wrap gap-2">
          {primaryMember && <MemberBadge member={primaryMember} />}
          {secondaryMembers.map((member, index) => (
            <MemberBadge key={index} member={member} />
          ))}
        </div>
      </div>
    </div>
  );
};

BasicDetails.propTypes = {
  noticeData: PropTypes.shape({
    assembly: PropTypes.string,
    session: PropTypes.string,
    noticeNumber: PropTypes.string,
    questionDate: PropTypes.string,
    createdAt: PropTypes.string,
    ministerDesignation: PropTypes.string,
    portfolio: PropTypes.string,
    subSubject: PropTypes.string,
    primaryMember: PropTypes.shape({
      id: PropTypes.string,
      memberDisplayName: PropTypes.string,
      constituencyName: PropTypes.string,
    }),
    secondaryMembers: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.string,
        memberDisplayName: PropTypes.string,
        constituencyName: PropTypes.string,
      })
    ),
  }),
};

InfoField.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
};

MemberBadge.propTypes = {
  member: PropTypes.shape({
    id: PropTypes.string.isRequired,
    memberDisplayName: PropTypes.string.isRequired,
    constituencyName: PropTypes.string.isRequired,
  }).isRequired,
};
