import { useState } from "react";
import PropTypes from "prop-types";
import { Badge, Label, Switch, Textarea } from "@kla-v2/ui-components";
import { StarredIcon, UnStarredIcon } from "@/icons/star-icon";
import FilePreviewButton from "@/components/ui/file-preview-button";

export const QuestionDetails = ({ noticeData }) => {
  const [value, setValue] = useState(
    noticeData?.clauses?.[0]?.answers?.[0]?.content || ""
  );

  const getBadgeStyle = (priority) => {
    if (noticeData?.noticePriority === priority) {
      switch (priority) {
        case "P1":
          return "border border-blue-200 bg-blue-100 text-blue-500";
        case "P2":
          return "border border-green-200 bg-green-100 text-green-500";
        case "P3":
          return "border border-red-200 bg-red-100 text-red-500";
        case "NIL":
          return "border border-gray-300 bg-gray-100 text-gray-700";
        default:
          return "border border-gray-300 text-gray-700";
      }
    }
    return "border border-gray-300 text-gray-700";
  };

  const getAnswerTypeBadgeStyle = (answerType) => {
    switch (answerType) {
      case "INTERIM":
        return "bg-warning-100 text-warning-500";
      case "FINAL":
        return "bg-blue-100 text-blue-500";
      default:
        return "border border-gray-300 text-gray-700";
    }
  };

  return (
    <div className="flex flex-col gap-6 p-6">
      <div className="flex items-center gap-2">
        <div>
          <Label
            htmlFor="priority"
            className="mb-0 text-gray-600 typography-body-text-m-14"
          >
            Priority
          </Label>
          <div className="flex gap-2">
            <Badge
              className={`px-4 py-2 w-32 rounded-full cursor-pointer text-center ${getBadgeStyle(
                noticeData?.noticePriority
              )}`}
            >
              {noticeData?.noticePriority}
            </Badge>
          </div>
        </div>
        <div className="ml-auto">
          <div className="flex items-center gap-2 cursor-pointer">
            <Label className="text-gray-700 typography-body-text-m-14">
              {noticeData?.starred ? "Starred" : "Unstarred"}
            </Label>
            {noticeData?.starred ? <StarredIcon /> : <UnStarredIcon />}
          </div>
        </div>
      </div>

      <div className="flex flex-col gap-2">
        <Label
          htmlFor="question-heading"
          className="text-gray-600 typography-body-text-m-14"
        >
          Question Heading
        </Label>
        <h3 className="typography-body-text-s-18">
          {noticeData?.noticeHeading}
        </h3>
      </div>

      <div className="flex flex-col gap-4">
        {noticeData?.clauses?.map((clause, index) => {
          const answer = clause?.answers?.[0];

          return (
            <div key={clause.id} className="flex flex-col space-y-4">
              <Label className="font-medium">
                Clause {String.fromCharCode(65 + index)}
              </Label>
              <span className="font-bold text-primary-tint-10 typography-para-16">
                {clause.content}
              </span>
              <div className="flex items-center">
                <h4 className="text-secondary typography-body-text-s-16">
                  Answer
                </h4>
                <div className="flex items-center gap-2 ml-6">
                  <Badge>Old version</Badge>
                  <Switch
                    id="switch"
                    label="New version"
                    onCheckedChange={() => {}}
                    size="sm"
                  />
                </div>
              </div>
              <div className="flex flex-col gap-2">
                <div className="relative border rounded-md">
                  <div className="absolute z-10 top-2 left-2">
                    <Badge
                      className={`px-3 py-1 text-xs font-medium border-none ${getAnswerTypeBadgeStyle(
                        answer?.answerType
                      )}`}
                    >
                      {answer?.answerType.charAt(0).toUpperCase() +
                        answer?.answerType.slice(1).toLowerCase()}
                    </Badge>
                  </div>
                  <Textarea
                    className="w-full min-h-[100px] pt-10 px-3 pb-8 resize-none border-0 focus:ring-0 focus:outline-none"
                    value={value}
                    readOnly
                    onChange={(e) => setValue(e.target.value)}
                  />
                  <div className="absolute text-xs text-gray-500 bottom-2 right-3">{`${
                    value?.length || 0
                  }/500`}</div>
                </div>
              </div>

              {answer?.attachments?.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {answer.attachments.map((attachment) => (
                    <FilePreviewButton
                      key={attachment?.id}
                      documentName={attachment?.name}
                      pdfUrl={attachment?.fileUrl}
                    />
                  ))}
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

QuestionDetails.propTypes = {
  noticeData: PropTypes.shape({
    noticePriority: PropTypes.string,
    starred: PropTypes.bool,
    noticeHeading: PropTypes.string,
    clauses: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.string,
        content: PropTypes.string,
        answers: PropTypes.arrayOf(
          PropTypes.shape({
            content: PropTypes.string,
            answerType: PropTypes.string,
            attachments: PropTypes.arrayOf(
              PropTypes.shape({
                id: PropTypes.string,
                name: PropTypes.string,
                fileUrl: PropTypes.string,
              })
            ),
          })
        ),
      })
    ),
    delayStatement: PropTypes.object,
    ministerLetter: PropTypes.object,
  }),
};
