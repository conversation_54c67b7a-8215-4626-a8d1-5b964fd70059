import { useState } from "react";
import PropTypes from "prop-types";
import {
  Button,
  Combobox,
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogPortal,
  DialogTitle,
} from "@kla-v2/ui-components";

export const AssignDialog = ({ open, onClose, onAssign }) => {
  const [selectedUser, setSelectedUser] = useState(null);

  const users = [
    {
      avatar:
        "https://w1.pngwing.com/pngs/917/718/png-transparent-moustache-prithviraj-sukumaran-malayalam-kerala-film-cinema-malayalam-tamil-mammootty-thumbnail.png",
      description: "<PERSON><PERSON> Menon",
      label: "CPL Assistant 1",
      value: "assistant-1",
    },
    {
      avatar:
        "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSUUup-wv0BIizOeCeTmhNDXfh5UPNrLcEQOA&s",
      description: "<PERSON><PERSON>",
      label: "CPL Assistant 2",
      value: "assistant-2",
    },
    {
      avatar:
        "https://www.pinarayivijayan.in/wp-content/themes/pinarayi/img/logo.png",
      description: "Vijayakrishnan Nair",
      label: "CPL Assistant 3",
      value: "assistant-3",
    },
    {
      avatar:
        "https://images.rawpixel.com/image_png_800/cHJpdmF0ZS9sci9pbWFnZXMvd2Vic2l0ZS8yMDIyLTA4L2pvYjEwMzQtZWxlbWVudC0wNi0zOTcucG5n.png",
      description: "Mary Joseph",
      label: "CPL Assistant 4",
      value: "assistant-4",
    },
  ];

  const handleAssign = () => {
    if (selectedUser) {
      onAssign(selectedUser);
      onClose();
    }
  };

  return (
    <Dialog className="sm:text-left" open={open} onOpenChange={onClose}>
      <DialogPortal>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Assign</DialogTitle>
            <DialogDescription className="text-gray-500">
              Select a user from the list to assign this task.
            </DialogDescription>
            <hr className="border border-border-1 opacity-30" />
          </DialogHeader>
          <div className="mt-4">
            <Combobox
              className="w-full"
              options={users}
              isMulti={false}
              modalPopover={true}
              value={selectedUser}
              onValueChange={setSelectedUser}
              displayValue={(user) => user?.label || ""}
              placeholder="Select user..."
              showAvatar
              size="lg"
            />
          </div>
          <DialogFooter className="!justify-end !items-center space-x-2 mt-4">
            <DialogClose asChild>
              <Button variant="neutral">Cancel</Button>
            </DialogClose>
            <Button
              variant="primary"
              onClick={handleAssign}
              disabled={!selectedUser}
            >
              Assign
            </Button>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

AssignDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onAssign: PropTypes.func.isRequired,
};
