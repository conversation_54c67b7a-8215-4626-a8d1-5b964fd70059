import { AccordionTitle } from "@/components/accordion-title";
import { Form, FormField, FormItem } from "@/components/ui/form";
import { useDebounce, useLanguage } from "@/hooks";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Button,
  Checkbox,
  ExpandableContent,
  ExpandableTrigger,
  RichTextEditor,
  toast,
} from "@kla-v2/ui-components";
import { Expand, Save } from "lucide-react";
import PropTypes from "prop-types";
import { useForm } from "react-hook-form";
import { usePostExplanatoryDetailsMutation } from "@/services/short-notice";
import { useParams } from "react-router-dom";
import { RichTextPage } from "./rich-text-page";
import { ExplanatoryFormSchema } from "./form-schema";
import { useState } from "react";
import { useMemo } from "react";
import { resetFormWithResponseData, shouldShowTick } from "@/utils";

const ExplanatoryNote = ({
  accordionOrderNo,
  explanatoryDetails,
  openNext,
}) => {
  const { t } = useLanguage();
  const [postExplanatoryDetails, { isLoading }] =
    usePostExplanatoryDetailsMutation();
  const [content, setContent] = useState("");
  const textDebounced = useDebounce(content);
  const { documentId } = useParams();

  const form = useForm({
    mode: "onSubmit",
    resolver: zodResolver(ExplanatoryFormSchema(t)),
    defaultValues: {
      explanatoryNote: explanatoryDetails.explanatoryNote ?? "",
      hasDigitalSignature: false,
    },
  });
  const {
    handleSubmit,
    control,
    formState: { errors },
    getValues,
  } = form;

  useMemo(async () => {
    if (textDebounced) {
      const updatedData = {
        explanatoryNote: textDebounced,
        id: documentId,
      };

      await postExplanatoryDetails({
        documentId,
        data: updatedData,
      }).unwrap();
    }
  }, [textDebounced, documentId]);

  const showTick = shouldShowTick(form);
  const onSubmit = async () => {
    const data = getValues();
    const updatedData = {
      explanatoryNote: data?.explanatoryNote,
      hasDigitalSignature: data?.hasDigitalSignature,
      id: documentId,
    };
    try {
      const response = await postExplanatoryDetails({
        documentId,
        data: updatedData,
      }).unwrap();
      resetFormWithResponseData(form, ExplanatoryFormSchema(t), response);
      toast.success(t("toast.documentSave"));
      openNext();
    } catch (error) {
      toast.error(error?.message);
    }
  };
  const jsonString = form?.watch("explanatoryNote");

  return (
    <>
      <ExpandableTrigger
        className="text-primary font-semibold text-base"
        variant="secondary"
        showTick={showTick}
      >
        <AccordionTitle
          accordionOrderNo={accordionOrderNo}
          accordionTitle="Explanatory Note"
          isColored={true}
        />
      </ExpandableTrigger>
      <ExpandableContent>
        <div className="w-full flex flex-col">
          <div className="flex flex-col gap-4 p-6">
            <Form className="w-full">
              <form onSubmit={handleSubmit(onSubmit)}>
                <div className="lg:grid lg:grid-cols-5 gap-4 min-h-[500px]">
                  <div className="flex flex-col col-span-3 h-full">
                    <FormField
                      control={control}
                      name="explanatoryNote"
                      render={({ field }) => (
                        <FormItem className="mb-2 h-full [&>div]:h-full">
                          <RichTextEditor
                            {...field}
                            defaultValue={
                              field.value && typeof field.value === 'string'
                                ? JSON.parse(field.value)
                                : {}
                            }
                            fullScreenControl={
                              <div className="p-2.5 h-full">
                                <Expand className="size-4" />
                              </div>
                            }
                            onChange={(val) => {
                              field.onChange(JSON.stringify(val));
                              setContent(JSON.stringify(val));
                            }}
                          />
                        </FormItem>
                      )}
                    />
                    {isLoading && (
                      <div className="absolute right-2 bottom-2 typography-body-text-r-12 text-gray-500">
                        Saving...
                      </div>
                    )}
                    {errors.explanatoryNote && (
                      <p className="h-4 typography-body-text-r-14 text-error my-2">
                        {errors.explanatoryNote.message}
                      </p>
                    )}
                  </div>
                  <div className="col-span-2 w-full mb-2">
                    <RichTextPage jsonString={jsonString} />
                  </div>
                </div>
                <FormField
                  control={form.control}
                  name="digitalSignature"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:col-span-1">
                      <Checkbox
                        {...field}
                        label={t("form:digitalSignature")}
                        error={fieldState.error?.message}
                        placeholder={t("placeholder:Place")}
                      />
                    </FormItem>
                  )}
                />
                <div className="flex justify-end">
                  <Button
                    type="submit"
                    variant="secondary"
                    className="flex items-center"
                  >
                    Save <Save size={16} />
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </ExpandableContent>
    </>
  );
};

ExplanatoryNote.propTypes = {
  accordionOrderNo: PropTypes.number,
  explanatoryDetails: PropTypes.object,
  openNext: PropTypes.func,
};

export { ExplanatoryNote };
