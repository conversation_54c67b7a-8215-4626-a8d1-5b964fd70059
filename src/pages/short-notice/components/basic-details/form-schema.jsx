import { z } from "zod";
import { basicDetailsFormFields } from "./form-fields";

export const BasicDetailsSchema = (t) => {
  const { DESIGNATION, MINISTER_PORTFOLIO, MINISTER_SUB_SUBJECT, DATE, PLACE } =
    basicDetailsFormFields(t);
  return z.object({
    [DESIGNATION.name]: z.preprocess(
      (value) => (value ? String(value) : value),
      z.string({ invalid_type_error: "Designation is required" })
    ),
    [MINISTER_PORTFOLIO.name]: z.preprocess(
      (value) => (value ? String(value) : value),
      z.string({ invalid_type_error: "Portfolio is required" })
    ),
    [MINISTER_SUB_SUBJECT.name]: z.preprocess(
      (value) => (value ? String(value) : value),
      z.string({ invalid_type_error: "Minister Sub Subject is required" })
    ),
    [DATE.name]: z
      .string({ invalid_type_error: "Date is required" })
      .min(1, "Date is required"),
    [PLACE.name]: z.string({ invalid_type_error: "Place is required" }),
  });
};
