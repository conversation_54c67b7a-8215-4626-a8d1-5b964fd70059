import { AccordionTitle } from "@/components/accordion-title";
import {
  ExpandableContent,
  ExpandableTrigger,
  Button,
  Combobox,
  DatePicker,
  toast,
} from "@kla-v2/ui-components";
import PropTypes from "prop-types";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { useLanguage } from "@/hooks";
import { zodResolver } from "@hookform/resolvers/zod";
import { Save } from "lucide-react";
import { useForm } from "react-hook-form";
import { BasicDetailsSchema } from "./form-schema";
import {
  useGetDesignationQuery,
  useGetPortfolioQuery,
  useGetSubSubjectQuery,
} from "@/services/master-data-management/basic-details-notice";
import { usePostBasicDetailsMutation } from "@/services/short-notice";
import { basicDetailsFormFields } from "./form-fields";
import { forwardRef } from "react";
import { useImperativeHandle } from "react";
import { useMemo } from "react";
import { useParams } from "react-router-dom";
import { AccordionMetadata } from "@/components/accordion-metadata";
import { resetFormWithResponseData, shouldShowTick } from "@/utils";

const BasicDetailsPage = forwardRef(
  ({ accordionOrderNo, Metadata, basicDetailsData, openNext }, ref) => {
    const { t } = useLanguage();
    const [postBasicDetails] = usePostBasicDetailsMutation();
    const { data: designationData } = useGetDesignationQuery();
    const { data: portfolioData } = useGetPortfolioQuery();
    const { data: subjectData } = useGetSubSubjectQuery();
    const { documentId } = useParams();
    const formFields = basicDetailsFormFields(t);

    const form = useForm({
      resolver: zodResolver(BasicDetailsSchema(t)),
      defaultValues: {
        [formFields.DESIGNATION.name]:
          basicDetailsData?.[formFields.DESIGNATION.name] || "",
        [formFields.MINISTER_PORTFOLIO.name]:
          basicDetailsData?.[formFields.MINISTER_PORTFOLIO.name] || "",
        [formFields.MINISTER_SUB_SUBJECT.name]:
          basicDetailsData?.[formFields.MINISTER_SUB_SUBJECT.name] || "",
        [formFields.PLACE.name]:
          basicDetailsData?.[formFields.PLACE.name] || "Trivandrum",
        [formFields.DATE.name]:
          basicDetailsData?.[formFields.DATE.name] || null,
      },
    });

    const { control, trigger, getValues } = form;
    useImperativeHandle(ref, () => ({
      async validateAndSubmit() {
        const isValid = await form.trigger();
        return isValid;
      },
    }));

    const showTick = shouldShowTick(form);

    const designation = useMemo(() => {
      if (designationData) {
        const formattedDesignations = designationData.map((item) => ({
          label: item.title,
          value: item.id.toString(),
        }));
        return formattedDesignations;
      } else return [];
    }, [designationData]);

    const portfolios = useMemo(() => {
      if (portfolioData) {
        const formattedPortfolios = portfolioData.map((item) => ({
          label: item.title,
          value: item.id.toString(),
        }));
        return formattedPortfolios;
      } else return [];
    }, [portfolioData]);

    const subSubjects = useMemo(() => {
      if (subjectData) {
        const formattedSubject = subjectData.map((item) => ({
          label: item.title,
          value: item.id.toString(),
        }));
        return formattedSubject;
      } else return [];
    }, [subjectData]);

    const handlePartialSave = async (e) => {
      e.preventDefault();
      const data = getValues();
      const noticeType = "NOTICE_FOR_SHORT_NOTICE";
      const basicDetails = {
        type: noticeType,
        ministerDesignationId: data?.ministerDesignationId,
        portfolioId: data?.portfolioId,
        subSubjectId: data?.subSubjectId,
        place: data?.place,
        noticeDate: data?.noticeDate,
        
        // Add text values for the entities
        ministerDesignation: designation.find(d => d.value === data?.ministerDesignationId)?.label || "",
        portfolio: portfolios.find(p => p.value === data?.portfolioId)?.label || "",
        subSubject: subSubjects.find(s => s.value === data?.subSubjectId)?.label || "",
      };
      try {
        const response = await postBasicDetails({
          documentId,
          data: basicDetails,
        }).unwrap();
        resetFormWithResponseData(form, BasicDetailsSchema(t), response);
        toast.success(t("toast.documentSave"));
        openNext();
      } catch (error) {
        toast.error(error?.message);
      }
    };

    return (
      <>
        <ExpandableTrigger showTick={showTick} variant="secondary">
          <AccordionTitle
            accordionOrderNo={accordionOrderNo}
            accordionTitle={t("form:basicDetails")}
          />
        </ExpandableTrigger>
        <ExpandableContent>
          <div className="w-full flex flex-col">
            <div className="py-4 px-6">
              <Form {...form} className="w-full">
                <form onSubmit={(e) => handlePartialSave(e)} noValidate>
                  <div>
                    <AccordionMetadata
                      accordionMetadata={Metadata}
                      className="mb-6"
                    />
                  </div>
                  <div className="grid grid-cols-3 gap-4">
                    <FormField
                      control={control}
                      name={formFields.DESIGNATION.name}
                      render={({ field }) => {
                        return (
                          <FormItem>
                            <FormControl>
                              <Combobox
                                className="w-full"
                                value={field.value?.toString() || ""}
                                options={designation}
                                {...formFields.DESIGNATION}
                                {...field}
                                onValueChange={(value) => {
                                  field.onChange(value);
                                  trigger(formFields.DESIGNATION.name);
                                }}
                              />
                            </FormControl>
                          </FormItem>
                        );
                      }}
                    />

                    <FormField
                      control={form.control}
                      name={formFields.MINISTER_PORTFOLIO.name}
                      render={({ field }) => {
                        return (
                          <FormItem>
                            <FormControl>
                              <Combobox
                                className="w-full"
                                value={field.value?.toString() || ""}
                                onValueChange={(value) => {
                                  field.onChange(value);
                                  trigger(formFields.MINISTER_PORTFOLIO.name);
                                }}
                                options={portfolios}
                                {...formFields.MINISTER_PORTFOLIO}
                                {...field}
                              />
                            </FormControl>
                          </FormItem>
                        );
                      }}
                    />
                    <FormField
                      control={control}
                      name={formFields.MINISTER_SUB_SUBJECT.name}
                      render={({ field }) => {
                        return (
                          <FormItem>
                            <FormControl>
                              <Combobox
                                className="w-full"
                                value={field.value?.toString() || ""}
                                onValueChange={(value) => {
                                  field.onChange(value);
                                  trigger(formFields.MINISTER_SUB_SUBJECT.name);
                                }}
                                options={subSubjects}
                                {...formFields.MINISTER_SUB_SUBJECT}
                                {...field}
                              />
                            </FormControl>
                          </FormItem>
                        );
                      }}
                    />
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <FormField
                        control={control}
                        name={formFields.PLACE.name}
                        render={({ field }) => {
                          return (
                            <FormItem>
                              <FormControl>
                                <Combobox
                                  className="w-full"
                                  {...formFields.PLACE}
                                  {...field}
                                  value={field.value?.toString() || ""}
                                  onValueChange={(value) => {
                                    field.onChange(value);
                                    trigger(formFields.PLACE.name);
                                  }}
                                  options={[
                                    {
                                      label: "Trivandrum",
                                      value: "Trivandrum",
                                    },
                                  ]}
                                />
                              </FormControl>
                            </FormItem>
                          );
                        }}
                      />
                    </div>

                    <div>
                      <FormField
                        control={control}
                        name={formFields.DATE.name}
                        render={({ field }) => {
                          return (
                            <FormItem>
                              <FormControl>
                                <DatePicker
                                  className="w-full"
                                  value={field.value}
                                  options={[]}
                                  {...formFields.DATE}
                                  {...field}
                                  onChange={(value) => {
                                    field.onChange(value);
                                    trigger(formFields.DATE.name);
                                  }}
                                />
                              </FormControl>
                            </FormItem>
                          );
                        }}
                      />
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button variant="secondary" className="flex items-center">
                      Save <Save className="w-4 h-4" />
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          </div>
        </ExpandableContent>
      </>
    );
  }
);
BasicDetailsPage.displayName = "BasicDetailsPage";

BasicDetailsPage.propTypes = {
  accordionOrderNo: PropTypes.number,
  Metadata: PropTypes.object,
  basicDetailsData: PropTypes.object,
  openNext: PropTypes.func,
};

export { BasicDetailsPage };
