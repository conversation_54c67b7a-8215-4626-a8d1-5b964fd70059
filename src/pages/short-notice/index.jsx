import { DocumentMetadata } from "@/components/document-metadata";
import { useAccordion, useLanguage } from "@/hooks";
import ArrowRight from "@/icons/arrow-right-icon";
import { useShortNoticeSubmitMutation } from "@/services/short-notice";
import {
  resetBreadcrumb,
  setBreadcrumb,
} from "@/slices/layout/breadcrumb-slice";
import {
  Button,
  ExpandableAccordion,
  ExpandableItem,
  toast,
} from "@kla-v2/ui-components";
import { ArrowLeft } from "lucide-react";
import { createContext, useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { NoticeDetailsPage } from "../../components/accordions/notice-page";
import { BasicDetailsPage } from "./components/basic-details";
import { ExplanatoryNote } from "./components/explanatory-note";
import SubmitPreview from "./components/short-notice-submit-preview";
import { useLoaderData } from "react-router-dom";

const NoticeContext = createContext();

function CreateShortNotice() {
  const dispatch = useDispatch();
  const { t } = useLanguage();
  const documentName = t("Create Short Notice");
  const { documentId } = useParams();
  const navigate = useNavigate();
  const { documentData } = useLoaderData();
  const basicDetailsRef = useRef();
  const noticeDetailsRef = useRef();
  const [submitDocument] = useShortNoticeSubmitMutation();
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);

  useEffect(() => {
    dispatch(
      setBreadcrumb([
        { label: "Home", id: "home", action: "navigate", route: "/" },
        {
          label: "Notices",
          id: "notices",
          action: "navigate",
          route: "/member/my-notices",
        },
        {
          label: "Short Notice",
          id: "documentName",
          isActive: true,
        },
      ])
    );

    return () => dispatch(resetBreadcrumb());
  }, [dispatch]);

  const formatCreatedOn = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    const options = { year: "numeric", month: "numeric", day: "numeric" };
    return date.toLocaleDateString(undefined, options);
  };

  const amendedMetadata = {
    ...documentData,
    createdOn: formatCreatedOn(documentData?.createdAt),
    createdBy: documentData?.createdBy || "",
    isDocTypeAdded: true,
    documentType: "Short Notice",
    noticeType: "Short Notice",
    member: "Smt. K.K Rema",
  };

  const goBack = () => {
    navigate("/member/my-notices");
  };
  const accordions = ["item-1", "item-2", "item-3"];
  const initialAccordion = { accordions, openAccordions: [accordions[0]] };
  const { accordionValue, setAccordionValue, openNextAccordion } =
    useAccordion(initialAccordion);

  const handleFinalSubmit = async () => {
    try {
      await submitDocument({
        documentId,
      }).unwrap();
      toast.success(t("Submitted Successfully"));
      navigate("/member/my-notices");
    } catch (error) {
      toast.error(error?.message);
    }
  };

  const handleValidation = async () => {
    const invalidAccordions = [];

    const [isBasicDetailsValid, isNoticeDetailsValid] = await Promise.all([
      await basicDetailsRef.current.validateAndSubmit(true),
      await noticeDetailsRef.current.validateAndSubmit(true),
    ]);
    if (!isBasicDetailsValid) invalidAccordions.push("item-1");
    if (!isNoticeDetailsValid) invalidAccordions.push("item-2");
    if (invalidAccordions.length > 0) {
      setAccordionValue([...invalidAccordions]);
      return;
    }
    setShowPreviewDialog(true);
  };

  return (
    <div className="px-5 py-3 bg-background flex flex-col min-h-screen">
      <div className="flex justify-between">
        <h1 className="typography-page-heading">{documentName}</h1>
      </div>
      <div className="w-full py-5">
        <DocumentMetadata documentMetadata={amendedMetadata} />
      </div>
      <ExpandableAccordion
        type="multiple"
        value={accordionValue}
        onValueChange={setAccordionValue}
        className="w-full flex flex-col gap-4 flex-grow"
      >
        <ExpandableItem value={`item-1`}>
          <BasicDetailsPage
            ref={basicDetailsRef}
            accordionOrderNo={1}
            Metadata={amendedMetadata}
            basicDetailsData={documentData}
            openNext={() => openNextAccordion(accordions[0])}
          />
        </ExpandableItem>
        <ExpandableItem value={`item-2`}>
          <NoticeDetailsPage
            ref={noticeDetailsRef}
            accordionOrderNo={2}
            noticeDetailsData={documentData}
            openNext={() => openNextAccordion(accordions[1])}
          />
        </ExpandableItem>
        <ExpandableItem value={`item-3`}>
          <ExplanatoryNote
            accordionOrderNo={3}
            explanatoryDetails={documentData}
            openNext={() => openNextAccordion(accordions[2])}
          />
        </ExpandableItem>
      </ExpandableAccordion>

      <div className="flex justify-end items-center gap-4 py-4 bg-background">
        <Button
          variant="neutral"
          className="flex items-center gap-3 text-black border border-gray-200 rounded-md"
          onClick={goBack}
          icon={ArrowLeft}
          iconPosition="left"
        >
          <span>{t("back")}</span>
        </Button>
        <Button
          size="md"
          variant="primary"
          iconPosition="right"
          icon={ArrowRight}
          type="submit"
          onClick={handleValidation}
        >
          {t("submit")}
        </Button>
        {showPreviewDialog && (
          <SubmitPreview
            isOpen={showPreviewDialog}
            onClose={() => setShowPreviewDialog(false)}
            onConfirm={handleFinalSubmit}
            documentId={documentId}
          />
        )}
      </div>
    </div>
  );
}

export { CreateShortNotice, NoticeContext };
