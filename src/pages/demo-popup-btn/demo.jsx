import ClausePopUpModal from "@/components/pop-up/clause-popup";
import { useState } from "react";
import { Button } from "@kla-v2/ui-components";

const PopupButton = () => {
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  return (
    <div className="h-full flex items-center justify-center gap-2">
      <Button
        type="button"
        variant="primary"
        className="flex items-center"
        onClick={() => setIsPreviewOpen(true)}
      >
        Clause Pop Up
      </Button>
      <ClausePopUpModal
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
      />
    </div>
  );
};

export default PopupButton;
