import { describe, it, expect, vi } from "vitest";
import { screen } from "@testing-library/react";
import { renderApp } from "@/testing/utils";
import { Route, Routes } from "react-router-dom";
import ScheduleOfActivityPage from "./index";

vi.mock("@kla-v2/ui-components", async () => {
  const actual = await vi.importActual("@kla-v2/ui-components");
  return {
    ...actual,
    toast: {
      success: vi.fn(),
      error: vi.fn(),
    },
  };
});

vi.mock("@/hooks", () => {
  const translations = {
    "document.fields.assembly": "Assembly",
    "document.fields.session": "Session",
    "document.fields.type": "Schedule of Activity",
    "document.fields.currentNumber": "Current number",
    "document.fields.createdBy": "Created by",
    "document.actions.preview": "preview",
    "document.actions.save": "save",
    "document.actions.submit": "submit",
    "document.actions.back": "back",
  };

  return {
    useLanguage: () => ({
      t: (key) => translations[key] || key,
    }),
  };
});

// Mock services module
vi.mock("@/services/schedule-of-activity", () => {
  return {
    useGetScheduleOfActivityQuery: () => ({
      data: {
        id: "test-123",
        name: "Test Schedule",
        assembly: 15,
        session: 3,
        type: "Schedule of Activity",
        currentNumber: "SOA-001",
        createdAt: "2025-05-01T12:00:00.000Z",
        createdBy: "Test User",
        scheduleOfActivityEntries: [
          { id: 1, date: "2025-05-10", description: "First Activity" },
          { id: 2, date: "2025-05-15", description: "Second Activity" },
        ],
      },
      isLoading: false,
      error: null,
    }),
    useUpdateScheduleOfActivityMutation: () => [
      vi.fn().mockResolvedValue({ data: "updated" }),
      { isLoading: false },
    ],
    useSubmitScheduleOfActivityMutation: () => [
      vi.fn().mockResolvedValue({ data: "submitted" }),
      { isLoading: false },
    ],
    scheduleOfActivityApi: {
      reducerPath: "scheduleOfActivityApi",
      reducer: () => ({}),
      middleware: () => (next) => (action) => next(action),
    },
  };
});

// Mock the ScheduleTable component
vi.mock("./components/ScheduleTable", () => ({
  default: ({ entries }) => (
    <div data-testid="schedule-table">
      {entries.map((entry) => (
        <div key={entry.id} data-testid={`entry-${entry.id}`}>
          {entry.description}
        </div>
      ))}
    </div>
  ),
}));

describe("ScheduleOfActivityPage", () => {
  it.todo(
    "renders the schedule of activity page with correct data",
    async () => {
      await renderApp(
        <Routes>
          <Route path="*" element={<ScheduleOfActivityPage />} />
        </Routes>
      );
      screen.debug();
      // Check document metadata labels
      expect(screen.findByText(/Assembly/i)).toBeInTheDocument();

      expect(screen.getByText(/session/i)).toBeInTheDocument();
      expect(screen.getByText("Schedule of Activity")).toBeInTheDocument();
      expect(screen.getByText(/Current number/i)).toBeInTheDocument();
      expect(screen.getByText(/Created by/i)).toBeInTheDocument();

      // Check document metadata values
      expect(screen.getByText("15")).toBeInTheDocument();
      expect(screen.getByText("3")).toBeInTheDocument();
      expect(screen.getByText("SOA-001")).toBeInTheDocument();
      expect(screen.getByText("Test User")).toBeInTheDocument();

      // Check that buttons are present
      expect(screen.getByText("preview")).toBeInTheDocument();
      expect(screen.getByText("save")).toBeInTheDocument();
      expect(screen.getByText("submit")).toBeInTheDocument();
      expect(screen.getByText("back")).toBeInTheDocument();

      // Check schedule table is rendered
      expect(screen.getByTestId("schedule-table")).toBeInTheDocument();
      expect(screen.getByText("First Activity")).toBeInTheDocument();
      expect(screen.getByText("Second Activity")).toBeInTheDocument();
    }
  );
});
