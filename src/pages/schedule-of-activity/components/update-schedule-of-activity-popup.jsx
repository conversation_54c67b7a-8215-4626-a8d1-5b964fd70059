import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogPort<PERSON>,
  toast,
  Badge,
  MultiSelectDropdown,
} from "@kla-v2/ui-components";
import { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { useLanguage } from "@/hooks";
import { GroupBadge } from "../../../components/group-badge";
import ErrorCircleIcon from "@/icons/error-circle-icon";
import { useGetSoaUpdateQuery } from "@/services/schedule-of-activity-updates";
import { Calendar } from "lucide-react";

const UpdateSoaPopup = ({ isOpen, onClose, isPreview = false }) => {
  const { t } = useLanguage();
  const [groups, setGroups] = useState([]);
  const [assignedGroups, setAssignedGroups] = useState({});
  const { data: dataUpdates } = useGetSoaUpdateQ<PERSON>y();

  useEffect(() => {
    const groupOptions = Array.from({ length: 5 }, (_, index) => ({
      label: <GroupBadge groupNumber={index + 1} />,
      value: index + 1,
    }));
    setGroups(groupOptions);
  }, []);

  useEffect(() => {
    const initialAssignments = {};
    dataUpdates?.data.updates.forEach((row, idx) => {
      if (row.group === null) {
        initialAssignments[idx] = [4];
      }
    });
    setAssignedGroups(initialAssignments);
  }, [dataUpdates]);

  const handleSubmit = () => {
    try {
      onClose();
      toast.success("Success", { description: t("Submitted") });
    } catch (err) {
      toast.error("Error submitting document", {
        description: err?.message,
      });
    }
  };

  const handleRemoveGroup = (index) => {
    const updated = { ...assignedGroups };
    updated[index] = [];
    setAssignedGroups(updated);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose} className="overflow-hidden">
      <DialogPortal>
        <DialogContent className="max-w-[1255px] h-[443px] flex flex-col">
          <DialogHeader>
            <DialogTitle className="px-2">{t("aodUpdates")}</DialogTitle>
            <div className="pt-1 px-2 text-sm text-gray-400">
              {dataUpdates?.data.updateBy}
            </div>
          </DialogHeader>

          <div className="flex-2 overflow-y-auto w-full">
            <div className="flex justify-between text-gray-600 text-sm py-2">
              <div className="w-[220px] flex justify-between px-3">
                <span className="text-gray-400">{t("form:date")}</span>
                <span className="text-gray-400">{t("status")}</span>
              </div>
              <span className="mr-2 text-gray-400">
                {t("form:assignedGroups")}
              </span>
            </div>

            {dataUpdates?.data.updates.map((row, index) => {
              const isNew = row.shiftedDate === null;
              const badgeText = isNew ? "Newly Added" : row.shiftedDate;
              const badgeColor = isNew
                ? "bg-[#E4F9EC] text-success"
                : "bg-[#FEF3F2] text-error";
              const textColor = isNew ? "text-success" : "text-error";

              return (
                <div
                  key={index}
                  className="grid grid-cols-12 items-center px-2 py-3 border border-gray-200 rounded mb-3 bg-white"
                >
                  <div className="col-span-7 flex items-center gap-3">
                    <div
                      className={`flex items-center gap-2 text-sm font-normal ${textColor}`}
                      style={{ fontFamily: "Inter" }}
                    >
                      <span className="px-2">{row.date}</span>
                      <Calendar className={`w-4 h-4 ${textColor}`} />
                    </div>
                    <div className="col-span-10 flex  justify-end items-center gap-2 text-sm">
                      {isNew ? (
                        <Badge
                          variant="solid"
                          className={`text-sm border-none min-w-fit px-3 h-[30px] flex justify-center items-center gap-2 ${badgeColor}`}
                        >
                          {badgeText}
                        </Badge>
                      ) : (
                        <>
                          <span className="text-gray-500">
                            {t("form:moveTo")}
                          </span>
                          <Badge
                            variant="solid"
                            className={`text-sm border-none min-w-fit px-3 h-[30px] flex justify-center items-center gap-2 ${badgeColor}`}
                          >
                            {badgeText}
                          </Badge>
                        </>
                      )}
                    </div>
                  </div>

                  <div className="col-span-5 flex justify-end items-center gap-2 text-sm">
                    {row.group ? (
                      <GroupBadge groupNumber={row.group} />
                    ) : (
                      <div className="flex items-center gap-2 h-[20px]">
                        {assignedGroups[index]?.length > 0 ? (
                          <>
                            <GroupBadge
                              groupNumber={assignedGroups[index][0]}
                            />
                            <ErrorCircleIcon
                              onClick={() => handleRemoveGroup(index)}
                            />
                          </>
                        ) : (
                          <>
                            <span className="text-gray-400">
                              {t("notAssigned")}
                            </span>
                            <MultiSelectDropdown
                              options={groups}
                              value={assignedGroups[index] || []}
                              onSelectionChange={(selected) => {
                                const updated = {
                                  ...assignedGroups,
                                  [index]: selected
                                    .slice(0, 1)
                                    .map((m) => m.value),
                                };
                                setAssignedGroups(updated);
                              }}
                              addLabel="Add Group"
                              maxCount={1}
                            />
                          </>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          <DialogFooter className="flex justify-end gap-2 px-1">
            {isPreview ? (
              <DialogClose asChild>
                <Button size="sm" variant="primary">
                  {t("Close")}
                </Button>
              </DialogClose>
            ) : (
              <>
                <DialogClose asChild>
                  <Button size="lg" variant="secondary">
                    {t("Cancel")}
                  </Button>
                </DialogClose>
                <DialogClose asChild>
                  <Button size="lg" variant="primary" onClick={handleSubmit}>
                    {t("Apply")}
                  </Button>
                </DialogClose>
              </>
            )}
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

UpdateSoaPopup.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  isPreview: PropTypes.bool,
  onClose: PropTypes.func.isRequired,
};

export default UpdateSoaPopup;
