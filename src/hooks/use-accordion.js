import { useReducer } from "react";

const actionTypes = {
  SET_VALUE: "SET_VALUE",
  OPEN_NEXT: "OPEN_NEXT",
  OPEN_ACCORDION: "OPEN_ACCORDION",
  CLOSE_ACCORDION: "CLOSE_ACCORDION",
};

function accordionReducer(state, action) {
  switch (action.type) {
    case actionTypes.SET_VALUE: {
      return { ...state, openAccordions: action.payload };
    }
    case actionTypes.OPEN_NEXT: {
      const accordions = state.accordions;
      const currentAccordion = action.payload;
      const indexOfCurrentAccordion = accordions.indexOf(currentAccordion);
      const indexOfNextAccordion = indexOfCurrentAccordion + 1;

      const openAccordions = new Set(state.openAccordions);
      openAccordions.delete(currentAccordion);

      if (indexOfNextAccordion < accordions.length)
        openAccordions.add(accordions[indexOfNextAccordion]);

      return { ...state, openAccordions: [...openAccordions] };
    }
    case actionTypes.OPEN_ACCORDION: {
      const openAccordions = new Set([...state.openAccordions, action.payload]);
      return { ...state, openAccordions: [...openAccordions] };
    }
    case actionTypes.CLOSE_ACCORDION: {
      const openAccordions = new Set([...state.openAccordions]);
      openAccordions.delete(action.payload);
      return { ...state, openAccordions: [...openAccordions] };
    }
    default:
      return state;
  }
}

function useAccordion(initialState) {
  const [state, dispatch] = useReducer(accordionReducer, initialState);

  const setAccordionValue = (value) => {
    dispatch({
      type: actionTypes.SET_VALUE,
      payload: value,
    });
  };

  const openNextAccordion = (currentAccordion) => {
    dispatch({
      type: actionTypes.OPEN_NEXT,
      payload: currentAccordion,
    });
  };

  const openAccordion = (value) => {
    dispatch({
      type: actionTypes.OPEN_ACCORDION,
      payload: value,
    });
  };

  const closeAccordion = (value) => {
    dispatch({
      type: actionTypes.CLOSE_ACCORDION,
      payload: value,
    });
  };

  return {
    accordionValue: state.openAccordions,
    setAccordionValue,
    openAccordion,
    closeAccordion,
    openNextAccordion,
  };
}

export { useAccordion };
