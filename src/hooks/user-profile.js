import { useGetCurrentUserProfileQuery } from "../services/master-data-management/user-profile";

export const useUserProfile = () => {
  const {
    data,
    isLoading,
    error,
    refetch
  } = useGetCurrentUserProfileQuery();

  // Extract user profile from the response
  const userProfile = data?.content?.[0] || null;

  return {
    userProfile,
    isLoading,
    error: error?.message || error,
    refetch,
  };
};