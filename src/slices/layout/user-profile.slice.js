import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  userProfile: null,
  isLoading: false,
  error: null,
};

export const userProfileSlice = createSlice({
  name: "userProfile",
  initialState,
  reducers: {
    setUserProfile: (state, action) => {
      state.userProfile = action.payload;
    },
    clearUserProfile: (state) => {
      state.userProfile = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addMatcher(
        (action) => action.type.endsWith("/pending") && action.type.includes("userProfile"),
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        (action) => action.type.endsWith("/fulfilled") && action.type.includes("userProfile"),
        (state, action) => {
          state.isLoading = false;
          state.userProfile = action.payload?.content?.[0] || null;
        }
      )
      .addMatcher(
        (action) => action.type.endsWith("/rejected") && action.type.includes("userProfile"),
        (state, action) => {
          state.isLoading = false;
          state.error = action.error.message;
        }
      );
  },
});

export const { setUserProfile, clearUserProfile } = userProfileSlice.actions;

export const selectUserProfile = (state) => state.userProfile?.userProfile || null;
export const selectIsLoading = (state) => state.userProfile?.isLoading || false;
export const selectError = (state) => state.userProfile?.error || null;

export default userProfileSlice.reducer;