import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  items: [],
};

const breadcrumbSlice = createSlice({
  name: "breadcrumb",
  initialState,
  reducers: {
    setBreadcrumb(state, action) {
      state.items = action.payload;
    },
    resetBreadcrumb: () => initialState,
  },
});

export const { setBreadcrumb, resetBreadcrumb } = breadcrumbSlice.actions;
export default breadcrumbSlice.reducer;

export const selectBreadcrumbItems = (state) => state.breadcrumb.items;
