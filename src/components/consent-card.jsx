import { useLanguage } from "@/hooks";
import TickIcon from "@/icons/tick_icon";
import {
  useAcceptConsentMutation,
  useRejectConsentMutation,
  useRevokeConsentMutation,
  useWithdrawConsentMutation,
} from "@/services/consent";
import { cn } from "@/utils";
import { Dialogbox } from "@kla-v2/ui-components";
import { Check, TriangleAlert, Unlink, WrapText, X } from "lucide-react";
import PropTypes from "prop-types";
import { useState } from "react";
import { useLocation } from "react-router-dom";

export default function ConsentCard({ data, hideActions = false }) {
  const { t } = useLanguage();
  const location = useLocation();
  const [addAcceptConsent] = useAcceptConsentMutation();
  const [addRejectConsent] = useRejectConsentMutation();
  const [addRevokeConsent] = useRevokeConsentMutation();
  const [addWithdrawConsent] = useWithdrawConsentMutation();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [actionType, setActionType] = useState("");
  const getDialogDescription = (action) => {
    switch (action) {
      case "accept":
        return t("form:acceptConfirm");
      case "reject":
        return t("form:declineConfirm");
      case "revoke":
        return t("form:revokeConfirm");
      case "withdraw":
        return t("form:withdrawConfirm");
    }
  };
  const handleConfirm = async () => {
    try {
      if (actionType === "accept") {
        await addAcceptConsent({ consentId: data.id }).unwrap();
      } else if (actionType === "reject") {
        await addRejectConsent({ consentId: data.id }).unwrap();
      } else if (actionType === "revoke") {
        await addRevokeConsent({ consentId: data.id }).unwrap();
      } else if (actionType === "withdraw") {
        await addWithdrawConsent({ consentId: data.id }).unwrap();
      }
    } catch (err) {
      console.error(`Error ${actionType}ing Consent:`, err);
    }
    setIsDialogOpen(false);
  };
  return (
    <div className="flex  h-[116px] p-[18px] justify-between items-center rounded-[12px] border-2 border-gray-300 bg-white shadow-sm">
      <div className="flex gap-4 items-center">
        <div>
          <img src="https://placehold.co/400" width={88} height={88} />
        </div>
        <div className="flex flex-col  min-w-0">
          <span className="typography-body-text-s-14 text-gray-500">
            {data.consentType === "PPO" &&
            // TODO change after authentication
            location.pathname.includes("mla-consent")
              ? "Parliamentary Party"
              : "Constituency Name"}
          </span>
          <span className="typography-body-text-s-16 text-foreground">
            {data.consentType === "PPO" &&
            // TODO change after authentication
            location.pathname.includes("mla-consent")
              ? data.politicalPartyName
              : data.memberDisplayName}
          </span>
        </div>
      </div>
      <div
        className={cn(
          "flex flex-col items-end grow h-full justify-evenly gap-4"
        )}
      >
        {!hideActions ? (
          data.status === "PENDING" ? (
            <>
              <button
                className="flex items-center typography-body-text-s-16 text-green-600 gap-2"
                onClick={() => {
                  setActionType("accept");
                  setIsDialogOpen(true);
                }}
              >
                <Check size={16} /> {t("accept")}
              </button>
              <button
                className="flex items-center typography-body-text-s-16 text-red-500 mt-1 gap-2"
                onClick={() => {
                  setActionType("reject");
                  setIsDialogOpen(true);
                }}
              >
                <X size={16} /> {t("decline")}
              </button>
            </>
          ) : data.status === "ACCEPTED" &&
            !hideActions &&
            location.pathname.includes("mla-consent") ? (
            <button
              className="text-primary flex items-center typography-body-text-s-16 gap-2"
              onClick={() => {
                setActionType("revoke");
                setIsDialogOpen(true);
              }}
            >
              <WrapText size={16} /> {t("revoke")}
            </button>
          ) : (
            <div className="flex  flex-col justify-between items-end space-x-2 gap-6 ">
              <div className="flex items-center gap-2">
                <span className="typography-body-text-s-14 text-success">
                  {t("accepted")}
                </span>
                <TickIcon className="!w-3 !h-3" />
              </div>
              <div>
                <button
                  className="text-error flex items-center typography-body-text-s-14 gap-1"
                  onClick={() => {
                    setActionType("withdraw");
                    setIsDialogOpen(true);
                  }}
                >
                  {t("withdraw")}
                  <X size={16} />
                </button>
              </div>
            </div>
          )
        ) : (
          <div className="flex items-center space-x-2">
            {data.status === "ACCEPTED" &&
            location.pathname.includes("mla-consent") ? (
              <div className="flex  flex-col justify-between items-end space-x-2 gap-6 ">
                <div className="flex items-center gap-2">
                  <span className="typography-body-text-s-14 text-success">
                    {t("accepted")}
                  </span>
                  <TickIcon className="!w-3 !h-3" />
                </div>
                <div>
                  <button
                    className="text-error flex items-center typography-body-text-s-14 gap-1"
                    onClick={() => {
                      setActionType("withdraw");
                      setIsDialogOpen(true);
                    }}
                  >
                    {t("withdraw")}
                    <X size={16} />
                  </button>
                </div>
              </div>
            ) : data.status === "REJECTED" ? (
              <div className="flex items-center space-x-2 typography-body-text-s-14 text-error">
                <span>{t("declined")}</span>
                <Unlink className="w-3 h-3" />
              </div>
            ) : data.status === "PENDING" ? (
              <div className="flex items-center space-x-2 typography-body-text-s-14 text-warning">
                <span>{t("pending")}</span>
                <TriangleAlert className="w-3 h-3" />
              </div>
            ) : (
              <div className="flex items-center space-x-2 typography-body-text-s-14 text-primary">
                <span>{t("revoked")}</span>
                <Unlink className="w-3 h-3" />
              </div>
            )}
          </div>
        )}
      </div>
      <Dialogbox
        dialogDescription={getDialogDescription(actionType)}
        dialogTitle={t("confirmAction")}
        onCancel={() => setIsDialogOpen(false)}
        onOpenChange={setIsDialogOpen}
        onSubmit={handleConfirm}
        primaryActionText={t("confirm")}
        secondaryActionText={t("cancel")}
        open={isDialogOpen}
      />
    </div>
  );
}

ConsentCard.propTypes = {
  data: PropTypes.object,
  hideActions: PropTypes.bool,
};
