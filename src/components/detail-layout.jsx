import { IconButton } from "@kla-v2/ui-components";
import {
  ChevronLeftIcon,
  PanelBottomIcon,
  LayoutGridIcon,
  EllipsisIcon,
  ChevronRightIcon,
} from "lucide-react";

import PropTypes from "prop-types";

function DetailLayout({ name }) {
  return (
    <div className="ml-2 h-10 flex-col gap-4">
      <div className="flex justify-between items-center gap-4">
        <h3 className="typography-page-heading text-grey-600">{name}</h3>
        <div className="flex gap-4 items-center">
          <div className="h-6 w-11 rounded border border-border-1 divide-x divide-border-1 flex items-center justify-center">
            <IconButton
              variant="textInfo"
              className="w-6 h-6 p-1 rounded-none"
              aria-label="Chevron Left"
              icon={ChevronLeftIcon}
            />
            <IconButton
              variant="textInfo"
              className="w-6 h-6 p-1 rounded-none"
              aria-label="Chevron Right"
              icon={ChevronRightIcon}
            />
          </div>
          <IconButton
            variant="textInfo"
            className="w-6 h-6 p-0 rounded-none"
            aria-label="Ellipsis"
            icon={EllipsisIcon}
          />

          <IconButton
            variant="textInfo"
            className="w-6 h-6 p-0 rounded-none text-primary"
            aria-label="Panel Bottom"
            icon={PanelBottomIcon}
          />

          <IconButton
            variant="textInfo"
            className="w-6 h-6 p-0 rounded-none"
            aria-label="Layout Grid"
            icon={LayoutGridIcon}
          />
        </div>
      </div>
    </div>
  );
}
DetailLayout.propTypes = {
  name: PropTypes.string,
};

export default DetailLayout;
