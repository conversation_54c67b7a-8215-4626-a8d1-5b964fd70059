import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import { AccordionMetadata } from "./index";

describe("DocumentMetadata Component", () => {
  const mockMetadata = {
    noticeType: "SRO",
    member: "Parent Rule/Order",
    assembly: "15",
    session: "13",
  };

  it("renders title and type when isTitle is true", () => {
    const metadata = { ...mockMetadata };
    render(<AccordionMetadata accordionMetadata={metadata} />);

    // Ensure other elements are not rendered
    expect(screen.queryByText("Notice Type")).not.toBeInTheDocument();
    expect(screen.queryByText("Member")).not.toBeInTheDocument();
    expect(screen.queryByText("KLA")).not.toBeInTheDocument();
    expect(screen.queryByText("Session")).not.toBeInTheDocument();
  });
});
