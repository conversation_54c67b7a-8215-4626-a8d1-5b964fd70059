import { useLanguage } from "@/hooks";
import { getLabels } from "@/utils/document-metadata";
import PropTypes from "prop-types";
import DocumentMetadataItem from "./accordion-metadata-item";
import { cn } from "@/lib/utils";

const AccordionMetadata = ({ accordionMetadata, className }) => {
  const { t } = useLanguage();
  const metaLabels = getLabels(t);
  const { noticeType, createdBy, assembly, session } = accordionMetadata;

  return (
    <div className={cn("p-3 mb-2 rounded-t-xl bg-warning-50", className)}>
      <div className="flex items-center justify-between">
        <div className="flex gap-4">
          {assembly && (
            <DocumentMetadataItem
              label={metaLabels.assembly}
              value={assembly}
              section="left"
              variant="small"
            />
          )}
          {session && (
            <DocumentMetadataItem
              label={metaLabels.SESSION}
              value={session}
              section="left"
              variant="small"
            />
          )}
        </div>

        <div>
          {noticeType && (
            <DocumentMetadataItem
              label="Notice Type"
              value={noticeType}
              section="left"
              variant="small"
            />
          )}
        </div>

        <div>
          {createdBy && (
            <DocumentMetadataItem
              label="Member"
              value={createdBy}
              section="left"
              variant="small"
            />
          )}
        </div>
      </div>
    </div>
  );
};

AccordionMetadata.propTypes = {
  accordionMetadata: PropTypes.shape({
    noticeType: PropTypes.string,
    member: PropTypes.string,
    assembly: PropTypes.number,
    session: PropTypes.number,
    createdBy: PropTypes.string,
  }),
  className: PropTypes.string,
};

export { AccordionMetadata };
