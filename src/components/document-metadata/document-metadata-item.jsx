import PropTypes from "prop-types";
import { cva } from "class-variance-authority";
import { cn } from "@/utils";

// Define typography classes using CVA
const labelClasses = cva("text-gray-600 typography-body-text-r-12", {
  base: "typography-body-text-r-12",
});

const valueClasses = cva("", {
  variants: {
    size: {
      small: "typography-body-text-m-12",
      medium: "typography-body-text-m-14",
      large: "typography-body-text-m-16",
    },
  },
  defaultVariants: {
    size: "small",
  },
});

const DocumentMetadataItem = ({ label, value, variant = "medium" }) => {
  return (
    <div>
      {label && <span className={cn(labelClasses())}>{`${label}: `}</span>}
      <span className={cn(valueClasses({ size: variant }))}>{value}</span>
    </div>
  );
};

DocumentMetadataItem.propTypes = {
  label: PropTypes.string,
  value: PropTypes.string,
  variant: PropTypes.oneOf(["small", "medium", "large"]),
};

export default DocumentMetadataItem;
