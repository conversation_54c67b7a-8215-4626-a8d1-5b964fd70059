import { formatDate } from "@/utils/date-utils";
import PropTypes from "prop-types";

const AnswerBulletinHeader = ({ document }) => {
  const questionDates =
    document?.lateAnswerStats?.map((stat) => stat.questionDate) || [];
  const createdAt = document?.createdAt?.split("T")[0] || "";
  const documentType = document?.type;

  const getHeaderContent = () => {
    switch (documentType) {
      case "LATE_ANSWER_BULLETIN":
        return (
          <p className="mb-6 text-justify">
            {document?.assembly || "___"} കേരള നിയമസഭയുടെ{" "}
            {document?.session || "___"}
            സമ്മേളനത്തിൽ {questionDates.map(formatDate).join(", ")} എന്നീ
            തീയതികളിലെ ചോദ്യങ്ങളുടെ പട്ടികയിൽ ഉൾപ്പെടുത്തിയിരുന്നതും യഥാസമയം
            ഉത്തരം ലഭിക്കാത്തതും എന്നാൽ {formatDate(createdAt)} വരെ ഉത്തരം
            ലഭിച്ചതുമായ ചോദ്യങ്ങളുടെ നമ്പർ താഴെ കൊടുത്തിരിക്കുന്നു. ഉത്തരങ്ങൾ
            നിയമസഭയുടെ ഔദ്യോഗിക വെബ് സൈറ്റിൽ ലഭ്യമാണ്.
          </p>
        );
      case "DELAYED_ANSWER_BULLETIN":
        return (
          <p className="mb-6 text-justify">
            {document?.assembly || "___"} കേരള നിയമസഭയുടെ
            {document?.session || "___"} സമ്മേളനത്തിൽ ചുവടെ ചേർത്തിരിക്കുന്ന
            നിയമസാഭാ സമ്മേളനങ്ങളിലെ ചോദ്യങ്ങളുടെ മറുപടികൾക്ക് കേരള നിയമസഭയുടെ
            നടപടിക്രമവും കാര്യനിർവ്വഹണവും സംബന്ധിച്ച ചട്ടം 47(2) പ്രകാരം{" "}
            {formatDate(createdAt)} വരെ ലഭിച്ച അന്തിമ മറുപടിയുടേയും
            കാലതാമസപത്രികയുടേയും ചോദ്യ നമ്പരുകൾ താഴെ കൊടുത്തിരിക്കുന്നു.
            പ്രസ്തുത ഉത്തരങ്ങൾ നിയമസഭയുടെ ഔദ്യോഗിക വെബ് സൈറ്റിൽ ലഭ്യമാണ്. എന്നാൽ
            ഇവയുടെ കാലതാമസപത്രികകൾ ബന്ധപ്പെട്ട വകുപ്പ് മന്ത്രി
            നടപ്പുസമ്മേളനത്തിലോ അടുത്ത സമ്മേളനത്തിലോ ഏതാണോ ആദ്യം വരുന്നത് അതിൽ
            മേശപ്പുറത്ത് വയ്ക്കുന്നതാണ്.
          </p>
        );
      default:
        return (
          <p className="mb-6 text-justify">
            {document?.assembly || "___"} കേരള നിയമസഭയുടെ{" "}
            {document?.session || "___"}
            സമ്മേളനത്തിലെ ചോദ്യോത്തരങ്ങളുടെ വിവരങ്ങൾ താഴെ കൊടുത്തിരിക്കുന്നു.
          </p>
        );
    }
  };

  return (
    <div className="answer-bulletin-header">
      <div className="mb-8 text-center">
        <p className=" typography-sub-heading">
          {document?.assembly || "___"} കേരള നിയമസഭ
        </p>
        <p className="mt-1 typography-sub-heading">
          {document?.session || "___"} സമ്മേളനം
        </p>
        <p className="mt-2 typography-body-text-s-16">
          {documentType === "LATE_ANSWER_BULLETIN" ||
          documentType === "DELAYED_ANSWER_BULLETIN"
            ? "ബുള്ററിൻ ഭാഗം 2"
            : "താമസിച്ച ഉത്തരങ്ങളുടെ ബുള്ററിൻ"}
        </p>
        <hr className="border border-black w-[202px] mx-auto my-4" />
      </div>

      <div className="mb-6">
        <p className="mb-4 typography-body-text-s-14">നമ്പർ :________</p>
        {getHeaderContent()}
      </div>
    </div>
  );
};

AnswerBulletinHeader.propTypes = {
  document: PropTypes.shape({
    type: PropTypes.oneOf(["LATE_ANSWER_BULLETIN", "DELAYED_ANSWER_BULLETIN"]),
    assembly: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    session: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    createdAt: PropTypes.string,
    lateAnswerStats: PropTypes.arrayOf(
      PropTypes.shape({
        questionDate: PropTypes.string,
      })
    ),
  }),
};

AnswerBulletinHeader.defaultProps = {
  document: {
    type: undefined,
    assembly: undefined,
    session: undefined,
    createdAt: undefined,
    lateAnswerStats: [],
  },
};

export default AnswerBulletinHeader;
