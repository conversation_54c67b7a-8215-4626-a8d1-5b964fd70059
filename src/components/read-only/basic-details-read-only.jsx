import { AccordionTitle } from "@/components/accordion-title";
import { useLanguage } from "@/hooks";
import { ExpandableContent, ExpandableTrigger } from "@kla-v2/ui-components";
import PropTypes from "prop-types";
import ContentData from "../ui/content-data";
import ContentProfile from "../ui/content-profile";
import { useUserProfile } from "@/hooks/user-profile";
import BASIC_DETAILS_READONLY_FIELDS from "./basic-details-read-only-fields";

const BasicDetailsReadOnly = ({ accordionOrderNo, data, variant }) => {
  const { t } = useLanguage();
  const { userProfile } = useUserProfile();
  const showFields =
    BASIC_DETAILS_READONLY_FIELDS[variant] ||
    BASIC_DETAILS_READONLY_FIELDS.default;

  return (
    <>
      <ExpandableTrigger
        className="text-primary font-semibold text-base"
        variant="secondary"
      >
        <AccordionTitle
          accordionOrderNo={accordionOrderNo}
          accordionTitle="Basic Details"
          isColored={true}
        />
      </ExpandableTrigger>
      <ExpandableContent>
        <div className="w-full flex flex-col">
          <div className="flex flex-col gap-4 p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {showFields.includes("assembly") && data?.assembly && (
                <ContentData
                  className="w-full"
                  title={t("form:KLA")}
                  data={data?.assembly || "---"}
                />
              )}
              {showFields.includes("session") && data?.session && (
                <ContentData
                  className="w-full"
                  title={t("form:session")}
                  data={data?.session || "---"}
                />
              )}

              {showFields.includes("noticeNumber") && data?.noticeNumber && (
                <ContentData
                  className="w-full"
                  title="Notice No:"
                  data={data?.noticeNumber || "---"}
                />
              )}

              {showFields.includes("noticeType") && data?.noticeType && (
                <ContentData
                  className="w-full"
                  title="Notice Type"
                  data={data?.noticeType || "---"}
                />
              )}
              {showFields.includes("type") && data?.type && (
                <ContentData
                  className="w-full"
                  title="Notice Type"
                  data={data?.type || "---"}
                />
              )}

              {showFields.includes("questionDate") && data?.questionDate && (
                <ContentData
                  className="w-full"
                  title="Question Date"
                  data={data?.questionDate || "---"}
                />
              )}
              {showFields.includes("noticeDate") && data?.noticeDate && (
                <ContentData
                  className="w-full"
                  title="Question Date"
                  data={data?.noticeDate || "---"}
                />
              )}

              {showFields.includes("place") && data?.place && (
                <ContentData
                  className="w-full"
                  title="Place"
                  data={data?.place || "---"}
                />
              )}
              {showFields.includes("portfolioId") && data?.portfolioId && (
                <ContentData
                  className="w-full"
                  title="Minister Subject / Portfolio"
                  data={data?.portfolioId || "---"}
                />
              )}
              {showFields.includes("subSubjectId") && data?.subSubjectId && (
                <ContentData
                  className="w-full"
                  title="Minister Sub Subject"
                  data={data?.subSubjectId || "---"}
                />
              )}
              {showFields.includes("ministerDesignationId") &&
                data?.ministerDesignationId && (
                  <ContentData
                    className="w-full"
                    title="Minister Designation"
                    data={data?.ministerDesignationId || "---"}
                  />
                )}

              {showFields.includes("category") && data?.category && (
                <ContentData
                  className="w-full"
                  title="Category Type"
                  data={data?.category || "---"}
                />
              )}
              {showFields.includes("nameOfResolution") &&
                data?.category == "Resolution" &&
                data?.nameOfResolution && (
                  <ContentData
                    className="w-full"
                    title="Name of Resolution"
                    data={data?.nameOfResolution || "---"}
                  />
                )}
              {showFields.includes("nameOfBill") &&
                data?.category == "Bill" && (
                  <ContentData
                    className="w-full"
                    title="Name of Bill"
                    data={data?.nameOfBill || "---"}
                  />
                )}
              {showFields.includes("others") &&
                data?.nameOfOthersCategory &&
                data?.category == "Others" && (
                  <ContentData
                    className="w-full"
                    title="Name of other category"
                    data={data?.nameOfOthersCategory || "---"}
                  />
                )}
              {showFields.includes("member") && data?.member && (
                <>
                  <ContentData
                    className="w-full"
                    title="Minister Subject/Portfolio"
                    data={userProfile?.portfolio || "---"}
                  />

                  <ContentData
                    className="w-full"
                    title="Minister Sub Subject"
                    data={userProfile?.ministerDesignation || "---"}
                  />

                  <ContentData
                    className="w-full"
                    title="Minister Designation"
                    data={userProfile?.ministerDesignation || "---"}
                  />

                  <ContentProfile
                    className="w-full"
                    title="Name of Members"
                    data={userProfile?.displayName || "---"}
                    place={userProfile?.constituencyName}
                    url={userProfile?.profileURL}
                  />
                </>
              )}
              {showFields.includes("memberName") && data?.member && (
                <ContentProfile
                  className="w-full"
                  title="Member Name"
                  data={data?.member?.memberDisplayName || "---"}
                  place={data?.member?.constituencyName}
                  url=""
                />
              )}
              {showFields.includes("submittedBy") && data?.submittedBy && (
                <ContentProfile
                  className="w-full"
                  title="Submitted by"
                  data={data?.submittedBy?.memberDisplayName || "---"}
                  place={data?.submittedBy?.constituencyName}
                  url=""
                />
              )}
            </div>
          </div>
        </div>
      </ExpandableContent>
    </>
  );
};

BasicDetailsReadOnly.propTypes = {
  data: PropTypes.object,
  accordionOrderNo: PropTypes.number,
  variant: PropTypes.string,
};

export { BasicDetailsReadOnly };
