import { AccordionTitle } from "@/components/accordion-title";
import { renderProseMirrorJSON } from "@/utils/richTestTopdf";
import { ExpandableContent, ExpandableTrigger } from "@kla-v2/ui-components";
import PropTypes from "prop-types";

const ExplanatoryReadOnly = ({ accordionOrderNo, data }) => {
  const json = JSON.parse(data?.explanatoryNote);
  const htmlContent = renderProseMirrorJSON(json);

  return (
    <>
      <ExpandableTrigger
        className="text-primary font-semibold text-base"
        variant="secondary"
      >
        <AccordionTitle
          accordionOrderNo={accordionOrderNo}
          accordionTitle="Explanatory Notice"
          isColored={true}
        />
      </ExpandableTrigger>
      <ExpandableContent>
        <div className="w-full flex flex-col">
          <div className="border border-gray-300 rounded-lg">
            <div className="w-full h-full leading-8 px-8 py-12">
              <div
                id="pdf-content"
                dangerouslySetInnerHTML={{ __html: htmlContent }}
              />
            </div>
          </div>
        </div>
      </ExpandableContent>
    </>
  );
};

ExplanatoryReadOnly.propTypes = {
  data: PropTypes.object,
  accordionOrderNo: PropTypes.number,
};

export { ExplanatoryReadOnly };
