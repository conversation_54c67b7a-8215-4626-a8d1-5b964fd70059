import { AccordionTitle } from "@/components/accordion-title";
import { ExpandableContent, ExpandableTrigger } from "@kla-v2/ui-components";
import PropTypes from "prop-types";
import ContentData from "../ui/content-data";
import { renderProseMirrorJSON } from "@/utils/richTestTopdf";

const NoriceDetailsReadOnly = ({ accordionOrderNo, data }) => {
  let htmlContent = "";

  try {
    const json = data?.description ? JSON.parse(data.description) : null;
    htmlContent = json ? renderProseMirrorJSON(json) : "";
  } catch (error) {
    console.error("Invalid JSON in data.description:", error);
  }
  return (
    <>
      <ExpandableTrigger
        className="text-primary font-semibold text-base"
        variant="secondary"
      >
        <AccordionTitle
          accordionOrderNo={accordionOrderNo}
          accordionTitle="Notice Details"
          isColored={true}
        />
      </ExpandableTrigger>
      <ExpandableContent>
        <div className="w-full flex flex-col">
          <div className="flex flex-col gap-4 p-6">
            {data?.noticeHeading && (
              <ContentData
                className="w-full"
                title="Notice Heading"
                data={data?.noticeHeading || "---"}
              />
            )}

            {htmlContent && (
              <div className="border border-gray-300 rounded-lg">
                <div className="w-full h-full leading-8 px-8 py-12">
                  <div
                    id="pdf-content"
                    dangerouslySetInnerHTML={{ __html: htmlContent }}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
        {Array.isArray(data?.clauses) && data.clauses.length > 0 && (
          <div className="w-full flex flex-col gap-4 p-6">
            {data.clauses.map((clause, index) => (
              <ContentData
                key={clause.id || index}
                className="w-full"
                title={`Clause ${index + 1}`}
                data={clause.content || "---"}
              />
            ))}
          </div>
        )}
      </ExpandableContent>
    </>
  );
};

NoriceDetailsReadOnly.propTypes = {
  data: PropTypes.object,
  accordionOrderNo: PropTypes.number,
};

export { NoriceDetailsReadOnly };
