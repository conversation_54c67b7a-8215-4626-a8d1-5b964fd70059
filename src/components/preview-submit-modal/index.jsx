import { DocumentMetadata } from "@/components/document-metadata";
import {
  <PERSON><PERSON>,
  <PERSON>alog,
  DialogClose,
  Dialog<PERSON>ontent,
  <PERSON><PERSON>Footer,
  Di<PERSON>Header,
  DialogPortal,
  DialogTitle,
  toast,
} from "@kla-v2/ui-components";
import PropTypes from "prop-types";
import { CloseToastButton } from "../ui/close-toast";
import { useLanguage } from "@/hooks";
import { useNavigate } from "react-router-dom";
import { Fragment } from "react";

const PreviewSubmitPopup = ({
  isOpen,
  onClose,
  documentId,
  submitDocument,
  metaData,
  children,
  isPreview = false,
  documentName,
}) => {
  const { t } = useLanguage();

  const showErrorToast = (errorMessage) => {
    toast.error("Error submitting document", {
      description: errorMessage,
      action: { label: <CloseToastButton /> },
    });
  };
  const navigate = useNavigate();
  const handleSubmit = async () => {
    try {
      await submitDocument({ documentId });
      onClose();
      toast.success("Success", {
        description: t("documentSubmitSuccess"),
      });

      navigate("/section/documents");
    } catch (err) {
      showErrorToast(err?.message);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose} className="overflow-hidden">
      <DialogPortal>
        <DialogContent className="max-w-[1140px] h-[700px] flex flex-col">
          <DialogHeader>
            <DialogTitle>
              <p
                className="typography-page-heading mb-4"
                data-testid="preview-submit-popup"
              >
                {isPreview ? documentName : t("previewAndSubmit")}
              </p>
              <p className="typography-sub-title-heading mb-4">
                {metaData?.name}
              </p>
              <div className="h-12">
                <DocumentMetadata documentMetadata={metaData} />
              </div>
            </DialogTitle>
          </DialogHeader>
          <div className="mt-4 bg-background flex-1 h-auto overflow-y-auto">
            <div className="py-4 ml-4 mr-4">{children}</div>
          </div>
          <DialogFooter className="flex">
            {isPreview ? (
              <DialogClose asChild>
                <Button size="sm" variant="primary">
                  {t("close")}
                </Button>
              </DialogClose>
            ) : (
              <Fragment>
                <DialogClose asChild>
                  <Button size="sm" variant="secondary">
                    {t("edit")}
                  </Button>
                </DialogClose>
                <DialogClose asChild>
                  <Button size="sm" variant="primary" onClick={handleSubmit}>
                    {t("submit")}
                  </Button>
                </DialogClose>
              </Fragment>
            )}
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

PreviewSubmitPopup.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  isPreview: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  submitDocument: PropTypes.func.isRequired,
  documentId: PropTypes.string.isRequired,
  metaData: PropTypes.object.isRequired,
  children: PropTypes.node.isRequired,
  documentName: PropTypes.string,
};

export default PreviewSubmitPopup;
