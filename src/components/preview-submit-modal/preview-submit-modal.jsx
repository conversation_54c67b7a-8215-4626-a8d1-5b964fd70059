import { DocumentMetadata } from "@/components/document-metadata";
import {
  <PERSON><PERSON>,
  <PERSON>alog,
  <PERSON>alogClose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogPortal,
  DialogTitle,
  toast,
} from "@kla-v2/ui-components";
import PropTypes from "prop-types";
import { useLanguage } from "@/hooks";
import DetailLayout from "../detail-layout";
import { XIcon } from "lucide-react";

const PreviewSubmitPopup = ({
  isOpen,
  onClose,
  documentId,
  submitDocument,
  metaData,
  children,
}) => {
  const { t } = useLanguage();

  const showErrorToast = (errorMessage) => {
    toast.error("Error submitting document", {
      description: errorMessage,
      action: {
        label: <XIcon className="size-5 text-grey-400 hover:text-foreground" />,
      },
    });
  };

  const handleSubmit = async () => {
    try {
      await submitDocument({ documentId });
      onClose();
    } catch (err) {
      showErrorToast(err?.message);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose} className="overflow-hidden">
      <DialogPortal>
        <DialogContent className="max-w-[1140px] h-[700px] flex flex-col">
          <DialogHeader>
            <DialogTitle>
              <p
                className="typography-page-heading mb-4"
                data-testid="preview-submit-popup"
              >
                {t("preview")}
              </p>
              <p className="typography-sub-title-heading mb-4">
                {metaData?.name}
              </p>
              <div className="h-12">
                <DocumentMetadata documentMetadata={metaData} />
              </div>
            </DialogTitle>
          </DialogHeader>
          <div className="bg-background flex-1 h-auto overflow-y-auto">
            <div className="p-4">
              <DetailLayout name="Details" />
            </div>
            <div className="py-4 ml-4 mr-4">{children}</div>
          </div>
          <DialogFooter className="flex">
            <DialogClose asChild>
              <Button size="sm" variant="primary" onClick={handleSubmit}>
                {t("close")}
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

PreviewSubmitPopup.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  submitDocument: PropTypes.func,
  documentId: PropTypes.string.isRequired,
  metaData: PropTypes.object.isRequired,
  children: PropTypes.node,
};

export default PreviewSubmitPopup;
