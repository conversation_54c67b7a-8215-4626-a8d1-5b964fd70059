import { useLanguage } from "@/hooks";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogHeader,
  DialogPortal,
  DialogTitle,
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
  Button,
  Badge,
  ExpandableAccordion,
  ExpandableItem,
  ExpandableTrigger,
  ExpandableContent,
} from "@kla-v2/ui-components";
import { ArrowRight, XIcon, ExternalLink, Star } from "lucide-react";
import PropTypes from "prop-types";
import { useMemo, useState, useEffect } from "react";
import SpinnerLoader from "@/utils/loaders/spinner-loader";
import { useCheckDuplicateQuery } from "@/services/question";

export default function CheckDuplicateModal({ onClose, content, isOpen }) {
  const { t } = useLanguage();
  const [matchType, setMatchType] = useState("HEADING");
  const [keywords, setKeywords] = useState([]);

  useEffect(() => {
    if (content && content.trim()) {
      setKeywords([content]);
    }
  }, [content]);

  const { data: noticeList, isLoading } = useCheckDuplicateQuery({
    searchKeywords: keywords,
    matchType,
    page: 0,
    size: 10,
  });

  const handleClearAll = () => {
    setKeywords([]);
  };

  const handleRemoveKeyword = (keywordToRemove) => {
    setKeywords(keywords.filter((keyword) => keyword !== keywordToRemove));
  };

  const highlightText = (text, searchTerms) => {
    if (!searchTerms?.length || !text) return text;

    try {
      let highlightedText = text;

      searchTerms.forEach((term) => {
        if (!term) return;
        const regex = new RegExp(`(${term})`, "gi");
        highlightedText = highlightedText
          .split(regex)
          .map((part, index) =>
            regex.test(part) ? (
              <span key={`highlight-${index}`} className="text-orange-600">
                {part}
              </span>
            ) : (
              part
            )
          )
          .flat();
      });

      return highlightedText;
    } catch (error) {
      console.error("Highlight error:", error);
      return text;
    }
  };

  const highlightedNotices = useMemo(() => {
    if (!noticeList?.content || !keywords.length) return [];

    return noticeList.content.map((notice) => ({
      ...notice,
      highlightedContent: highlightText(notice.matchContent, keywords),
    }));
  }, [noticeList, keywords]);

  const handleTabChange = (value) => {
    setMatchType(value);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogPortal>
        <DialogContent className="max-h-[90vh] max-w-[80vw] w-12rem">
          <DialogHeader className="overflow-hidden">
            <DialogTitle>
              <div className="typography-page-heading">
                {t("checkDuplicate")}
              </div>
            </DialogTitle>
            <Tabs defaultValue="HEADING" onValueChange={handleTabChange}>
              <TabsList variant="segmented" className="block w-full">
                <TabsTrigger
                  value="HEADING"
                  variant="segmented"
                  className="w-1/4"
                >
                  {t("headingMatches")}
                </TabsTrigger>
                <TabsTrigger
                  value="CLAUSE"
                  variant="segmented"
                  className="w-1/4"
                >
                  {t("clauseMatches")}
                </TabsTrigger>
                <hr className="w-full" />
              </TabsList>

              {/* Common Search Section */}
              <div className="flex p-3 flex-col items-start gap-3 flex-shrink-0 self-stretch bg-background rounded-md">
                <div className="flex w-full items-center">
                  <p className="text-grey-600">{t("keyWordToSearch")}</p>
                </div>
                <div className="flex w-full items-center gap-2 flex-wrap">
                  {keywords.length > 0 ? (
                    keywords.map((keyword, index) => (
                      <Badge
                        key={`keyword-${index}`}
                        onClose={() => handleRemoveKeyword(keyword)}
                      >
                        {keyword}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-gray-400">{t("noKeyWords")}</span>
                  )}
                </div>
                <div className="flex w-full justify-end items-center gap-4">
                  <div
                    className="cursor-pointer flex items-center gap-2 text-secondary"
                    onClick={handleClearAll}
                  >
                    <span className="typography-body-text-m-16">
                      {t("clearAll")}
                    </span>
                    <XIcon className="size-4" />
                  </div>
                  <Button
                    size="md"
                    variant="primary"
                    iconPosition="right"
                    icon={ArrowRight}
                    type="button"
                  >
                    {t("search")}
                  </Button>
                </div>
              </div>

              {/* Heading Matches Tab */}
              <TabsContent value="HEADING">
                <div className="overflow-scroll flex p-3 flex-col items-start gap-3 flex-shrink-0 self-stretch bg-background rounded-md">
                  <div className="flex w-full items-center">
                    <p className="text-grey-600">{t("matches")}</p>
                  </div>
                  {isLoading ? (
                    <SpinnerLoader />
                  ) : highlightedNotices.length ? (
                    <div className=" w-full mt-2">
                      <ExpandableAccordion
                        type="multiple"
                        className="w-full flex flex-col gap-4"
                      >
                        {highlightedNotices
                          .filter((notice) => notice.matchType === "HEADING")
                          .map((notice, index) => (
                            <ExpandableItem
                              key={notice.noticeId || index}
                              className="w-full"
                              value={`item-${index}`}
                            >
                              <ExpandableTrigger className="w-full">
                                <div className="flex items-center justify-between w-full px-4 py-2 rounded-lg transition group cursor-pointer">
                                  <div className="flex w-full items-center gap-3 overflow-hidden">
                                    <span className="font-semibold text-gray-800">
                                      {notice.questionNumber}
                                    </span>
                                    {notice.starred && (
                                      <Star
                                        className="text-yellow-500 size-4"
                                        fill="currentColor"
                                      />
                                    )}
                                    <span className="text-gray-700 truncate">
                                      {notice.highlightedContent ||
                                        notice.matchContent}
                                    </span>
                                  </div>
                                  <ExternalLink className="w-4 h-4" />
                                </div>
                              </ExpandableTrigger>
                              <ExpandableContent>
                                <div className="p-4 border-t-0 rounded-b-lg">
                                  <p>
                                    {notice.highlightedContent ||
                                      notice.matchContent}
                                  </p>
                                </div>
                              </ExpandableContent>
                            </ExpandableItem>
                          ))}
                      </ExpandableAccordion>
                    </div>
                  ) : (
                    <div className="w-full text-center">
                      {t("noMatchesFound")}
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Clause Matches Tab */}
              <TabsContent value="CLAUSE">
                <div className="flex p-3 flex-col items-start gap-3 flex-shrink-0 self-stretch bg-background rounded-md">
                  <div className="flex w-full items-center">
                    <p className="text-grey-600">{t("matches")}</p>
                  </div>
                  {isLoading ? (
                    <SpinnerLoader />
                  ) : highlightedNotices.length ? (
                    <div className="w-full mt-2">
                      <ExpandableAccordion
                        type="multiple"
                        className="w-full flex flex-col gap-4"
                      >
                        {highlightedNotices
                          .filter((notice) => notice.matchType === "CLAUSE")
                          .map((notice, index) => (
                            <ExpandableItem
                              key={notice.noticeId || index}
                              className="w-full"
                              value={`item-${index}`}
                            >
                              <ExpandableTrigger className="w-full">
                                <div className="flex items-center justify-between w-full px-4 py-2 rounded-lg transition group cursor-pointer">
                                  <div className="flex w-full items-center gap-3 overflow-hidden">
                                    <span className="font-semibold text-gray-800">
                                      {notice.questionNumber}
                                    </span>
                                    {notice.starred && (
                                      <Star
                                        className="text-yellow-500 size-4"
                                        fill="currentColor"
                                      />
                                    )}
                                    <span className="text-gray-700 truncate">
                                      {notice.highlightedContent ||
                                        notice.matchContent}
                                    </span>
                                  </div>
                                  <ExternalLink className="w-4 h-4" />
                                </div>
                              </ExpandableTrigger>
                              <ExpandableContent>
                                <div className="p-4 border-t-0 rounded-b-lg">
                                  <p>
                                    {notice.highlightedContent ||
                                      notice.matchContent}
                                  </p>
                                </div>
                              </ExpandableContent>
                            </ExpandableItem>
                          ))}
                      </ExpandableAccordion>
                    </div>
                  ) : (
                    <div className="w-full text-center">
                      {t("noMatchesFound")}
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </DialogHeader>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
}

CheckDuplicateModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  content: PropTypes.string,
};
