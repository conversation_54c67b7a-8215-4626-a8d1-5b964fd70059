import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  toast,
  RadioGroup,
  RadioGroupItem,
  Checkbox,
  Input,
  DialogPortal,
} from "@kla-v2/ui-components";
import { useState } from "react";
import PropTypes from "prop-types";
import { useLanguage } from "@/hooks";

const RULES = [
  [
    "a",
    "It shall not bring in any name or statement not strictly necessary to make the question ineligible;",
  ],
  [
    "b",
    "If it contains a statement the member shall make himself responsible for accuracy of the statement;",
  ],
  [
    "m",
    "It shall not repeat in substance questions already answered or to which an answer has been refused in current session.",
  ],
  [
    "f",
    "It shall not contain arguments, inference, ironical expressions, imputations, epithets or defamatory statements",
  ],
];

const DISALLOW_RULES = [
  [
    "b",
    "If it contains a statement the member shall make himself responsible for accuracy of the statement;",
  ],
  [
    "f",
    "It shall not contain arguments, inference, ironical expressions, imputations, epithets or defamatory statements",
  ],
  [
    "m",
    "It shall not repeat in substance questions already answered or to which an answer has been refused in current session.",
  ],
  [
    "a",
    "It shall not bring in any name or statement not strictly necessary to make the question ineligible;",
  ],
];

const ClausePopUpModal = ({ isOpen, onClose, isPreview = false }) => {
  const { t } = useLanguage();
  const [mode, setMode] = useState("list");
  const [selectedRules, setSelectedRules] = useState({
    list: {},
    manual: {},
  });

  const mapRules = (data) =>
    data.map(([letter, description]) => ({
      id: `rule-${letter}`,
      label: letter.startsWith("m") ? "Rule 36(2)(m)" : `Rule 36(2)(${letter})`,
      description,
      key: letter,
    }));

  const rules = mode === "manual" ? mapRules(DISALLOW_RULES) : mapRules(RULES);

  const handleCheckboxChange = (id) => {
    setSelectedRules((prev) => {
      const updatedRules = { ...prev[mode] };
      updatedRules[id] = {
        ...updatedRules[id],
        selected: !updatedRules[id]?.selected,
        questionNumber: updatedRules[id]?.questionNumber || "",
      };
      return { ...prev, [mode]: updatedRules };
    });
  };

  const handleInputChange = (id, value) => {
    setSelectedRules((prev) => {
      const updatedRules = { ...prev[mode] };
      updatedRules[id] = {
        ...updatedRules[id],
        questionNumber: value,
      };
      return { ...prev, [mode]: updatedRules };
    });
  };

  const handleSubmit = () => {
    try {
      onClose();
      toast.success("Success", { description: t("Submitted") });
    } catch (err) {
      toast.error("Error submitting document", {
        description: err?.message,
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose} className="overflow-hidden">
      <DialogPortal>
        <DialogContent className="max-w-[1140px] h-[500px] flex flex-col">
          <DialogHeader>
            <DialogTitle>Do you want to Disallow Clause Partially?</DialogTitle>
            <div className="pt-3">
              <RadioGroup
                value={mode}
                onValueChange={setMode}
                className="flex items-center space-x-6 py-3"
              >
                {["list", "manual"].map((val) => (
                  <div key={val} className="flex items-center space-x-2">
                    <RadioGroupItem value={val} id={val} />
                    <label htmlFor={val}>
                      {val === "list"
                        ? "Partial Disallow In Clause"
                        : "Disallow Completely"}
                    </label>
                  </div>
                ))}
              </RadioGroup>
            </div>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto mr-4">
            <div className="mb-2">
              A question, in order to be admissible, shall be governed by the
              following conditions:-
            </div>
            {rules.map(({ id, label, description }) => {
              const { selected = false, questionNumber = "" } =
                selectedRules[mode][id] || {};
              const showInput = selected && id === "rule-m";
              return (
                <div key={id} className="px-3 py-2">
                  <div className="flex items-start">
                    <Checkbox
                      className="mr-3 mt-1"
                      checked={selected}
                      onCheckedChange={() => handleCheckboxChange(id)}
                    />
                    <div className="flex-1">
                      <div className="text-sm font-medium">{label}</div>
                      <div className="text-sm text-gray-500">{description}</div>
                    </div>
                  </div>

                  {showInput && (
                    <div className="pl-8 pt-2 max-w-[400px]">
                      <label className="typography-body-text-m-14 text-gray-600">
                        Question Number
                      </label>
                      <Input
                        placeholder="Enter Question Number"
                        size="md"
                        value={questionNumber}
                        onChange={(e) => handleInputChange(id, e.target.value)}
                      />
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          <DialogFooter className="flex">
            {isPreview ? (
              <DialogClose asChild>
                <Button size="sm" variant="primary">
                  {t("close")}
                </Button>
              </DialogClose>
            ) : (
              <>
                <DialogClose asChild>
                  <Button size="sm" variant="secondary">
                    {t("Cancel")}
                  </Button>
                </DialogClose>
                <DialogClose asChild>
                  <Button size="sm" variant="primary" onClick={handleSubmit}>
                    {t("Confirm")}
                  </Button>
                </DialogClose>
              </>
            )}
          </DialogFooter>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

ClausePopUpModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  isPreview: PropTypes.bool,
  onClose: PropTypes.func.isRequired,
};

export default ClausePopUpModal;
