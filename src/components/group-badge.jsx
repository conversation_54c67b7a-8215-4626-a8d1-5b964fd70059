import PropTypes from "prop-types";

export const GroupBadge = ({ groupNumber }) => {
  const groupColors = {
    1: "bg-secondary-tint-90",
    2: "bg-success-100",
    3: "bg-warning-50",
    4: "bg-error-100",
    5: "bg-info-100",
    6: "bg-warning-100",
  };

  const bgClass = groupColors[groupNumber] || "bg-primary-tint-90";

  return (
    <div
      className={`flex items-center ${bgClass} pl-3 pr-1 py-1 justify-between rounded-full w-[89px] h-7`}
    >
      <span className="typography-body-text-m-14">Group</span>
      <div className="flex items-center justify-center w-6 h-6 font-bold text-black bg-white border rounded-full shadow-lg typography-body-text-m-12 border-secondary">
        {groupNumber}
      </div>
    </div>
  );
};

GroupBadge.propTypes = {
  groupNumber: PropTypes.string.isRequired,
};
