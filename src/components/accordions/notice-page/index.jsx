import PropTypes from "prop-types";
import {
  <PERSON>ton,
  Expandable<PERSON><PERSON>nt,
  ExpandableTrigger,
  Textarea,
  toast,
} from "@kla-v2/ui-components";
import { AccordionTitle } from "@/components/accordion-title";
import { useFieldArray, useForm } from "react-hook-form";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import CornerIcon from "@/icons/corner-icon";
import CompareIcon from "@/icons/compare-icon";
import AddClauseIcon from "@/icons/add-clause-icon";
import { Save } from "lucide-react";
import { forwardRef } from "react";
import { noticeDetailsFormFields } from "./form-fields";
import { useLanguage } from "@/hooks";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  getNoticeDetailsSchema,
  getNoticeDetailsValidationSchema,
} from "./form-schema";
import { useImperativeHandle } from "react";
import { usePostNoticeDetailsMutation } from "@/services/short-notice";
import { useParams } from "react-router-dom";
import ClauseCards from "./clause-cards";
import { resetFormWithResponseData, shouldShowTick } from "@/utils";
import { useState } from "react";
import CheckDuplicateModal from "@/components/pop-up/check-duplicate";
import { useSaveNoticeDetailsMutation } from "@/services/notice-for-question-to-private-members";

const NoticeDetailsPage = forwardRef(
  (
    {
      accordionOrderNo,
      noticeDetailsData,
      openNext,
      variant = "default",
      refetch,
    },
    ref
  ) => {
    const { t } = useLanguage();
    const { documentId } = useParams();
    const formFields = noticeDetailsFormFields(t);
    const [postDefaultNoticeDetails] = usePostNoticeDetailsMutation();
    const [postPrivateNoticeDetails] = useSaveNoticeDetailsMutation();

    const postNoticeDetails =
      variant === "privateMember"
        ? postPrivateNoticeDetails
        : postDefaultNoticeDetails;
    const form = useForm({
      resolver: zodResolver(getNoticeDetailsValidationSchema(t)),
      shouldUnregister: false,
      defaultValues: {
        [formFields.NOTICE_HEADING.name]:
          noticeDetailsData?.[formFields.NOTICE_HEADING.name] || "",
        [formFields.CLAUSES.name]:
          !noticeDetailsData?.[formFields.CLAUSES.name] ||
          noticeDetailsData?.[formFields.CLAUSES.name].length === 0
            ? [{ content: "" }]
            : noticeDetailsData?.[formFields.CLAUSES.name],
      },
      reValidateMode: "onChange",
    });
    const { control, trigger, getValues } = form;
    const { fields, append, remove, move } = useFieldArray({
      control,
      name: "clauses",
    });
    const maxCount = 500;

    useImperativeHandle(ref, () => ({
      async validateAndSubmit() {
        const isValid = await form.trigger();
        return isValid;
      },
      getValues: () => form.getValues(),
    }));
    const showTick = shouldShowTick(form);

    const handleAddClause = () => {
      append({
        content: "",
        order: fields.length + 1,
      });
    };

    const handlePartialSave = async (e) => {
      e.preventDefault();
      const data = getValues();
      const filteredClauses = data[formFields.CLAUSES.name].filter(
        (clause) => clause?.content?.length != 0
      );
      const noticeDetails = {
        id: documentId,
        noticeHeading: data[formFields.NOTICE_HEADING.name],
        clauses: filteredClauses.map((clause, index) => ({
          content: clause.content,
          order: index + 1,
        })),
      };

      // For PrivateMember variant, ensure we include any text values for IDs
      try {
        const response = await postNoticeDetails({
          documentId,
          data: noticeDetails,
        }).unwrap();
        if (response[formFields.CLAUSES.name].length == 0) {
          const data = {
            ...response,
            [formFields.CLAUSES.name]: [{ content: "" }],
          };
          resetFormWithResponseData(form, getNoticeDetailsSchema(t), data);
        } else {
          resetFormWithResponseData(form, getNoticeDetailsSchema(t), response);
        }
        toast.success(t("toast.noticeDetailsSave"));

        // Refetch data after saving to get updated values
        if (refetch) {
          await refetch();
        }

        openNext();
      } catch (error) {
        toast.error(error?.message);
      }
    };
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalContent, setModalContent] = useState("");

    const handleOpenModal = () => {
      const heading = getValues(formFields.NOTICE_HEADING.name);
      setModalContent(heading);
      setIsModalOpen(true);
    };
    const handleCloseModal = () => {
      setIsModalOpen(false);
    };

    return (
      <>
        <ExpandableTrigger showTick={showTick} variant="secondary">
          <AccordionTitle
            accordionOrderNo={accordionOrderNo}
            accordionTitle={t("form:noticeDetails")}
          />
        </ExpandableTrigger>
        <ExpandableContent>
          <div className="w-full flex flex-col">
            <div className="flex flex-col gap-4 p-6">
              <Form className="w-full" {...form}>
                <form
                  onSubmit={(e) => handlePartialSave(e)}
                  className="w-full space-y-2"
                >
                  <FormField
                    control={control}
                    name={formFields.NOTICE_HEADING.name}
                    render={({ field, fieldState }) => {
                      const { error } = fieldState;
                      const charCount = field.value?.length || 0;
                      return (
                        <FormItem className="flex flex-col">
                          <FormControl>
                            <div className="relative mt-1">
                              <Textarea
                                className="border border-gray-300 rounded-lg p-3 text-black font-bold text-sm focus:outline-none focus:border-blue-500 resize-none w-full"
                                rows="4"
                                maxLength={500}
                                value={field.value}
                                {...field}
                                {...formFields.NOTICE_HEADING}
                                onChange={(value) => {
                                  field.onChange(value);
                                  trigger(formFields.NOTICE_HEADING.name);
                                }}
                              />

                              <div className="absolute bottom-3 right-3 flex items-center gap-2">
                                <CornerIcon />
                                <div className="text-gray-500 text-sm">
                                  <span className="typography-body-text-r-12 text-foreground">
                                    {charCount}
                                  </span>
                                  <span className="typography-body-text-r-12 text-gray-500">
                                    /{maxCount}
                                  </span>
                                </div>
                              </div>
                              <div
                                className="absolute top-8 right-3 flex items-center cursor-pointer"
                                onClick={handleOpenModal}
                              >
                                <CompareIcon />
                              </div>

                              <CheckDuplicateModal
                                isOpen={isModalOpen}
                                onClose={handleCloseModal}
                                content={modalContent}
                              />
                            </div>
                          </FormControl>
                          {error && (
                            <p className="typography-body-text-r-14 text-error">
                              {error?.message}
                            </p>
                          )}
                        </FormItem>
                      );
                    }}
                  />

                  <div className="flex items-center justify-between">
                    <label className="text-base font-semibold text-black-600 leading-6 tracking-[-0.02em] font-inter">
                      {t("form:clauses")}
                    </label>
                    <button
                      type="button"
                      className="flex items-center justify-center w-10 h-10 p-2 rounded-md border border-gray-300 bg-white"
                      onClick={handleAddClause}
                    >
                      <AddClauseIcon />
                    </button>
                  </div>

                  <div className="flex flex-col ">
                    <FormField
                      control={control}
                      name={formFields.CLAUSES.name}
                      render={({ field, fieldState }) => {
                        const { error } = fieldState;
                        return (
                          <FormItem>
                            <FormControl>
                              <ClauseCards
                                {...field}
                                fields={fields}
                                remove={remove}
                                move={move}
                                control={control}
                                trigger={trigger}
                                formFields={formFields}
                                error={error}
                              />
                            </FormControl>
                          </FormItem>
                        );
                      }}
                    />
                  </div>
                  <div className="flex justify-end mt-2">
                    <Button
                      type="submit"
                      variant="secondary"
                      className="flex items-center"
                    >
                      {t("save")} <Save className="w-4 h-4" />
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          </div>
        </ExpandableContent>
      </>
    );
  }
);
NoticeDetailsPage.displayName = "NoticeDetailsPage";

NoticeDetailsPage.propTypes = {
  accordionOrderNo: PropTypes.number.isRequired,
  setAccordionValue: PropTypes.func,
  noticeDetailsData: PropTypes.object,
  openNext: PropTypes.func,
  variant: PropTypes.string,
  refetch: PropTypes.func,
};

export { NoticeDetailsPage };
