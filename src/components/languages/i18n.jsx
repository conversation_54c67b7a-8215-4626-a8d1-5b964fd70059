import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import commonEn from "../../utils/locales/en/common.json";
import commonMl from "../../utils/locales/ml/common.json";
import formEn from "../../utils/locales/en/form-fields.json";
import formMl from "../../utils/locales/ml/form-fields.json";
import tableEn from "../../utils/locales/en/table-labels.json";
import tableMl from "../../utils/locales/ml/table-labels.json";
import placeholderEn from "@/utils/locales/en/placeholder.json";
import placeholderMl from "@/utils/locales/ml/placeholder.json";
import validationEn from "@/utils/locales/en/validation.json";

const getLanguageFromLocalStorage = () => {
  return localStorage.getItem("selectedLanguage") || "ml";
};

i18n.use(initReactI18next).init({
  resources: {
    en: {
      common: commonEn,
      form: formEn,
      table: tableEn,
      placeholder: placeholderEn,
      validation: validationEn,
    },
    ml: {
      form: formMl,
      placeholder: placeholderMl,
      common: commonMl,
      table: tableMl,
    },
  },
  lng: getLanguageFromLocalStorage(),
  fallbackLng: "en",
  interpolation: {
    escapeValue: false,
  },
  ns: ["common", "form", "table", "validation", "placeholder"],
  defaultNS: "common",
});

export default i18n;
