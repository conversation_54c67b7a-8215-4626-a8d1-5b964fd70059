import PropTypes from "prop-types";
import { createContext, useState } from "react";
import i18n from "./i18n";

const LanguageContext = createContext({
  language: "en",
  changeLanguage: () => {},
  t: (key) => key, // Add options parameter for flexibility
});

const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState(() => {
    const selectedLanguage = localStorage.getItem("selectedLanguage") || "en";
    i18n.changeLanguage(selectedLanguage);
    return selectedLanguage;
  });

  const changeLanguage = (lng) => {
    localStorage.setItem("selectedLanguage", lng);
    i18n.changeLanguage(lng);
    setLanguage(lng);
  };

  const { t } = i18n;

  return (
    <LanguageContext.Provider value={{ language, changeLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export { LanguageContext, LanguageProvider };

LanguageProvider.propTypes = {
  children: PropTypes.node,
};
