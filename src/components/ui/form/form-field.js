const getFormFields = (t) => ({
    GAZETTE_DATE: {
      name: "gazetteDate",
      label: t("form:gazetteDate"),
      placeholder: t("placeholder:gazetteDate"),
    },
    GAZETTE_NUMBER: {
      name: "gazetteNumber",
      label: t("form:gazetteNumber"),
      placeholder: t("placeholder:gazetteNumber"),
    },
    CLASSIFICATION: {
      name: "classification",
      label: t("form:sro.classification"),
      placeholder: t("placeholder:sro.classification"),
    },
    SUBJECT_IN_LOCAL: {
      name: "subjectInLocal",
      label: t("form:sro.subjectInLocal"),
      placeholder: t("placeholder:sro.subjectInLocal"),
    },
    SUBJECT: {
      name: "subject",
      label: t("form:sro.subject"),
      placeholder: t("placeholder:sro.subject"),
    },
  });
  
  export { getFormFields };
  