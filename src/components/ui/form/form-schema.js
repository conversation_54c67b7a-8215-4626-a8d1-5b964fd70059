import { z } from "zod";

export const BasicDetailsSchema = (t) =>
  z
    .object({
      questionDate: z.string().refine((val) => !isNaN(Date.parse(val)), {
        message: t("validation:invalidDateValue"),
        path: ["questionDate"],
      }),
      designation: z.string(),
      ministerPortfolio: z.string(),
      ministerSubSubject: z.string().optional(),
      members: z.array(
        z.number(),
      ),
    })
    .refine(
      (data) => {
        const selectedMembers = data.members || [];
        const primaryCount = selectedMembers.length > 0 ? 1 : 0;
        const secondaryCount = Math.max(0, selectedMembers.length - 1);
        const totalCount = primaryCount + secondaryCount;

        if (totalCount > 4) {
          return false;
        }
        return true;
      },
      {
        message: t("validation:maximumMembersExceeded"),
        path: ["members"],
      }
    );

export const NoticeFormSchema = (t) =>
  z.object({
    noticeHeading: z.string().min(1, t("validation:noticeHeadingRequired")),
    noticePriority: z.enum(["P1", "P2", "P3", "NIL"], {
      errorMap: () => ({ message: t("validation:invalidNoticePriority") }),
    }),
    starred: z.boolean(),
    clauses: z
      .array(
        z.object({
          id: z.string().min(1, t("validation:clauseIdRequired")),
          text: z.string().min(1, t("validation:clauseTextRequired")),
        })
      )
      .min(1, t("validation:atLeastOneClauseRequired")),
  });
