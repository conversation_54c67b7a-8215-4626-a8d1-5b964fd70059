import { useLanguage } from "@/hooks";
import SaveIcon from "@/icons/save-icon";
import { Button } from "@kla-v2/ui-components";
import { LoaderCircleIcon } from "lucide-react";
import PropTypes from "prop-types";

const ButtonIcon = ({ isSubmitting }) => {
  if (isSubmitting) return <LoaderCircleIcon className="animate-spin size-4" />;
  return <SaveIcon />;
};
ButtonIcon.propTypes = {
  isSubmitting: PropTypes.bool,
};

function SaveButton({ isSubmitting, ...props }) {
  const { t } = useLanguage();
  const renderIcon = () => <ButtonIcon isSubmitting={isSubmitting} />;

  return (
    <Button
      variant="secondary"
      size="md"
      icon={renderIcon}
      iconPosition="right"
      type="submit"
      // TODO: change type to button
      {...props}
    >
      {t("save")}
    </Button>
  );
}

SaveButton.propTypes = {
  isSubmitting: PropTypes.bool,
};

export { SaveButton };
