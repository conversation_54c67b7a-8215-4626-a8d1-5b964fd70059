import {
  Checkbox,
  Combobox,
  DatePicker,
  DropdownSelect,
  FileUploader,
  Input,
  Label,
  RadioGroup,
  RadioGroupItem,
  RichTextEditor,
} from "@kla-v2/ui-components";
import { CalendarIcon } from "lucide-react";
import PropTypes from "prop-types";
import { forwardRef } from "react";
import { FormControl, FormField, FormItem } from ".";
import { disableFutureDates } from "@/utils";

const FormInput = ({ fieldType, control, name, ...props }) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field, fieldState }) => (
        <>
          <FormItem>
            {fieldType === "radio" && <Label>{props.label}</Label>}
            <FormControl>
              <FormElement fieldType={fieldType} {...field} {...props} />
            </FormControl>
          </FormItem>
          {["file", "checkbox"].includes(fieldType) && fieldState.error && (
            <p className="text-error text-sm mt-2">
              {fieldState.error.message}
            </p>
          )}
        </>
      )}
    />
  );
};

const fieldTypes = [
  "input",
  "dropdownSelect",
  "date",
  "year",
  "richText",
  "file",
  "radio",
  "checkbox",
];

FormInput.propTypes = {
  fieldType: PropTypes.oneOf(fieldTypes).isRequired,
  name: PropTypes.string.isRequired,
  label: PropTypes.string,
  control: PropTypes.object,
  error: PropTypes.string,
  value: PropTypes.any,
  onChange: PropTypes.func,
};

const FormElement = forwardRef(({ fieldType, ...props }, ref) => {
  switch (fieldType) {
    case "input": {
      const { value = "" } = props;
      return <Input ref={ref} {...props} value={value ?? ""} />;
    }
    case "dropdownSelect": {
      const { options = [], value = "" } = props;

      return (
        <DropdownSelect
          ref={ref}
          className="w-full"
          {...props}
          value={`${value ?? ""}`}
          options={[{ selectItems: [...options] }]}
        />
      );
    }
    case "date": {
      const { value = "" } = props;
      return (
        <DatePicker
          ref={ref}
          required
          {...props}
          value={value}
          disabledDays={disableFutureDates}
        />
      );
    }
    case "checkbox": {
      const { value = "", onChange } = props;
      return <Checkbox {...props} checked={value} onCheckedChange={onChange} />;
    }
    case "radio": {
      const { value, onChange = () => {}, options = [], ...field } = props;

      return (
        <RadioGroup
          defaultValue={`${value}`}
          onValueChange={(val) => onChange(val)}
          {...field}
        >
          {options.map((item) => (
            <FormItem key={item.value}>
              <FormControl>
                <RadioGroupItem
                  label={item.label}
                  value={item.value}
                  id={`${field.name}.${item.label}.${item.value}`}
                />
              </FormControl>
            </FormItem>
          ))}
        </RadioGroup>
      );
    }
    case "year": {
      const { value = "", onChange = () => {}, options = [] } = props;
      return (
        <Combobox
          className="w-full"
          icon={CalendarIcon}
          onValueChange={(val) => onChange(parseInt(val, 10))}
          options={options}
          isMulti={false}
          {...props}
          value={`${value}`}
        />
      );
    }
    case "file": {
      const { value = [], label = "", onChange = () => {} } = props;
      return (
        <FileUploader
          value={value}
          labelText={label}
          onUpload={(files) => onChange(files)}
          {...props}
          onChange={() => {}}
        />
      );
    }

    case "richText": {
      const { value, onChange = () => {}, error = "" } = props;
      return (
        <RichTextEditor
          defaultValue={value ? JSON.parse(value) : ""}
          onChange={(val) => {
            onChange(JSON.stringify(val));
          }}
          errorMessage={error}
        />
      );
    }
    default:
      console.warn("invalid field type");
      return <></>;
  }
});

FormElement.displayName = "FormElement";
FormElement.propTypes = {
  fieldType: PropTypes.oneOf(fieldTypes).isRequired,
  label: PropTypes.string,
  value: PropTypes.any,
  onChange: PropTypes.func,
  error: PropTypes.string,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string,
      value: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number,
        PropTypes.bool,
      ]),
    })
  ),
};

export { FormInput };
