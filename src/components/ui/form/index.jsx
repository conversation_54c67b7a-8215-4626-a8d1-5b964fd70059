import { cn } from "@/utils";
import { Label } from "@kla-v2/ui-components";
import { Slot } from "@radix-ui/react-slot";
import PropTypes from "prop-types";
import { createContext, forwardRef, useContext, useId } from "react";
import { Controller, FormProvider, useFormContext } from "react-hook-form";

/**
 * Form component that wraps react-hook-form's FormProvider
 */
const Form = FormProvider;

/**
 * Context for form field data
 */
const FormFieldContext = createContext({});

/**
 * FormField component that provides field context and renders a Controller
 *
 * @param {Object} props - Component props
 * @param {string} props.name - Field name
 * @param {Object} props.control - Form control from useForm
 * @param {Function} props.render - Render function for the field
 * @returns {JSX.Element} FormField component
 */
const FormField = (props) => {
  return (
    <FormFieldContext.Provider value={{ name: props.name }}>
      <Controller {...props} />
    </FormFieldContext.Provider>
  );
};

FormField.propTypes = {
  name: PropTypes.string.isRequired,
  control: PropTypes.object.isRequired,
  render: PropTypes.func.isRequired,
};

/**
 * Hook to access form field data and state
 *
 * @returns {Object} Field data including id, name, and field state
 */
const useFormField = () => {
  const fieldContext = useContext(FormFieldContext);
  const itemContext = useContext(FormItemContext);

  const { getFieldState, formState } = useFormContext();

  if (!fieldContext) {
    throw new Error("useFormField should be used within <FormField>");
  }

  const fieldState = getFieldState(fieldContext.name, formState);
  const { id } = itemContext;

  return {
    id,
    name: fieldContext.name,
    formItemId: `${id}-form-item`,
    formMessageId: `${id}-form-item-message`,
    ...fieldState,
  };
};

/**
 * Context for form item data
 */
const FormItemContext = createContext({});

/**
 * FormItem component that wraps form elements
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child elements
 * @param {string} props.className - CSS class names
 * @returns {JSX.Element} FormItem component
 */
const FormItem = forwardRef(({ className, children, ...props }, ref) => {
  const id = useId();

  return (
    <FormItemContext.Provider value={{ id }}>
      <div ref={ref} className={className} {...props}>
        {children}
      </div>
    </FormItemContext.Provider>
  );
});

FormItem.displayName = "FormItem";
FormItem.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
};

/**
 * FormControl component that provides accessibility attributes
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child elements
 * @returns {JSX.Element} FormControl component
 */
const FormControl = forwardRef(({ children, ...props }, ref) => {
  const { error, formItemId } = useFormField();

  return (
    <Slot
      ref={ref}
      id={formItemId}
      aria-invalid={!!error}
      error={error?.message}
      {...props}
    >
      {children}
    </Slot>
  );
});

FormControl.displayName = "FormControl";
FormControl.propTypes = {
  children: PropTypes.node,
};

/**
 * FormLabel component for form field labels
 *
 * @param {Object} props - Component props
 * @param {string} props.className - CSS class names
 * @param {React.ReactNode} props.children - Child elements
 * @returns {JSX.Element} FormLabel component
 */
const FormLabel = forwardRef(({ className, children, ...props }, ref) => {
  const { error, formItemId } = useFormField();

  return (
    <Label
      ref={ref}
      className={cn(error && "text-destructive", className)}
      htmlFor={formItemId}
      {...props}
    >
      {children}
    </Label>
  );
});

FormLabel.displayName = "FormLabel";
FormLabel.propTypes = {
  className: PropTypes.string,
  children: PropTypes.node,
};

export { Form, FormControl, FormField, FormItem, FormLabel };
