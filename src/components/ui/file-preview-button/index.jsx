import PropTypes from "prop-types";
import { useState } from "react";
import { FileText } from "lucide-react";
import { cn } from "@/lib/utils";
import PdfViewer from "@/components/richTextToPDF";

export default function FilePreviewComponent({
  documentName,
  pdfUrl,
  previewImageUrl,
  className,
}) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleViewPdf = () => {
    setIsModalOpen(true);
  };

  const handleClosePdf = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <div
        className={cn(
          "flex flex-col justify-center items-center gap-[10px]",
          className
        )}
      >
        <div
          className="relative w-[140px] h-[114px] overflow-hidden border border-gray-200 rounded-lg shadow-sm cursor-pointer"
          onClick={handleViewPdf}
        >
          {previewImageUrl ? (
            <img
              src={previewImageUrl}
              alt={documentName}
              className="object-cover w-full h-full"
            />
          ) : (
            <div className="flex items-center justify-center w-full h-full bg-gray-50">
              <FileText className="w-6 h-6 text-gray-400" />
            </div>
          )}
        </div>

        <p className="w-full text-xs font-medium text-center text-gray-700 truncate">
          {documentName}
        </p>
      </div>

      {isModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
          <div className="bg-white rounded-lg shadow-lg w-[90vw] h-[90vh] overflow-hidden">
            <PdfViewer
              pdf={{ name: documentName, fileUrl: pdfUrl }}
              onClosePdf={handleClosePdf}
            />
          </div>
        </div>
      )}
    </>
  );
}

FilePreviewComponent.propTypes = {
  documentName: PropTypes.string.isRequired,
  pdfUrl: PropTypes.string.isRequired,
  previewImageUrl: PropTypes.string,
  className: PropTypes.string,
};
