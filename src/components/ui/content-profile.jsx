import PropTypes from "prop-types";
import { cn } from "@/utils";
import { Dot } from "lucide-react";
import avatar from "../../assets/avatar.svg";

const ContentProfile = ({ title, data, className, place, url }) => {
  return (
    <div className={cn(`w-1/2`, className)}>
      <h4 className="text-grey-600 typography-body-text-r-14 mb-2">{title}</h4>
      <div className="flex items-center gap-4">
        <img
          src={url || avatar}
          className="w-9 h-9 rounded-full"
          alt="member-img"
        />
        <h5 className="flex items-end text-black text-sm font-normal mt-1">
          {data}
          <span className="text-gray-500 flex items-end ">
            <Dot size={22} /> {place}
          </span>
        </h5>
      </div>
    </div>
  );
};

ContentProfile.propTypes = {
  title: PropTypes.oneOfType([PropTypes.string, PropTypes.node]).isRequired,
  data: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  place: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  className: PropTypes.string,
  url: PropTypes.string,
};

export default ContentProfile;
