import PropTypes from "prop-types";
import { cn } from "@/utils";

const  ContentData = ({ title, data, className }) => {
  return (
    <div className={cn(`w-1/2`, className)}>
      <h4 className="text-grey-600 typography-body-text-r-14 mb-2">{title}</h4>
      <h5 className="text-black text-sm font-normal mt-1">{data}</h5>
    </div>
  );
};

ContentData.propTypes = {
  title: PropTypes.oneOfType([PropTypes.string, PropTypes.node]).isRequired,
  data: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  className: PropTypes.string,
};

export default ContentData;
