import { renderProseMirrorJSON } from "@/utils/richTestTopdf";
import {
  <PERSON>ton,
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogPortal,
  DialogTitle,
} from "@kla-v2/ui-components";
import { Download, Expand, Minus, Plus } from "lucide-react";
import PropTypes from "prop-types";
import { useEffect, useRef, useState } from "react";

const RichTextToPDF = ({ jsonString }) => {
  const containerRef = useRef(null);
  const [scale, setScale] = useState(1);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const contentRef = useRef(null);

  const handleZoomIn = () => setScale((prev) => Math.min(prev + 0.2, 3));
  const handleZoomOut = () => setScale((prev) => Math.max(prev - 0.2, 0.5));

  const toggleFullScreen = () => setIsFullScreen(!isFullScreen);

  useEffect(() => {
    const handleFullScreenChange = () => {
      setIsFullScreen(!!document.fullscreenElement);
    };
    document.addEventListener("fullscreenchange", handleFullScreenChange);
    return () =>
      document.removeEventListener("fullscreenchange", handleFullScreenChange);
  }, []);

  let htmlContent = "";

  try {
    const json = jsonString && typeof jsonString === 'string' ? JSON.parse(jsonString) : null;
    htmlContent = json ? renderProseMirrorJSON(json) : "";
  } catch (error) {
    console.error("Invalid JSON in data.description:", error);
    htmlContent = "";
  }

  function downloadPDF() {
    // TODO : Have to work with PDF downloader from converting html content
  }

  return (
    <div
      ref={containerRef}
      className="border border-gray-300 w-full h-full bg-white relative rounded-lg min-h-[23rem] overflow-auto"
    >
      <div className="absolute right-4 top-2 z-10">
        <div className="flex flex-col items-center gap-4">
          <button
            className="text-gray-600 hover:text-gray-900 bg-white p-2 rounded-md border border-gray-300 hover:bg-white"
            type="button"
          >
            <Expand onClick={toggleFullScreen} size={16} />
          </button>
          <div className="flex flex-col border border-gray-300 rounded-md">
            <button
              className="text-gray-600 hover:text-gray-900 bg-white p-2 border-none hover:bg-white"
              type="button"
              disabled={scale > 1.5}
            >
              <Plus onClick={handleZoomIn} size={16} />
            </button>
            <hr />
            <button
              className="text-gray-600 hover:text-gray-900 bg-white p-2 border-none hover:bg-white"
              type="button"
              disabled={scale === 1}
            >
              <Minus onClick={handleZoomOut} size={16} />
            </button>
          </div>
        </div>
      </div>

      <div className="flex-grow w-full h-full overflow-auto px-8 py-6">
        <div
          ref={contentRef}
          id="pdf-content"
          style={{
            transform: `scale(${scale})`,
            transformOrigin: "top left",
            transition: "transform 0.2s ease-in-out",
          }}
          dangerouslySetInnerHTML={{ __html: htmlContent }}
        />
      </div>

      <Dialog open={isFullScreen} onOpenChange={toggleFullScreen}>
        <DialogPortal>
          <DialogContent className="w-[1140px] max-w-[80vw]">
            <DialogHeader>
              <DialogTitle>
                <div className="typography-page-heading">Document.pdf</div>
              </DialogTitle>
            </DialogHeader>
            <div className="max-h-[80vh] overflow-auto border border-gray-300 rounded-lg">
              <div className="w-full h-full leading-8 px-8 py-12">
                <div
                  ref={contentRef}
                  id="pdf-content"
                  dangerouslySetInnerHTML={{ __html: htmlContent }}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="neutral"
                className="flex items-center"
                onClick={downloadPDF}
              >
                Download <Download size={16} />
              </Button>
            </DialogFooter>
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </div>
  );
};

RichTextToPDF.propTypes = {
  jsonString: PropTypes.string,
};

RichTextToPDF.defaultProps = {
  jsonString: "{}",
};

export default RichTextToPDF;
