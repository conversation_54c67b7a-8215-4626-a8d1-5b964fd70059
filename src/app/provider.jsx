import { ThemeProvider, Typo<PERSON>Provider } from "@kla-v2/ui-components";
import PropTypes from "prop-types";
import { Provider } from "react-redux";
import { store } from "./store";
import { LanguageProvider } from "@/components/languages/language-context";

export const AppProvider = ({ children }) => {
  return (
    <Provider store={store}>
      <ThemeProvider>
        <TypographyProvider>
          <LanguageProvider data-testid="language-provider">
            {children}
          </LanguageProvider>
        </TypographyProvider>
      </ThemeProvider>
    </Provider>
  );
};

AppProvider.propTypes = {
  children: PropTypes.element,
};
