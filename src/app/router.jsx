import { BaseLayout } from "@/layouts/base-layout";
import MinisterDesignationGroupPage from "@/pages/minister-designation-group";
import DocumentList from "@/pages/listing";
import { QuestionCreatePage } from "@/pages/listing/questions-page/question-notice";
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  RouterProvider,
  Outlet,
  Navigate,
} from "react-router-dom";
import BallotList from "../pages/listing/ballot";
import { AllotmentOfDaysPage } from "@/pages/allotment-of-days/index.js";
import ScheduleOfActivityPage from "@/pages/schedule-of-activity/index.js";
import PerformBalloting from "@/pages/listing/components/perform-balloting";
import ConsentList from "@/pages/consent";
import ConsentPpoList from "@/pages/consent/parliamentary-party-office-consent";
import NoticeForQuestion from "@/pages/notice-for-question";
import MyNotices from "@/pages/notice-for-question/my-notice";
import NoticeBank from "@/pages/notice-for-question/notice-bank";
import { CreateShortNotice } from "@/pages/short-notice";
import { CreateDiscussion } from "@/pages/half-an-hour-discussion";
import LateAnswerBulletinPage from "@/pages/late-answer-bulletin";
import DelayedAnswerBulletinPage from "@/pages/delayed-answer-bulletin";
import OtherNotices from "@/pages/other-notices-listing";
import MyOtherNotices from "@/pages/other-notices-listing/other-notices";
import OtherNoticeBanks from "@/pages/other-notices-listing/other-notice-bank";
import QuestionToPrivateMember from "@/pages/notice-for-question/question-to-private-member";
import { documentByIdLoader } from "@/utils/loaders";
import DelayStatement from "@/pages/listing/delay-statement";
import SettingOfUnstarredQuestionsPage from "@/pages/setting-of-unstarred-questions";
import StarredQuestionPage from "@/pages/listing/components/starred/index";
import AnswerStatusReport from "@/pages/answer-status-report";
import CorrectionOfAnswerPage from "@/pages/correction-of-answer";
import SectionStaffList from "@/pages/notice-for-question/section-staff-list";
import ActionToBeTaken from "@/pages/notice-for-question/section-staff-list/action-to-be-taken";
import AllTab from "@/pages/notice-for-question/section-staff-list/all-tab";
import StaffNoticeList from "@/pages/notice-for-question/staff-notice-list";
import AllNotices from "@/pages/notice-for-question/staff-notice-list/all-notices";
import ActionToBeTakenTab from "@/pages/notice-for-question/staff-notice-list/action-to-be-taken";
import QuestionEditPage from "@/pages/question-edit";
import ViewMDGDetailsPage from "@/pages/minister-designation-group/view-mdg-details";
import HomePage from "@/pages/home";

// Layout wrappers for each user role
const SectionLayout = () => <Outlet />;
const MemberLayout = () => <Outlet />;
const PpoLayout = () => <Outlet />;

const AppRouter = () => {
  const router = createBrowserRouter(
    [
      {
        path: "/",
        element: <BaseLayout />,
        children: [
          {
            index: true,
            element: <HomePage />,
          },

          // Section Staff Routes
          {
            path: "section",
            element: <SectionLayout />,
            children: [
              {
                index: true,
                element: <Navigate to="/section/documents" replace />,
              },
              {
                path: "documents",
                children: [
                  {
                    index: true,
                    element: <DocumentList />,
                  },
                  {
                    path: "minister-designation-group/:documentId",
                    element: <MinisterDesignationGroupPage />,
                  },
                  {
                    path: "view/minister-designation-group/:documentId",
                    element: <ViewMDGDetailsPage />,
                  },
                  {
                    path: "allotment-of-days/:documentId",
                    element: <AllotmentOfDaysPage />,
                  },
                  {
                    path: "schedule-of-activity/:documentId",
                    element: <ScheduleOfActivityPage />,
                  },
                  {
                    path: "delayed-answer-bulletin/:documentId",
                    element: <DelayedAnswerBulletinPage />,
                  },
                  {
                    path: "answer-status-report/:documentId",
                    element: <AnswerStatusReport />,
                  },
                  {
                    path: "delay-statement/:documentId",
                    element: <DelayStatement />,
                  },
                  {
                    path: "late-answer-bulletin/:documentId",
                    element: <LateAnswerBulletinPage />,
                  },
                  {
                    path: "correction-of-answer/:documentId",
                    element: <CorrectionOfAnswerPage />,
                  },
                ],
              },
              {
                path: "question-edit-list",
                children: [
                  {
                    index: true,
                    element: <SectionStaffList />,
                  },
                  {
                    path: "all",
                    element: <AllTab />,
                  },
                  {
                    path: "action",
                    element: <ActionToBeTaken />,
                  },
                  {
                    path: "edit/:documentId",
                    element: <QuestionEditPage />,
                  },
                ],
              },
              {
                path: "ballot",
                children: [
                  {
                    index: true,
                    element: <Navigate to="/section/ballot/list" replace />,
                  },
                  {
                    path: "list",
                    element: <BallotList />,
                  },
                  {
                    path: "perform",
                    element: <PerformBalloting />,
                  },
                  {
                    path: "perform/:ballotingId/:questionDate/:status",
                    element: <PerformBalloting />,
                  },
                ],
              },
              {
                path: "setting-starred-questions",
                element: <StarredQuestionPage />,
              },
              {
                path: "setting-of-unstarred-questions/:documentId",
                element: <SettingOfUnstarredQuestionsPage />,
              },
              {
                path: "notices-for-question",
                element: <StaffNoticeList />,
                children: [
                  {
                    index: true,
                    element: <ActionToBeTakenTab />,
                  },
                  {
                    path: "all",
                    element: <AllNotices />,
                  },
                ],
              },
            ],
          },

          // MLA (Member) Routes
          {
            path: "member",
            element: <MemberLayout />,
            children: [
              {
                index: true,
                element: <Navigate to="/member/consent" replace />,
              },
              {
                path: "consent",
                element: <ConsentList />,
              },
              {
                path: "my-notices",
                element: <OtherNotices />,
                children: [
                  {
                    index: true,
                    element: <MyOtherNotices />,
                  },
                  {
                    path: "notice-bank",
                    element: <OtherNoticeBanks />,
                  },
                  {
                    path: "short-notice/add/:documentId",
                    loader: documentByIdLoader,
                    element: <CreateShortNotice />,
                  },
                  {
                    path: "half-an-hour-discussion/:documentId",
                    loader: documentByIdLoader,
                    element: <CreateDiscussion />,
                  },
                  {
                    path: "notice-for-question-to-private-member/:documentId",
                    loader: documentByIdLoader,
                    element: <QuestionToPrivateMember />,
                  },
                ],
              },
              {
                path: "my-question-notices",
                element: <NoticeForQuestion />,
                children: [
                  {
                    index: true,
                    element: <MyNotices />,
                  },
                  {
                    path: "notice-bank",
                    element: <NoticeBank />,
                  },
                  {
                    path: "notice-for-question/:documentId",
                    element: <QuestionCreatePage />,
                  },
                ],
              },
            ],
          },

          // PPO Routes
          {
            path: "ppo",
            element: <PpoLayout />,
            children: [
              {
                index: true,
                element: <Navigate to="/ppo/consent" replace />,
              },
              {
                path: "consent",
                element: <ConsentPpoList />,
              },
              {
                path: "my-question-notices",
                element: <NoticeForQuestion />,
                children: [
                  {
                    index: true,
                    element: <MyNotices />,
                  },
                  {
                    path: "notice-bank",
                    element: <NoticeBank />,
                  },
                  {
                    path: "notice-for-question/:documentId",
                    element: <QuestionCreatePage />,
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
    { basename: import.meta.env.BASE_URL }
  );

  return (
    <RouterProvider
      router={router}
      future={{
        v7_startTransition: true,
      }}
    />
  );
};

export { AppRouter };
