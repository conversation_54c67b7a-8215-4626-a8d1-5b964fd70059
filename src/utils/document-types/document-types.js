export const documentTypeList = {
  MINISTERDESIGNATIONGROUP: "MINISTER_DESIGNATION_GROUP",
  ALLOTMENTOFDAYS: "ALLOTMENT_OF_DAYS",
  SCHEDULEOFACTIVITY: "SCHEDULE_OF_ACTIVITY",
  DELAYEDANSWERBULLETIN: "DELAYED_ANSWER_BULLETIN",
  DELAYSTATEMENT: "DELAY_STATEMENT",
  LATEANSWERBULLETIN: "LATE_ANSWER_BULLETIN",
  CORRECTIONOFANSWER: "CORRECTION_OF_ANSWER",
  REPORTGENERATION: "REPORT_GENERATION",
  ANSWERSTATUSREPORT: "ANSWER_STATUS_REPORT",
};

const documentTypes = [
  {
    label: "Minister Designation Group",
    labelInLocal: "Minister Designation Group",
    value: documentTypeList.MINISTERDESIGNATIONGROUP,
    route: "/section/documents/minister-designation-group/",
  },
  {
    label: "Allotment Of Days",
    labelInLocal: "Allotment Of Days",
    value: documentTypeList.ALLOTMENTOFDAYS,
    route: "/section/documents/allotment-of-days/",
  },
  {
    label: "Schedule of Activity",
    labelInLocal: "Schedule of Activity",
    value: documentTypeList.SCHEDULEOFACTIVITY,
    route: "/section/documents/schedule-of-activity/",
  },
  {
    label: "Delayed Answer Bulletin",
    labelInLocal: "Delayed Answer Bulletin",
    value: documentTypeList.DELAYEDANSWERBULLETIN,
    route: "/section/documents/delayed-answer-bulletin/",
  },
  {
    label: "Delay Statement",
    labelInLocal: "Delay Statement",
    value: documentTypeList.DELAYSTATEMENT,
    route: "/section/documents/delay-statement/",
  },
  {
    label: "Late Answer Bulletin",
    value: documentTypeList.LATEANSWERBULLETIN,
    route: "/section/documents/late-answer-bulletin/",
  },
  {
    label: "Correction of Answer",
    value: documentTypeList.CORRECTIONOFANSWER,
    route: "/section/documents/correction-of-answer/",
  },
  {
    label: "Report Generation",
    value: documentTypeList.REPORTGENERATION,
    route: "/section/documents/report-generation/",
  },
  {
    label: "Answer Status Report",
    value: documentTypeList.ANSWERSTATUSREPORT,
    route: "/section/documents/answer-status-report/",
  },
];

export const noticeTypeList = {
  NOTICEFORHALFANHOURDISCUSSION: "NOTICE_FOR_HALF_AN_HOUR_DISCUSSION",
  NOTICEFORQUESTIONTOPRIVATEMEMBER: "NOTICE_FOR_QUESTION_TO_PRIVATE_MEMBERS",
  NOTICEFORSHORTNOTICE: "NOTICE_FOR_SHORT_NOTICE",
};

// Helper function to get the correct path prefix based on the current URL
const getPathPrefix = () => {
  return window.location.pathname.includes("/ppo/")
    ? "/ppo/my-notices"
    : "/member/my-notices";
};

export const noticeTypes = [
  {
    label: "Notice For Half An Hour Discussion",
    value: noticeTypeList.NOTICEFORHALFANHOURDISCUSSION,
    get route() {
      return `${getPathPrefix()}/half-an-hour-discussion/`;
    },
  },
  {
    label: "Notice For Question To Private Member",
    value: noticeTypeList.NOTICEFORQUESTIONTOPRIVATEMEMBER,
    get route() {
      return `${getPathPrefix()}/notice-for-question-to-private-member/`;
    },
  },
  {
    label: "Notice For Short Notice",
    value: noticeTypeList.NOTICEFORSHORTNOTICE,
    get route() {
      return `${getPathPrefix()}/short-notice/add/`;
    },
  },
];

export const ballotStatus = {
  CANCELLED: "CANCELLED",
  SUBMITTED: "SUBMITTED",
};

export const ballotTypes = [
  {
    label: "Notice For Short Notice",
    value: ballotStatus.CANCELLED,
  },
  {
    label: "Notice For Short Notice",
    value: ballotStatus.SUBMITTED,
  },
];

export const constants = {
  scheduleofactivity: "Schedule of Ativity",
  ministerofgroup: "Minister of Group",
};

export const getFormattedDocumentType = (docTypeEnum = "") => {
  const match = documentTypes.find((doc) => doc.value === docTypeEnum);
  return match?.label || formatEnumToLabel(docTypeEnum);
};

const formatEnumToLabel = (enumStr = "") => {
  return enumStr
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
};

export default documentTypes;
