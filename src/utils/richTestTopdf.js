export function renderProseMirrorJSON(json) {
  if (!json?.content) return "";

  return json.content
    .map((node) => {
      if (node.type === "paragraph") {
        const align = node.attrs?.textAlign || "left";

        const inner = (node.content || [])
          .map((child) => {
            if (child.type !== "text") return "";

            let text = child.text || "";
            let style = "";
            let tagStart = "";
            let tagEnd = "";

            if (child.marks?.length) {
              child.marks.forEach((mark) => {
                if (mark.type === "bold") {
                  tagStart += "<strong>";
                  tagEnd = "</strong>" + tagEnd;
                }
                if (mark.type === "textStyle") {
                  if (mark.attrs?.fontSize) {
                    style += `font-size: ${mark.attrs.fontSize};`;
                  }
                }
              });
            }

            return `${tagStart}<span style="${style}">${text}</span>${tagEnd}`;
          })
          .join("");

        return `<p style="text-align: ${align};">${inner}</p>`;
      }
      return "";
    })
    .join("");
}
