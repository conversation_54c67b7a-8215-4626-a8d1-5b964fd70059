/**
 * Simple route pattern matcher that maps URL paths to config paths
 * This eliminates the need to manually maintain route mappings
 */
export const findConfigPath = (pathname) => {
  // Direct pattern matching based on URL structure
  // This approach is simpler and less error-prone than maintaining a manual mapping

  if (pathname === '/') return 'home';

  // Remove leading slash and split into segments
  const segments = pathname.slice(1).split('/');

  // Build config path based on URL structure
  if (segments[0] === 'section') {
    if (segments[1] === 'documents') {
      if (segments.length === 2) return 'section.documents';
      if (segments[2] === 'minister-designation-group') {
        return segments[3] === 'view' ? 'section.documents.viewMinisterDesignationGroup' : 'section.documents.ministerDesignationGroup';
      }
      if (segments[2] === 'allotment-of-days') return 'section.documents.allotmentOfDays';
      if (segments[2] === 'schedule-of-activity') return 'section.documents.scheduleOfActivity';
      if (segments[2] === 'delayed-answer-bulletin') return 'section.documents.delayedAnswerBulletin';
      if (segments[2] === 'answer-status-report') return 'section.documents.answerStatusReport';
      if (segments[2] === 'delay-statement') return 'section.documents.delayStatement';
      if (segments[2] === 'late-answer-bulletin') return 'section.documents.lateAnswerBulletin';
      if (segments[2] === 'correction-of-answer') return 'section.documents.correctionOfAnswer';
    }
    if (segments[1] === 'question-edit-list') {
      if (segments.length === 2) return 'section.questionEditList';
      if (segments[2] === 'all') return 'section.questionEditList.all';
      if (segments[2] === 'action') return 'section.questionEditList.action';
      if (segments[2] === 'edit') return 'section.questionEditList.edit';
    }
    if (segments[1] === 'ballot') {
      if (segments[2] === 'list') return 'section.ballot.list';
      if (segments[2] === 'perform') return 'section.ballot.perform';
    }
    if (segments[1] === 'setting-starred-questions') return 'section.settingStarredQuestions';
    if (segments[1] === 'setting-of-unstarred-questions') return 'section.settingUnstarredQuestions';
    if (segments[1] === 'notices-for-question') {
      return segments[2] === 'all' ? 'section.noticesForQuestion.all' : 'section.noticesForQuestion.action';
    }
  }

  if (segments[0] === 'member') {
    if (segments[1] === 'consent') return 'member.consent';
    if (segments[1] === 'my-notices') {
      if (segments.length === 2) return 'member.myNotices';
      if (segments[2] === 'notice-bank') return 'member.myNotices.noticeBank';
      if (segments[2] === 'short-notice') return 'member.myNotices.shortNotice';
      if (segments[2] === 'half-an-hour-discussion') return 'member.myNotices.halfAnHourDiscussion';
      if (segments[2] === 'notice-for-question-to-private-member') return 'member.myNotices.noticeForQuestionToPrivateMember';
    }
    if (segments[1] === 'my-question-notices') {
      if (segments.length === 2) return 'member.myQuestionNotices';
      if (segments[2] === 'notice-bank') return 'member.myQuestionNotices.noticeBank';
      if (segments[2] === 'notice-for-question') return 'member.myQuestionNotices.noticeForQuestion';
    }
  }

  if (segments[0] === 'ppo') {
    if (segments[1] === 'consent') return 'ppo.consent';
    if (segments[1] === 'my-question-notices') {
      if (segments.length === 2) return 'ppo.myQuestionNotices';
      if (segments[2] === 'notice-bank') return 'ppo.myQuestionNotices.noticeBank';
      if (segments[2] === 'notice-for-question') return 'ppo.myQuestionNotices.noticeForQuestion';
    }
  }

  return null;
};

/**
 * Generate route from config path and params
 * Simplified version that builds routes based on config path structure
 */
export const generateRoute = (configPath, params = {}) => {
  if (configPath === 'home') return '/';

  const parts = configPath.split('.');
  let route = '';

  // Build route based on config path structure
  if (parts[0] === 'section') {
    route = '/section';
    if (parts[1] === 'documents') {
      route += '/documents';
      if (parts[2]) {
        if (parts[2] === 'viewMinisterDesignationGroup') {
          route += '/view/minister-designation-group';
        } else if (parts[2] === 'ministerDesignationGroup') {
          route += '/minister-designation-group';
        } else {
          route += '/' + parts[2].replace(/([A-Z])/g, '-$1').toLowerCase();
        }
        if (params.documentId) route += `/${params.documentId}`;
      }
    } else if (parts[1] === 'questionEditList') {
      route += '/question-edit-list';
      if (parts[2] === 'edit' && params.documentId) {
        route += `/edit/${params.documentId}`;
      } else if (parts[2]) {
        route += `/${parts[2]}`;
      }
    } else if (parts[1] === 'ballot') {
      route += '/ballot';
      if (parts[2]) route += `/${parts[2]}`;
      if (parts[2] === 'perform' && params.ballotingId) {
        route += `/${params.ballotingId}/${params.questionDate}/${params.status}`;
      }
    } else if (parts[1]) {
      route += '/' + parts[1].replace(/([A-Z])/g, '-$1').toLowerCase();
      if (params.documentId) route += `/${params.documentId}`;
    }
  } else if (parts[0] === 'member' || parts[0] === 'ppo') {
    route = `/${parts[0]}`;
    if (parts[1]) {
      route += '/' + parts[1].replace(/([A-Z])/g, '-$1').toLowerCase();
      if (parts[2]) {
        if (parts[2] === 'noticeForQuestionToPrivateMember') {
          route += '/notice-for-question-to-private-member';
        } else if (parts[2] === 'halfAnHourDiscussion') {
          route += '/half-an-hour-discussion';
        } else if (parts[2] === 'shortNotice') {
          route += '/short-notice/add';
        } else {
          route += '/' + parts[2].replace(/([A-Z])/g, '-$1').toLowerCase();
        }
        if (params.documentId) route += `/${params.documentId}`;
      }
    }
  }

  return route || null;
};

/**
 * Legacy compatibility - maintains the same API as before
 * @deprecated Use findConfigPath and generateRoute directly
 */
export const generateRouteUtils = () => ({
  findConfigPath,
  generateRoute,
  patterns: {}, // Empty for backward compatibility
});

/**
 * Legacy compatibility
 * @deprecated Use findConfigPath directly
 */
export const extractRoutePatternsFromRouter = () => ({});

