import { describe, expect, it } from "vitest";
import { cn, formatDateWithFallback, generateYearOptions } from ".";

describe("cn utility function", () => {
  it("should return a single class when given one class", () => {
    expect(cn("bg-red-500")).toBe("bg-red-500");
  });

  it("should merge multiple classes without conflicts", () => {
    expect(cn("bg-red-500", "text-white")).toBe("bg-red-500 text-white");
  });

  it("should override conflicting Tailwind classes", () => {
    expect(cn("bg-red-500", "bg-blue-500")).toBe("bg-blue-500");
  });

  it("should handle conditional classes", () => {
    // eslint-disable-next-line no-constant-binary-expression
    expect(cn("bg-red-500", false && "text-white")).toBe("bg-red-500");
  });

  it("should remove duplicate classes", () => {
    expect(cn("bg-red-500", "bg-red-500", "text-white")).toBe(
      "bg-red-500 text-white"
    );
  });

  it("should handle nested arrays of classes", () => {
    expect(cn(["bg-red-500", ["text-white"]])).toBe("bg-red-500 text-white");
  });

  it("should return an empty string when no valid classes are provided", () => {
    expect(cn()).toBe("");
    expect(cn(null, undefined, false)).toBe("");
  });
});

describe("formatDateWithFallback", () => {
  it("should format a valid date string", () => {
    const date = "2023-09-26T00:00:00Z";
    const result = formatDateWithFallback(date);
    expect(result).toBe(new Date(date).toLocaleDateString());
  });

  it("should return the fallback value if date is null", () => {
    const fallback = "No date available";
    const result = formatDateWithFallback(null, { fallback });
    expect(result).toBe(fallback);
  });

  it("should return the fallback value if date is undefined", () => {
    const fallback = "No date provided";
    const result = formatDateWithFallback(undefined, { fallback });
    expect(result).toBe(fallback);
  });

  it("should return the fallback value if date is an empty string", () => {
    const fallback = "Empty date";
    const result = formatDateWithFallback("", { fallback });
    expect(result).toBe(fallback);
  });

  it("should handle invalid date strings", () => {
    const invalidDate = "invalid-date-string";
    const result = formatDateWithFallback(invalidDate);
    expect(result).toBe(new Date(invalidDate).toLocaleDateString());
  });
});
describe("forYearPicker function", () => {
  it("should return an array", () => {
    const result = generateYearOptions();
    expect(Array.isArray(result)).toBe(true);
  });

  it("should generate the correct number of years", () => {
    const currentYear = new Date().getFullYear();
    const startYear = 1957;
    const expectedLength = currentYear - startYear + 1;

    const result = generateYearOptions();
    expect(result.length).toBe(expectedLength);
  });

  it("should have objects with correct 'label' and 'value' keys", () => {
    const result = generateYearOptions();

    result.forEach((item) => {
      expect(item).toHaveProperty("label");
      expect(item).toHaveProperty("value");
      expect(typeof item.label).toBe("string");
      expect(typeof item.value).toBe("string");
    });
  });

  it("should generate years from the current year to 1957", () => {
    const currentYear = new Date().getFullYear();
    const result = generateYearOptions();

    const years = result.map((item) => parseInt(item.value, 10));
    expect(years[0]).toBe(currentYear);
    expect(years[years.length - 1]).toBe(1957);
  });

  it("should have descending order of years", () => {
    const result = generateYearOptions();

    const years = result.map((item) => parseInt(item.value, 10));
    for (let i = 1; i < years.length; i++) {
      expect(years[i - 1]).toBeGreaterThan(years[i]);
    }
  });
});
