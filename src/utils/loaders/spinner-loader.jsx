import { LoaderCircleIcon } from "lucide-react";
import PropTypes from "prop-types";

function SpinnerLoader({
  size = "w-12 h-12",
  color = "#e83a7a",
  center = true,
}) {
  const containerClass = center ? "flex justify-center items-center" : "";

  return (
    <div className={containerClass}>
      <LoaderCircleIcon className={`animate-spin ${size}`} style={{ color }} />
    </div>
  );
}

SpinnerLoader.propTypes = {
  size: PropTypes.string,
  color: PropTypes.string,
  center: PropTypes.bool,
};

export default SpinnerLoader;
