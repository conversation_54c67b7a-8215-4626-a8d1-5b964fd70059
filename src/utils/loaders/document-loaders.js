import { store } from "@/app/store";
import { documentsApi } from "@/services/documents";

export const documentByIdLoader = async ({ params }) => {
  const { documentId } = params;
  const promise = store.dispatch(
    documentsApi.endpoints.getDocumentById.initiate({
      documentId,
    })
  );
  try {
    const response = await promise.unwrap();
    return { documentData: response };
  } catch (e) {
    console.error({ e });
    throw new Response(e?.title, { status: e?.status });
  } finally {
    promise.unsubscribe();
  }
};
