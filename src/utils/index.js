import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";

function cn(...inputs) {
  return twMerge(clsx(inputs));
}

function formatDateWithFallback(date, options = {}) {
  return date ? new Date(date).toLocaleDateString() : options?.fallback;
}
const formatDateYYYYMMDD = (date) => {
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, "0");
  const day = String(d.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};
function generateYearOptions() {
  const currentYear = new Date().getFullYear();
  const startYear = 1957;

  return Array.from({ length: currentYear - startYear + 1 }, (_, index) => {
    const year = currentYear - index;
    return { label: year.toString(), value: year.toString() };
  });
}
const truncate = (text, maxLength, suffix = "...") => {
  if (text.length <= maxLength) {
    return text;
  }
  return text.slice(0, maxLength - suffix.length) + suffix;
};

function generateYearList() {
  const years = [];
  for (let year = 2000; year <= 2021; year++) {
    years.push({ label: year.toString(), value: year.toString() });
  }
  return years;
}

function formatDate(date) {
  return new Date(date).toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "long",
    year: "numeric",
  });
}
function formatedDate(date) {
  return new Date(date).toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  });
}
 
const getYearRange = () => {
  const currentYear = new Date().getFullYear();
  return Object.fromEntries(
    Array.from({ length: currentYear - 1999 }, (_, i) => {
      const year = (2000 + i).toString();
      return [year, year];
    })
  );
};

const hasCommonKeys = (obj1, obj2) => {
  const obj1Keys = new Set(Object.keys(obj1));
  const obj2Keys = Object.keys(obj2);

  return obj2Keys.some((key) => obj1Keys.has(key));
};

const hasFormValuesChanged = (form, formSchema) => {
  const { dirtyFields } = form.formState;
  const formSchemaShape = formSchema.shape;

  const hasValuesChanged = hasCommonKeys(dirtyFields, formSchemaShape);

  return hasValuesChanged;
};
const shouldShowTick = (form) => {
  const formValues = form.getValues();
  const isFormFieldsEmpty = Object.values(formValues).every((formValue) => {
    if (formValue == null || formValue == undefined) return true;
    else if (typeof formValue == "string") return formValue.length === 0;
    else if (Array.isArray(formValue)) {
      if (formValue.length === 0) return true;
      return formValue.every((arrayItem) => {
        if (
          typeof arrayItem === "object" &&
          !Array.isArray(arrayItem) &&
          arrayItem !== null
        ) {
          return Object.values(arrayItem).every((value) => value?.length === 0);
        }
      });
    }
    return !!formValue.length;
  });
  const { dirtyFields } = form.formState;
  const hasDirtyFields = Object.keys(dirtyFields).length > 0;
  return !isFormFieldsEmpty && !hasDirtyFields;
};
const resetFormWithResponseData = (form, formSchema, updatedDocumentData) => {
  const schema = formSchema.deepPartial();

  const {
    data: validatedData,
    error: validationErrors,
    success: isValid,
  } = schema.safeParse(updatedDocumentData);

  if (isValid) {
    Object.entries(validatedData).forEach(([key, value]) => {
      form.resetField(key, { defaultValue: value });
    });
  } else {
    console.error(validationErrors);
  }
};

const formatMalayalamDate = (dateString) => {
  const date = new Date(dateString);

  const day = String(date.getDate()).padStart(2, "0");
  const month = date.toLocaleString("en-US", { month: "long" }); // English month name
  const year = date.getFullYear();
  const weekday = date.toLocaleString("ml-IN", { weekday: "long" }); // Malayalam weekday

  return `${day}-${month}-${year},${weekday}`;
};

const monthNames = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];
const daysOfWeek = ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"];

const formatDateTimeAMPM = (isoString) => {
  if (!isoString) return "";

  const date = new Date(isoString);

  // Format date as DD-MM-YYYY
  const day = String(date.getDate()).padStart(2, "0");
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const year = date.getFullYear();

  // Format time as h:mm am/pm
  let hours = date.getHours();
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const ampm = hours >= 12 ? "pm" : "am";
  hours = hours % 12;
  hours = hours ? hours : 12;

  return `${day}-${month}-${year}, ${hours}:${minutes} ${ampm}`;
};

export {
  cn,
  monthNames,
  daysOfWeek,
  formatDate,
  formatedDate,
  formatDateWithFallback,
  formatDateYYYYMMDD,
  generateYearList,
  generateYearOptions,
  hasCommonKeys,
  hasFormValuesChanged,
  getYearRange,
  truncate,
  resetFormWithResponseData,
  shouldShowTick,
  formatMalayalamDate,
  formatDateTimeAMPM,
};
