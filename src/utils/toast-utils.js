import { toast } from "@kla-v2/ui-components";

export const handleErrorMessage = (errorType, t) => {
  const errorMessages = {
    INVALID_FILE_FORMAT: t("Invalid file format. Only PDF files are allowed."),
    DUPLICATE_FILE: t("This file has already been uploaded."),
    UPLOAD_LIMIT_EXCEEDED: t("Upload limit exceeded. Please select fewer files."),
    UPLOAD_FAILED: t("File upload failed. Please try again."),
    UPLOAD_ERROR: t("An error occurred during the upload process."),
    FILE_TOO_LARGE: t("File is too large."),
  };

  const errorMessage = errorMessages[errorType] ?? t("An unknown error occurred.");
  toast.error(errorMessage);
};