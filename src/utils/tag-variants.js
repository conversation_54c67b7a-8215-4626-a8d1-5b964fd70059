export const getTableVariant = (status) => {
  switch (status) {
    case "submitted":
    case "SUBMITTED":
      return "approved";
    case "CANCELLED":
      return "resubmitted";
    case "DRAFT_IN_PROGRESS":
      return "draft";
    case "APPROVED":
      return "approved";
    case "REJECTED":
      return "rejected";
    case "LAID":
      return "laid";
    case "RE_SUBMITTED":
      return "resubmitted";
    case "RETURN":
      return "return";
    default:
      return "draft";
  }
};

export const getTableStatus = (status) => {
  switch (status) {
    case "SUBMITTED":
      return "Submitted";
    case "DRAFT_IN_PROGRESS":
      return "Draft in Progress";
    case "APPROVED":
      return "Approved";
    case "REJECTED":
      return "Rejected";
    case "LAID":
      return "Laid";
    case "RE_SUBMITTED":
      return "Re-Submitted";
    case "RETURN":
      return "Returned";
    case "CANCELLED":
      return "Cancelled";
    default:
      return "Draft";
  }
};
