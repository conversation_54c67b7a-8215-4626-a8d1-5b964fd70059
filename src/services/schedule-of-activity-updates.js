import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

export const soaUpdateApi = createApi({
  reducerPath: "soaUpdateApi",
  baseQuery: fetchBaseQuery({
    baseUrl: import.meta.env.VITE_API_BASE_URL + "api/",
  }),
  endpoints: (builder) => ({
    getSoaUpdate: builder.query({
      query: () => "revised-soa-updates",
      providesTags: ["soaUpdates"],
    }),
  }),
});

export const { useGetSoaUpdateQuery } = soaUpdateApi;
