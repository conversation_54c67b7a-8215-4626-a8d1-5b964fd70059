import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const halfAnHourDiscussionApi = createApi({
  reducerPath: "halfAnHourDiscussion",
  baseQuery: fetchBaseQuery({
    baseUrl: VITE_API_BASE_URL,
  }),
  tagTypes: ["HalfAnHourDiscussion"],
  endpoints: (builder) => ({
    getHalfAnHourDiscussionList: builder.query({
      query: ({
        searchText,
        ministerDesignation,
        portfolio,
        status,
        createdDateStartDate,
        createdDateEndDate,
        questionDateStartDate,
        questionDateEndDate,
        assembly,
        session,
        starred,
        page = 0,
        size = 10,
        sortBy = "createdAt",
        sortDirection = "desc"
      }) => {
        const params = {
          page,
          size,
          sortBy,
          sortDirection,
          noticeType: "NOTICE_FOR_HALF_AN_HOUR_DISCUSSION",
          ...(searchText && { searchText }),
          ...(ministerDesignation && { ministerDesignation }),
          ...(portfolio && { portfolio }),
          ...(status && { status }),
          ...(createdDateStartDate && { createdDateStartDate }),
          ...(createdDateEndDate && { createdDateEndDate }),
          ...(questionDateStartDate && { questionDateStartDate }),
          ...(questionDateEndDate && { questionDateEndDate }),
          ...(assembly && { assembly }),
          ...(session && { session }),
          ...(starred !== undefined && starred !== "" && { starred })
        };
        
        return {
          url: `api/notices/my-notices`,
          params
        };
      },
      providesTags: ["HalfAnHourDiscussion"],
    }),
    
    getHalfAnHourDiscussionBank: builder.query({
      query: ({
        searchText,
        ministerDesignation,
        portfolio,
        createdDateStartDate,
        createdDateEndDate,
        questionDateStartDate,
        questionDateEndDate,
        assembly,
        session,
        starred,
        page = 0,
        size = 10,
        sortBy = "createdAt",
        sortDirection = "desc"
      }) => {
        const params = {
          page,
          size,
          sortBy,
          sortDirection,
          noticeType: "NOTICE_FOR_HALF_AN_HOUR_DISCUSSION",
          ...(searchText && { searchText }),
          ...(ministerDesignation && { ministerDesignation }),
          ...(portfolio && { portfolio }),
          ...(createdDateStartDate && { createdDateStartDate }),
          ...(createdDateEndDate && { createdDateEndDate }),
          ...(questionDateStartDate && { questionDateStartDate }),
          ...(questionDateEndDate && { questionDateEndDate }),
          ...(assembly && { assembly }),
          ...(session && { session }),
          ...(starred !== undefined && starred !== "" && { starred })
        };
        
        return {
          url: `api/notices/notice-bank`,
          params
        };
      },
      providesTags: ["HalfAnHourDiscussion"],
    }),
    
    getHalfAnHourDiscussion: builder.query({
      query: (id) => `api/documents/${id}`,
      providesTags: (result, error, id) => [{ type: 'HalfAnHourDiscussion', id }],
    }),
    
    saveBasicDetails: builder.mutation({
      query: ({ id, data }) => ({
        url: `api/notice-for-half-an-hour-discussion/${id}/basic-details`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'HalfAnHourDiscussion', id },
        'HalfAnHourDiscussion'
      ],
    }),
    
    saveNoticeDetails: builder.mutation({
      query: ({ id, data }) => ({
        url: `api/notice-for-half-an-hour-discussion/${id}/notice-details`,
        method: 'POST',
        body: {
          ...data,
          id
        },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'HalfAnHourDiscussion', id },
        'HalfAnHourDiscussion'
      ],
    }),
    
    saveExplanatoryNote: builder.mutation({
      query: ({ id, data }) => ({
        url: `api/notice-for-half-an-hour-discussion/${id}/explanatory-note`,
        method: 'POST',
        body: {
          ...data,
          id
        },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'HalfAnHourDiscussion', id },
        'HalfAnHourDiscussion'
      ],
    }),
    
    saveSignature: builder.mutation({
      query: ({ id, data }) => ({
        url: `api/notice-for-half-an-hour-discussion/${id}/signature`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'HalfAnHourDiscussion', id },
        'HalfAnHourDiscussion'
      ],
    }),
    
    submitNotice: builder.mutation({
      query: (id) => ({
        url: `api/documents/${id}/submit`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'HalfAnHourDiscussion', id },
        'HalfAnHourDiscussion'
      ],
    }),
  }),
});

export const {
  useGetHalfAnHourDiscussionListQuery,
  useGetHalfAnHourDiscussionBankQuery,
  useGetHalfAnHourDiscussionQuery,
  useSaveBasicDetailsMutation,
  useSaveNoticeDetailsMutation,
  useSaveExplanatoryNoteMutation,
  useSaveSignatureMutation,
  useSubmitNoticeMutation
} = halfAnHourDiscussionApi;