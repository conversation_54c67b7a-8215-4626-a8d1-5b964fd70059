import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const noticeApi = createApi({
  reducerPath: "notices",
  baseQuery: fetchBaseQuery({
    baseUrl: VITE_API_BASE_URL,
  }),
  tagTypes: ["Notice", "NoticeBank"],
  endpoints: (builder) => ({
    getMyNotices: builder.query({
      query: ({
        noticeType,
        status,
        searchText,
        ministerDesignation,
        portfolio,
        questionDate,
        questionDateStartDate,
        questionDateEndDate,
        createdDate,
        createdDateStartDate,
        createdDateEndDate,
        assembly,
        session,
        starred,
        page = 0,
        size = 10,
        sortBy = "createdAt",
        sortDirection = "desc"
      }) => {
        const params = {
          page,
          size,
          sortBy,
          sortDirection,
          ...(noticeType && { noticeType }),
          ...(status && { status }),
          ...(searchText && { searchText }),
          ...(ministerDesignation && { ministerDesignation }),
          ...(portfolio && { portfolio }),
          ...(questionDate && { questionDate }),
          ...(questionDateStartDate && { questionDateStartDate }),
          ...(questionDateEndDate && { questionDateEndDate }),
          ...(createdDate && { createdDate }),
          ...(createdDateStartDate && { createdDateStartDate }),
          ...(createdDateEndDate && { createdDateEndDate }),
          ...(assembly && { assembly }),
          ...(session && { session }),
          ...(starred !== undefined && starred !== "" && { starred }),
          excludeDraftStatus: true // Based on specification in the backend
        };
        
        return {
          url: `api/notices/my-notices`,
          params
        };
      },
      providesTags: ["Notice"],
    }),
    getNoticeBank: builder.query({
      query: ({
        noticeType,
        searchText,
        ministerDesignation,
        portfolio,
        questionDate,
        questionDateStartDate,
        questionDateEndDate,
        createdDate,
        createdDateStartDate,
        createdDateEndDate,
        assembly,
        session,
        starred,
        page = 0,
        size = 10,
        sortBy = "createdAt",
        sortDirection = "desc"
      }) => {
        const params = {
          page,
          size,
          sortBy,
          sortDirection,
          ...(noticeType && { noticeType }),
          ...(searchText && { searchText }),
          ...(ministerDesignation && { ministerDesignation }),
          ...(portfolio && { portfolio }),
          ...(questionDate && { questionDate }),
          ...(questionDateStartDate && { questionDateStartDate }),
          ...(questionDateEndDate && { questionDateEndDate }),
          ...(createdDate && { createdDate }),
          ...(createdDateStartDate && { createdDateStartDate }),
          ...(createdDateEndDate && { createdDateEndDate }),
          ...(assembly && { assembly }),
          ...(session && { session }),
          ...(starred !== undefined && starred !== "" && { starred })
        };
        
        return {
          url: `api/notices/notice-bank`,
          params
        };
      },
      providesTags: ["NoticeBank"],
    }),
  }),
});

export const {
  useGetMyNoticesQuery,
  useGetNoticeBankQuery
} = noticeApi;