import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const otherNoticesNoticeBankApi = createApi({
  reducerPath: "otherNoticesNoticeBank",
  baseQuery: fetchBaseQuery({
    baseUrl: VITE_API_BASE_URL,
  }),
  tagTypes: ["OtherNoticeBanks"],
  endpoints: (builder) => ({
    getOtherNoticesList: builder.query({
      query: ({
        search,
        noticeType,
        ministerDesignation,
        portfolio,
        status,
        createdDateStartDate,
        createdDateEndDate,
        questionDateStartDate,
        questionDateEndDate,
        assembly,
        session,
        page = 0,
        size = 10,
        sortBy = "createdAt",
        sortDirection = "desc"
      }) => {
        const params = {
          page,
          size,
          sortBy,
          sortDirection,
          ...(search && { searchText: search }),
          ...(noticeType && { noticeType }),
          ...(ministerDesignation && { ministerDesignation }),
          ...(portfolio && { portfolio }),
          ...(status && { status }),
          ...(createdDateStartDate && { createdDateStartDate }),
          ...(createdDateEndDate && { createdDateEndDate }),
          ...(questionDateStartDate && { questionDateStartDate }),
          ...(questionDateEndDate && { questionDateEndDate }),
          ...(assembly && { assembly }),
          ...(session && { session }),
          status: "DRAFT" // Only show draft notices in the notice bank
        };
        
        return {
          url: `api/notices/notice-bank`,
          params
        };
      },
      providesTags: ["OtherNoticeBanks"],
    })
  }),
});

export const {
  useGetOtherNoticesListQuery
} = otherNoticesNoticeBankApi;