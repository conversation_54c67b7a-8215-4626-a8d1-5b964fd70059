import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const allotmentOfDaysApi = createApi({
  reducerPath: "allotmentOfDays",
  baseQuery: fetchBaseQuery({
    baseUrl: `${VITE_API_BASE_URL}api`,
    prepareHeaders: (headers) => {
      return headers;
    },
  }),
  endpoints: (builder) => ({
    getAllotmentOfDays: builder.query({
      query: (documentId) => {
        return {
          url: `/documents/${documentId}`,
        };
      },
      providesTags: ["AllotmentOfDays"],
    }),

    createAllotmentOfDays: builder.mutation({
      query: ({ data }) => {
        return {
          url: "documents/draft",
          method: "POST",
          body: {
            ...data,
            //TODO : referredCalendarOfSittingsId api has to be done
            referredCalendarOfSittingsId:
              "b4a17b1d-b8b2-4e94-917e-217bc5e5b9e4",
          },
        };
      },
      invalidatesTags: ["AllotmentOfDays"],
    }),

    updateAllotmentOfDays: builder.mutation({
      query: ({ documentId, data }) => {
        return {
          url: `/allotment-of-days/${documentId}`,
          method: "PATCH",
          body: data,
        };
      },
      invalidatesTags: ["AllotmentOfDays"],
    }),

    submitAllotmentOfDays: builder.mutation({
      query: ({ documentId, data }) => ({
        url: `/allotment-of-days/${documentId}`,
        method: "POST",
        body: JSON.stringify(data),
        headers: {
          "Content-Type": "application/json",
        },
      }),
      invalidatesTags: ["AllotmentOfDays"],
    }),

    getMinisterGroups: builder.query({
      query: (documentId) => {
        return {
          url: `/documents/${documentId}`,
        };
      },
      providesTags: ["MinisterGroups"],
    }),
    getLatestApproved: builder.query({
      query: (documentType) => ({
        url: `documents/latest-approved`,
        method: "GET",
        params: { documentType },
      }),
      providesTags: ["AllotmentOfDays"],
    }),
  }),
});

export const {
  useGetAllotmentOfDaysQuery,
  useLazyGetAllotmentOfDaysQuery,
  useCreateAllotmentOfDaysMutation,
  useUpdateAllotmentOfDaysMutation,
  useSubmitAllotmentOfDaysMutation,
  useGetMinisterGroupsQuery,
  useLazyGetLatestApprovedQuery,
} = allotmentOfDaysApi;
