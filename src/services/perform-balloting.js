import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const performballotsApi = createApi({
  reducerPath: "perform-ballots",
  tagTypes: ["Ballot", "RandomizedBallot"],
  baseQuery: fetchBaseQuery({
    baseUrl: `${VITE_API_BASE_URL}api`,
    prepareHeaders: (headers) => {
      console.log("VITE_API_BASE_URL:", VITE_API_BASE_URL);
      return headers;
    },
  }),
  endpoints: (builder) => ({
    getBallotPerformList: builder.query({
      query: (params = {}) => {
        return {
          url: "/notice-for-questions/members-by-date",
          params,
        };
      },
      // providesTags: ["Ballot"],
    }),
    performBalloting: builder.mutation({
      query: ({ data }) => {
        const url = `/ballots/perform-balloting`;
        return {
          url,
          method: "POST",
          body: data,
        };
      },
      invalidatesTags: ["Ballot"],
    }),
    getRandomizedBallotResult: builder.query({
      query: ({ params }) => ({
        url: `/ballots/date/${params}`,
      }),
      providesTags: ["Ballot"],
    }),
  
    cancelBallot: builder.mutation({
      query: ({ ballotId,body}) => ({
        url: `/ballots/${ballotId}/cancel`,
        method: "POST",
        body:body,
      }),
      invalidatesTags: ["Ballot", "RandomizedBallot"],
    }),

  }),
});

export const {
  useGetBallotPerformListQuery,
  usePerformBallotingMutation,
  useGetRandomizedBallotResultQuery,
  useCancelBallotMutation,
} = performballotsApi;
