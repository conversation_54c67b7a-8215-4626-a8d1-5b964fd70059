import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const lateAnswerApi = createApi({
  reducerPath: "lateAnswer",
  baseQuery: fetchBaseQuery({
    baseUrl: VITE_API_BASE_URL + "api",
  }),
  tagTypes: ["LateAnswerBulletin"],
  endpoints: (builder) => ({
    // Create LAB document
    createLateAnswerBulletin: builder.mutation({
      query: (data) => ({
        url: `/documents-mock/draft`,
        method: "POST",
        body: {
          type: "LATE_ANSWER_BULLETIN",
          name: data.name,
          assembly: data.assembly,
          session: data.session,
        },
      }),
      invalidatesTags: ["LateAnswerBulletin"],
    }),

    // Get LAB document
    getLateAnswerBulletin: builder.query({
      query: (id) => ({
        url: `/documents-mock/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "LateAnswerBulletin", id }],
    }),
  }),
});

export const {
  useCreateLateAnswerBulletinMutation,
  useGetLateAnswerBulletinQuery,
} = lateAnswerApi;
