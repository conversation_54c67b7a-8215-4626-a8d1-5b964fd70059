import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const consentApi = createApi({
  reducerPath: "consentApi",
  baseQuery: fetchBaseQuery({
    baseUrl: new URL(VITE_API_BASE_URL + "api/", location.origin).href,
    tagTypes: ["Consent"],
  }),
  endpoints: (builder) => ({
    fetchPendingConsents: builder.query({
      query: ({ params }) => ({
        url: "consents/received",
        method: "GET",
        params,
      }),
      providesTags: ["Consent"],
    }),
    fetchAcceptedConsents: builder.query({
      query: ({ params }) => ({
        url: "consents/accepted",
        method: "GET",
        params,
      }),
      providesTags: ["Consent"],
    }),
    fetchUserConsents: builder.query({
      query: ({ params }) => ({
        url: "consents/sent",
        method: "GET",
        params,
      }),
      providesTags: ["Consent"],
    }),

    requestConsent: builder.mutation({
      query: (data) => ({
        url: "consents/request",
        method: "POST",
        body: data,
      }),
    }),

    acceptConsent: builder.mutation({
      query: ({ consentId }) => ({
        url: `consents/${consentId}/accept`,
        method: "POST",
      }),
      invalidatesTags: ["Consent"],
    }),

    rejectConsent: builder.mutation({
      query: ({ consentId }) => ({
        url: `consents/${consentId}/reject`,
        method: "POST",
      }),
      invalidatesTags: ["Consent"],
    }),
    revokeConsent: builder.mutation({
      query: ({ consentId }) => ({
        url: `consents/${consentId}/revoke`,
        method: "POST",
      }),
      invalidatesTags: ["Consent"],
    }),

    // TODO mocked
    withdrawConsent: builder.mutation({
      query: ({ consentId }) => ({
        url: `consents/${consentId}/withdraw`,
        method: "POST",
      }),
      invalidatesTags: ["Consent"],
    }),
  }),
});

export const {
  useFetchPendingConsentsQuery,
  useFetchUserConsentsQuery,
  useFetchAcceptedConsentsQuery,
  useRequestConsentMutation,
  useAcceptConsentMutation,
  useRejectConsentMutation,
  useRevokeConsentMutation,
  useWithdrawConsentMutation,
} = consentApi;
