import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const ballotsApi = createApi({
  reducerPath: "ballots",
  baseQuery: fetchBaseQuery({
    baseUrl: `${VITE_API_BASE_URL}api`,
    prepareHeaders: (headers) => {
      return headers;
    },
  }),
  endpoints: (builder) => ({
    getBallotList: builder.query({
      query: (params = {}) => {
        return {
          url: "/ballots/search",
          params,
        };
      },
      providesTags: ["Ballot"],
    }),
    getBallotPerformList: builder.query({
      query: (params = {}) => {
        return {
          url: "/notice-for-questions/members-by-date",
          params,
        };
      },
      providesTags: ["Ballot"],
    }),
  }),
});

export const { useGetBallotListQuery, useGetBallotPerformListQuery } =
  ballotsApi;
