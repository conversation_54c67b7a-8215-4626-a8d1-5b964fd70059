import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const noticeBankApi = createApi({
  reducerPath: "noticeBank",
  baseQuery: fetchBaseQuery({
    baseUrl: VITE_API_BASE_URL + "api/",
  }),
  
  endpoints: (builder) => ({
    getNoticeBankList: builder.query({
      query: ({
        search = "",
        page = 0,
        size = 10,
        ministerDesignation = "",
        portfolio = "",
        currentNumber = "",
        kla = "",
        session = "",
      }) => {
        const params = {
          page: page + 1,
          size
        };
        
        if (search) params.search = search;
        if (ministerDesignation) params.ministerDesignation = ministerDesignation;
        if (portfolio) params.portfolio = portfolio;
        if (currentNumber) params.currentNumber = currentNumber;
        if (kla) params.kla = kla;
        if (session) params.session = session;
        
        return {
          url: `/notice-bank-for-questions/my-notices`,
          params
        };
      },
      providesTags: ["NoticeBank"],
    }),
  }),
});

export const {
  useGetNoticeBankListQuery,
} = noticeBankApi;