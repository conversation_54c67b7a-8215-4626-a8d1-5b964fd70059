import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
const { VITE_API_BASE_URL } = import.meta.env;
export const scheduleOfActivityApi = createApi({
  reducerPath: "scheduleOfActivityApi",
  baseQuery: fetchBaseQuery({
    // baseUrl: "/question-service/",
    baseUrl: VITE_API_BASE_URL + "api/",
  }),
  tagTypes: ["ScheduleOfActivity"],
  endpoints: (builder) => ({
    getScheduleOfActivity: builder.query({
      query: (documentId) => ({
        url: `documents/${documentId}`,
      }),
      providesTags: ["ScheduleOfActivity"],
    }),

    createScheduleOfActivity: builder.mutation({
      query: ({ data, referredAllotmentOfDaysId, assembly, session }) => ({
        url: "documents/draft",
        method: "POST",
        body: {
          ...data,
          referredAllotmentOfDaysId,
          assembly,
          session,
        },
      }),
      invalidatesTags: ["ScheduleOfActivity"],
    }),

    updateScheduleOfActivity: builder.mutation({
      query: ({ id, data }) => ({
        url: `/api/schedule-of-activity/${id}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: ["ScheduleOfActivity"],
    }),

    submitScheduleOfActivity: builder.mutation({
      query: (id) => ({
        url: `documents/${id}/submit`,
        method: "POST",
      }),
      invalidatesTags: ["ScheduleOfActivity"],
    }),
    latestApprovedScheduleOfActivity: builder.mutation({
      query: (params = {}) => ({
        url: `documents/latest-approved`,
        method: "GET",
        params,
      }),
      providesTags: ["ScheduleOfActivity"],
    }),
  }),
});

export const {
  useGetScheduleOfActivityQuery,
  useCreateScheduleOfActivityMutation,
  useUpdateScheduleOfActivityMutation,
  useSubmitScheduleOfActivityMutation,
  useLatestApprovedScheduleOfActivityMutation,
} = scheduleOfActivityApi;
