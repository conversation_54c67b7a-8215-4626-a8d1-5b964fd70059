import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
const { VITE_API_BASE_URL } = import.meta.env;

export const questionApi = createApi({
  reducerPath: "questionApi",
  baseQuery: fetchBaseQuery({
    baseUrl: VITE_API_BASE_URL + "api",
  }),
  tagTypes: ["QuestionNotice"],
  endpoints: (builder) => ({
    getNoticeById: builder.query({
      query: ({ documentId, params }) => {
        const url = `/documents/${documentId}`;
        return {
          url,
          params,
        };
      },
      providesTags: ["Notice"],
    }),
    createBasicDetails: builder.mutation({
      query: ({ data, id }) => ({
        url: `/notice-for-questions/${id}/basic-details`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["BasicDetails"],
    }),
    createNoticeDetails: builder.mutation({
      query: ({ data, id }) => ({
        // Destructure the incoming object
        url: `/notice-for-questions/${id}/notice-details`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["NoticeDetails"],
    }),
    submitNotice: builder.mutation({
      query: ({ documentId }) => ({
        url: `/documents/${documentId}/submit`,
        method: "POST",
      }),
      invalidatesTags: ["NoticeDetails"],
    }),
    checkDuplicate: builder.query({
      query: ({
        searchKeywords = [],
        assembly,
        session,
        matchType,
        page = 0,
        size = 10,
      }) => ({
        url: `check-duplicate`,
        method: "GET",
        params: {
          searchKeywords,
          assembly,
          session,
          matchType,
          page,
          size,
        },
      }),
      providesTags: ["CheckDuplicate"],
    }),
  }),
});

export const {
  useCreateBasicDetailsMutation,
  useCreateNoticeDetailsMutation,
  useSubmitNoticeMutation,
  useGetNoticeByIdQuery,
  useCheckDuplicateQuery,
} = questionApi;
