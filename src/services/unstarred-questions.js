// services/unstarred-questions.js
import { createApiService } from "@/utils/api-utils";
import { BASE_URLS, TAG_TYPES } from "@/constants/api-constants";

export const unstarredQuestionsApi = createApiService({
  reducerPath: "unstarredQuestions",
  baseUrl: BASE_URLS.API,
  tagTypes: [TAG_TYPES.UNSTARRED_QUESTIONS],
  endpoints: (builder) => ({
    getUnstarredQuestions: builder.query({
      query: (date) => ({
        url: `api/documents-mockUnstarred`,
        params: { date }, // Passing date as query param
      }),
      providesTags: [TAG_TYPES.UNSTARRED_QUESTIONS],
    }),
  }),
});

export const { useGetUnstarredQuestionsQuery } = unstarredQuestionsApi;
