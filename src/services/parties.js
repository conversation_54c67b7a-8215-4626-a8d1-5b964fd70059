import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

export const partyApi = createApi({
  reducerPath: "partyApi",
  baseQuery: fetchBaseQuery({
    baseUrl: import.meta.env.VITE_API_BASE_URL + "api/",
  }),
  endpoints: (builder) => ({
    getParties: builder.query({
      query: () => "parties",
      providesTags: ["Parties"],
    }),
    getMembersByParty: builder.query({
      query: (partyId) => `/members?partyId=${partyId}`,
      providesTags: (result, error, partyId) => [
        { type: "Members", id: partyId },
      ],
    }),
    getIndependentMLAs: builder.query({
      query: () => "/members?type=independent",
      providesTags: ["IndependentMLAs"],
    }),
  }),
});

export const {
  useGetPartiesQuery,
  useGetMembersByPartyQuery,
  useGetIndependentMLAsQuery,
} = partyApi;
