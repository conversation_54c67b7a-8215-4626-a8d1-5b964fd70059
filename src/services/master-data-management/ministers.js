import { createApiService } from "@/utils/api-utils";
import { BASE_URLS, TAG_TYPES } from "@/constants/api-constants";

export const ministerApi = createApiService({
  reducerPath: "mdmMinisters",
  baseUrl: BASE_URLS.MDM,
  tagTypes: [TAG_TYPES.MINISTERS],
  endpoints: (builder) => ({
    // Get Ministers List
    getMinisters: builder.query({
      query: () => ({
        url: "ministers",
      }),
      providesTags: [TAG_TYPES.MINISTERS],
    }),
  }),
});

export const { useGetMinistersQuery } = ministerApi;
