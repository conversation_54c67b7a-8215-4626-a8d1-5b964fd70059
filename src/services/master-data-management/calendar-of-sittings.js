import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_MDMS_API_BASE_URL } = import.meta.env;

export const calendarOfSittingsApi = createApi({
  reducerPath: "api/calendar-of-sittings",
  baseQuery: fetchBaseQuery({
    baseUrl: `${VITE_MDMS_API_BASE_URL}api`,
  }),

  endpoints: (builder) => ({
    getCalendarOfSittings: builder.query({
      query: () => {
        const url = `calendar-of-sittings`;
        return { url };
      },
    }),
  }),
});

export const { useGetCalendarOfSittingsQuery } = calendarOfSittingsApi;
