import { createApiService } from "@/utils/api-utils";
import { BASE_URLS, TAG_TYPES } from "@/constants/api-constants";

export const placeListData = createApiService({
  reducerPath: "mdmPlaceList",
  baseUrl: BASE_URLS.BASE,
  tagTypes: [TAG_TYPES.PLACES_LIST],
  endpoints: (builder) => ({
    getPlaceList: builder.query({
      query: () => ({
        url: "api/places-list",
        method: "GET",
      }),
      providesTags: [TAG_TYPES.PLACES_LIST],
    }),
  }),
});

export const { useGetPlaceListQuery } = placeListData;
