import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

 const { VITE_MDM_SERVICE_BASE_URL } = import.meta.env;

export const basicDetailsApi = createApi({
  reducerPath: "basicDetailsApi",
  baseQuery: fetchBaseQuery({
    baseUrl: VITE_MDM_SERVICE_BASE_URL,
  }),
  tagTypes: ["BasicDetails"],
  endpoints: (builder) => ({
    getDesignation: builder.query({
      query: () => "/designations", // Endpoint to fetch designations
      providesTags: ["Designation"], // Tag for caching
    }),
    getPortfolio: builder.query({
      query: () => "/portfolio-subjects", // Endpoint to fetch Portfolios
      providesTags: ["Portfolios"], // Tag for caching
    }),
    getSubSubject: builder.query({
      query: () => "/sub-subjects", // Endpoint to fetch SubSubjects
      providesTags: ["SubSubjects"], // Tag for caching
    }),
   
    getConsentMembers: builder.query({
      query: () => "api/consents/members-consented", 
      providesTags: ["Members"], 
    }),
  }),
  
});

export const { useGetDesignationQuery , useGetPortfolioQuery, useGetSubSubjectQuery, useGetConsentMembersQuery } = basicDetailsApi;
