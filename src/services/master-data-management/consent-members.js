import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_MDM_SERVICE_BASE_URL } = import.meta.env;

export const consentMembersApi = createApi({
  reducerPath: "consentMembersApi",
  baseQuery: fetchBaseQuery({
    baseUrl: VITE_MDM_SERVICE_BASE_URL,
  }),
  tagTypes: ["ConsentMembers"],
  endpoints: (builder) => ({
    getConsentMembers: builder.query({
      query: () => "api/consents/members-consented", 
      providesTags: ["ConsentMembers"], 
    }),
  }),
});

export const { useGetConsentMembersQuery } = consentMembersApi;