import { createApiService } from "@/utils/api-utils";
import { BASE_URLS, TAG_TYPES } from "@/constants/api-constants";

export const assemblyApi = createApiService({
  reducerPath: "mdmAssembly",
  baseUrl: BASE_URLS.MDMS,
  tagTypes: [TAG_TYPES.ASSEMBLY],
  endpoints: (builder) => ({
    getAllAssembly: builder.query({
      query: () => ({
        url: `api/get/assembly`,
      }),
      providesTags: [TAG_TYPES.ASSEMBLY],
    }),

    getSessionsByKLA: builder.query({
      query: (klaId) => ({
        url: `api/get/assembly/${klaId}/sessions`,
      }),
      providesTags: (result, error, klaId) => [
        { type: TAG_TYPES.ASSEMBLY, id: klaId }
      ],
    }),
  }),
});

export const { useGetAllAssemblyQuery, useGetSessionsByKLAQuery } = assemblyApi;
