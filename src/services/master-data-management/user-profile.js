import { createApiService } from "@/utils/api-utils";
import { BASE_URLS, TAG_TYPES } from "@/constants/api-constants";

// Custom header preparation function for authentication
const prepareAuthHeaders = (headers) => {
  const token = localStorage.getItem("token");
  if (token) {
    headers.set("authorization", `Bearer ${token}`);
  }
  return headers;
};

export const userProfileApi = createApiService({
  reducerPath: "userProfileApi",
  baseUrl: BASE_URLS.BASE,
  tagTypes: [TAG_TYPES.USER_PROFILE],
  prepareHeaders: prepareAuthHeaders,
  endpoints: (builder) => ({
    getCurrentUserProfile: builder.query({
      query: () => ({
        url: "api/user/current-profile",
      }),
      providesTags: [TAG_TYPES.USER_PROFILE],
    }),
  }),
});

export const {
  useGetCurrentUserProfileQuery,
  useLazyGetCurrentUserProfileQuery,
} = userProfileApi;
