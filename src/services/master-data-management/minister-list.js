import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_MDM_SERVICE_BASE_URL } = import.meta.env;
export const ministerMdmListApi = createApi({
  reducerPath: "ministerMdmList",
  baseQuery: fetchBaseQuery({
    baseUrl: new URL(VITE_MDM_SERVICE_BASE_URL, location.origin).href,
  }),
  endpoints: (builder) => ({
    getMinisterList: builder.query({
      query: () => {
        const url = `/ministers`;
        return {
          url,
        };
      },
    }),
  }),
});

export const { useGetMinisterListQuery } = ministerMdmListApi;
