import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_MDM_SERVICE_BASE_URL } = import.meta.env;

export const privateMemberApi = createApi({
  reducerPath: "privateMemberApi",
  baseQuery: fetchBaseQuery({ baseUrl: VITE_MDM_SERVICE_BASE_URL }),

  endpoints: (builder) => ({
    getPrivateMemberResolutions: builder.query({
      query: () => "api/private-member-resolutions",
      transformResponse: (response) => response.data,
      providesTags: ["PrivateMemberResolutions"],
    }),

    getPrivateMemberBills: builder.query({
      query: () => "api/private-member-bills",
      transformResponse: (response) => response.data,
      providesTags: ["PrivateMemberBills"],
    }),

    getPrivateMemberList: builder.query({
      query: () => "api/private-member-list",
      transformResponse: (response) => response.data,
      providesTags: ["PrivateMemberList"],
    }),
  }),
});

export const {
  useGetPrivateMemberResolutionsQuery,
  useGetPrivateMemberBillsQuery,
  useGetPrivateMemberListQuery,
} = privateMemberApi;
