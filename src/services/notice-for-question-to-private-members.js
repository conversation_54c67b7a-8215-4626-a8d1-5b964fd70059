import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const noticeForQuestionToPrivateMembersApi = createApi({
  reducerPath: "noticeForQuestionToPrivateMembers",
  baseQuery: fetchBaseQuery({
    baseUrl: VITE_API_BASE_URL,
  }),
  tagTypes: ["NoticeForQuestionToPrivateMembers"],
  endpoints: (builder) => ({
    getPrivateMemberNoticeList: builder.query({
      query: ({
        searchText,
        ministerDesignation,
        portfolio,
        status,
        createdDateStartDate,
        createdDateEndDate,
        questionDateStartDate,
        questionDateEndDate,
        assembly,
        session,
        starred,
        page = 0,
        size = 10,
        sortBy = "createdAt",
        sortDirection = "desc",
      }) => {
        const params = {
          page,
          size,
          sortBy,
          sortDirection,
          noticeType: "NOTICE_FOR_QUESTION_TO_PRIVATE_MEMBERS",
          ...(searchText && { searchText }),
          ...(ministerDesignation && { ministerDesignation }),
          ...(portfolio && { portfolio }),
          ...(status && { status }),
          ...(createdDateStartDate && { createdDateStartDate }),
          ...(createdDateEndDate && { createdDateEndDate }),
          ...(questionDateStartDate && { questionDateStartDate }),
          ...(questionDateEndDate && { questionDateEndDate }),
          ...(assembly && { assembly }),
          ...(session && { session }),
          ...(starred !== undefined && starred !== "" && { starred }),
        };

        return {
          url: `api/notices/my-notices`,
          params,
        };
      },
      providesTags: ["NoticeForQuestionToPrivateMembers"],
    }),

    getPrivateMemberNoticeBank: builder.query({
      query: ({
        searchText,
        ministerDesignation,
        portfolio,
        createdDateStartDate,
        createdDateEndDate,
        questionDateStartDate,
        questionDateEndDate,
        assembly,
        session,
        starred,
        page = 0,
        size = 10,
        sortBy = "createdAt",
        sortDirection = "desc",
      }) => {
        const params = {
          page,
          size,
          sortBy,
          sortDirection,
          noticeType: "NOTICE_FOR_QUESTION_TO_PRIVATE_MEMBERS",
          ...(searchText && { searchText }),
          ...(ministerDesignation && { ministerDesignation }),
          ...(portfolio && { portfolio }),
          ...(createdDateStartDate && { createdDateStartDate }),
          ...(createdDateEndDate && { createdDateEndDate }),
          ...(questionDateStartDate && { questionDateStartDate }),
          ...(questionDateEndDate && { questionDateEndDate }),
          ...(assembly && { assembly }),
          ...(session && { session }),
          ...(starred !== undefined && starred !== "" && { starred }),
        };

        return {
          url: `api/notices/notice-bank`,
          params,
        };
      },
      providesTags: ["NoticeForQuestionToPrivateMembers"],
    }),

    getPrivateMemberNotice: builder.query({
      query: ({ documentId }) => `api/documents/${documentId}`,
      providesTags: (result, error, documentId) => [
        { type: "NoticeForQuestionToPrivateMembers", documentId },
      ],
    }),

    getNoticeDetails: builder.query({
      query: ({ documentId }) =>
        `api/notice-for-questions-to-private-members/${documentId}/notice-details`,
      providesTags: (result, error, documentId) => [
        { type: "NoticeForQuestionToPrivateMembers", documentId },
      ],
    }),

    saveBasicDetails: builder.mutation({
      query: ({ documentId, body }) => ({
        url: `api/notice-for-questions-to-private-members/${documentId}/basic-details`,
        method: "POST",
        body,
      }),
      invalidatesTags: (result, error, { documentId }) => [
        { type: "NoticeForQuestionToPrivateMembers", documentId },
        "NoticeForQuestionToPrivateMembers",
      ],
    }),

    saveNoticeDetails: builder.mutation({
      query: ({ documentId, data }) => ({
        url: `api/notice-for-questions-to-private-members/${documentId}/notice-details`,
        method: "POST",
        body: {
          ...data,
          documentId,
        },
      }),
      invalidatesTags: (result, error, { documentId }) => [
        { type: "NoticeForQuestionToPrivateMembers", documentId },
        "NoticeForQuestionToPrivateMembers",
      ],
    }),

    submitNotice: builder.mutation({
      query: ({ documentId }) => ({
        url: `api/documents/${documentId}/submit`,
        method: "POST",
      }),
      invalidatesTags: (result, error, documentId) => [
        { type: "NoticeForQuestionToPrivateMembers", documentId },
        "NoticeForQuestionToPrivateMembers",
      ],
    }),
  }),
});

export const {
  useGetPrivateMemberNoticeListQuery,
  useGetPrivateMemberNoticeBankQuery,
  useGetPrivateMemberNoticeQuery,
  useGetNoticeDetailsQuery,
  useSaveBasicDetailsMutation,
  useSaveNoticeDetailsMutation,
  useSubmitNoticeMutation,
} = noticeForQuestionToPrivateMembersApi;
