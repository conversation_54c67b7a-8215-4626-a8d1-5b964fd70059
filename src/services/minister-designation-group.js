import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const ministerDesignationGroupApi = createApi({
  reducerPath: "ministerDesignationGroup",
  baseQuery: fetchBaseQuery({
    baseUrl: VITE_API_BASE_URL + "api",
  }),
  tagTypes: ["MinisterDesignationGroup", "Ministers"],
  endpoints: (builder) => ({
    // Create Minister Designation Group
    createMinisterDesignationGroup: builder.mutation({
      query: ({ documentId, data }) => ({
        url: `/minister-designation-groups/${documentId}/designation-group`,
        method: "POST",
        body: { order: data.order },
      }),
      invalidatesTags: ["MinisterDesignationGroup"],
    }),

    // Update Minister Designation Group
    updateMinisterDesignationGroup: builder.mutation({
      query: ({ documentId, data }) => ({
        url: `/minister-designation-groups/${documentId}`,
        method: "PUT",
        body: data,
      }),
      invalidatesTags: ["MinisterDesignationGroup"],
    }),

    // Delete Minister Designation Group
    deleteMinisterDesignationGroup: builder.mutation({
      query: ({ documentId, groupId }) => ({
        url: `/minister-designation-groups/${documentId}/designation-group/${groupId}`,
        method: "DELETE",
      }),
      invalidatesTags: ["MinisterDesignationGroup"],
    }),

    //Get latest approved minister designation group
    getLatestApprovedMinisterGroups: builder.query({
      query: (documentType) => ({
        url: `/documents/latest-approved`,
        params: { documentType },
      }),
      providesTags: ["MinisterGroups"],
    }),

    //Check if updates are available for minister designation group
    checkUpdatesAvailable: builder.query({
      query: (id) => ({
        url: `/minister-designation-groups/${id}/updates-available`,
        method: "GET",
      }),
      providesTags: ["MinisterDesignationGroup"],
    }),

    //Apply updates to minister designation group
    applyUpdatesToMinisterDesignationGroup: builder.mutation({
      query: (id) => ({
        url: `/minister-designation-groups/${id}/apply-updates`,
        method: "POST",
      }),
      invalidatesTags: ["MinisterDesignationGroup", "Ministers"],
    }),
  }),
});

export const {
  useCreateMinisterDesignationGroupMutation,
  useUpdateMinisterDesignationGroupMutation,
  useDeleteMinisterDesignationGroupMutation,
  useLazyGetLatestApprovedMinisterGroupsQuery,
  useCheckUpdatesAvailableQuery,
  useApplyUpdatesToMinisterDesignationGroupMutation,
} = ministerDesignationGroupApi;
