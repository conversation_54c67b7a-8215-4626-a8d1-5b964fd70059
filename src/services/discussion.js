import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const noticesDiscussionAPI = createApi({
  reducerPath: "noticesAPI",
  baseQuery: fetchBaseQuery({
    baseUrl: VITE_API_BASE_URL + "api/",
  }),

  endpoints: (builder) => ({
    getHalfanHourNotice: builder.query({
      query: ({ documentId }) => {
        const url = `/documents/${documentId}`;
        return {
          url,
        };
      },
      providesTags: ["GetNotice"],
    }),
    getquestionNumbers: builder.query({
      query: ({ questionDate }) => {
        const url = `/notice-for-questions/question-numbers?questionDate=${questionDate}`;
        if (!questionDate) return;
        return {
          url,
        };
      },
      providesTags: ["GetNumbers"],
    }),
    postBasicDetails: builder.mutation({
      query: ({ documentId, data }) => {
        const url = `/notice-for-half-an-hour-discussion/${documentId}/basic-details`;
        return {
          url,
          body: data,
          method: "POST",
        };
      },
    }),
    postNoticeDetails: builder.mutation({
      query: ({ documentId, data }) => {
        const url = `/notice-for-half-an-hour-discussion/${documentId}/notice-details`;
        return {
          url,
          body: data,
          method: "POST",
        };
      },
    }),
    postExplanatoryDetails: builder.mutation({
      query: ({ documentId, data }) => {
        const url = `/notice-for-half-an-hour-discussion/${documentId}/explanatory-note`;
        return {
          url,
          body: data,
          method: "POST",
        };
      },
    }),
    postNoticeSignature: builder.mutation({
      query: ({ documentId, data }) => {
        const url = `/notice-for-half-an-hour-discussion/${documentId}/signature`;
        return {
          url,
          body: data,
          method: "POST",
        };
      },
    }),
    submitHalfanHourDiscussion: builder.mutation({
      query: ({ documentId }) => {
        const url = `/documents/${documentId}/submit`;
        return {
          url,
          method: "POST",
        };
      },
    }),
  }),
});

export const {
  useGetHalfanHourNoticeQuery,
  usePostBasicDetailsMutation,
  usePostNoticeDetailsMutation,
  usePostExplanatoryDetailsMutation,
  usePostNoticeSignatureMutation,
  useGetquestionNumbersQuery,
  useSubmitHalfanHourDiscussionMutation,
} = noticesDiscussionAPI;
