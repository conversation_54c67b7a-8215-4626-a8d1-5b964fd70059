import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

export const explanatoryNoteApi = createApi({
  reducerPath: "explanatoryNoteApi",
  baseQuery: fetchBaseQuery({
    baseUrl: import.meta.env.VITE_API_BASE_URL + "api/",
  }),
  endpoints: (builder) => ({
    getExplanatoryNote: builder.query({
      query: () => "explanatory-note-details",
      providesTags: ["explanatory"],
    }),
  }),
});

export const { useGetExplanatoryNoteQuery } = explanatoryNoteApi;
