import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const questionEditApi = createApi({
  reducerPath: "questionEdit",
  baseQuery: fetchBaseQuery({
    baseUrl: `${VITE_API_BASE_URL}api/`,
    prepareHeaders: (headers) => {
      return headers;
    },
  }),
  tagTypes: ["QuestionBasicDetails", "QuestionNoticeDetails"],
  endpoints: (builder) => ({
    // GET Basic Details
    getQuestionBasicDetails: builder.query({
      query: (documentId) => ({
        url: `question/basic-details`,
        method: "GET",
        params: { id: documentId },
      }),
      providesTags: (result, error, documentId) => [{ type: "QuestionBasicDetails", id: documentId }],
    }),

    // PATCH Basic Details
    updateQuestionBasicDetails: builder.mutation({
      query: (data) => ({
        url: `question/basic-details`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "QuestionBasicDetails", id }],
    }),

    // GET Notice Details
    getQuestionNoticeDetails: builder.query({
      query: (documentId) => ({
        url: `question/notice-details`,
        method: "GET",
        params: { id: documentId },
      }),
      providesTags: (result, error, documentId) => [{ type: "QuestionNoticeDetails", id: documentId }],
    }),

    // PATCH Notice Details
    updateQuestionNoticeDetails: builder.mutation({
      query: (data) => ({
        url: `question/notice-details`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "QuestionNoticeDetails", id }],
    }),
  }),
});

export const {
  useGetQuestionBasicDetailsQuery,
  useUpdateQuestionBasicDetailsMutation,
  useGetQuestionNoticeDetailsQuery,
  useUpdateQuestionNoticeDetailsMutation,
} = questionEditApi;