import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const answerStatusApi = createApi({
  reducerPath: "answerStatusReport",
  baseQuery: fetchBaseQuery({
    baseUrl: VITE_API_BASE_URL + "api",
  }),
  tagTypes: ["AnswerStatusReport"],
  endpoints: (builder) => ({
    createAnswerStatusReport: builder.mutation({
      query: ({ data }) => ({
        url: `documents-mockas/draft`,
        method: "POST",
        body: {
          type: "ANSWER_STATUS_REPORT",
          name: data.name,
          assembly: data.assembly,
          session: data.session,
        },
      }),
      invalidatesTags: ["AnswerStatusReport"],
    }),
    getDocumentById: builder.query({
      query: (documentId) => `documents-mockas/${documentId}`,
      providesTags: (documentId) => [
        { type: "AnswerStatusReport", documentId },
      ],
    }),
    addAnswerStatusReport: builder.mutation({
      query: ({ data }) => ({
        url: `documents-mockas/${data.documentId}/submit`,
        method: "POST",
        body: {
          assembly: data.assembly,
          session: data.session,
        },
      }),
      invalidatesTags: ["CreateAnswerStatusReport"],
    }),
  }),
});

export const {
  useCreateAnswerStatusReportMutation,
  useGetDocumentByIdQuery,
  useAddAnswerStatusReportMutation,
} = answerStatusApi;
