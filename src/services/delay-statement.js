import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
// const { BASE_URL } = import.meta.env;
export const delayStatementApi = createApi({
  reducerPath: "delayStatementApi",
  baseQuery: fetchBaseQuery({
    baseUrl: import.meta.env.VITE_API_BASE_URL + "api/",
  }),
  prepareHeaders: (headers) => {
    return headers;
  },
  endpoints: (builder) => ({
    updateDelayStatementDraft: builder.mutation({
      query: (body) => ({
        url: "documents-mocks/draft",
        method: "PATCH",
        body,
      }),
    }),
    // GET: Fetch delay statement by ID
    getDelayStatementListById: builder.query({
      query: (id) => {
        return {
          url: `/documents-mocks/${id}`,
        };
      },
    }),
    refreshLAB: builder.mutation({
      query: () => ({ url: `delay-statement-list/refresh`, method: "GET" }),
    }),
    uploadDelayStatement: builder.mutation({
      query: ({ data }) => ({
        url: `delay-statements/upload`,
        method: "POST",
        body: { ...data },
      }),
    }),
    addDelayStatementEntry: builder.mutation({
      query: ({ data, id }) => ({
        url: `delay-statements/${id}`,
        method: "POST",
        body: { ...data },
      }),
    }),
    removeDelayStatementEntry: builder.mutation({
      query: ({ id, documentId, reason }) => {
        return {
          url: `delay-statement-list/${documentId}/delay-statement/${id}/remove`,
          method: "POST",
          body: { reason },
        };
      },
    }),
    reorderDelayStatementEntry: builder.mutation({
      query: ({
        ministerWiseLayingListId,
        delayStatementEntries,
        documentId,
      }) => ({
        url: `delay-statement-list/${documentId}`,
        method: "POST",
        body: {
          ministerWiseLayingListId,
          delayStatementEntries,
        },
      }),
    }),
  }),
});

export const {
  useUpdateDelayStatementDraftMutation,
  useGetDelayStatementListByIdQuery,
  useRefreshLABMutation,
  useUploadDelayStatementMutation,
  useAddDelayStatementEntryMutation,
  useRemoveDelayStatementEntryMutation,
  useReorderDelayStatementEntryMutation,
} = delayStatementApi;
