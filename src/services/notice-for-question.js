import { createApiService } from "@/utils/api-utils";
import { BASE_URLS, TAG_TYPES } from "@/constants/api-constants";

export const noticeForQuestionApi = createApiService({
  reducerPath: "noticeForQuestion",
  baseUrl: BASE_URLS.API,
  tagTypes: [TAG_TYPES.NOTICE_FOR_QUESTION],
  endpoints: (builder) => ({
    // Get Notice for Question document
    getNoticeForQuestion: builder.query({
      query: (id) => ({
        url: `api/documents/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: TAG_TYPES.NOTICE_FOR_QUESTION, id }],
    }),

    getNoticeForQuestionList: builder.query({
      query: ({
        search = "",
        noticeNumber = "",
        ministerDesignation = "",
        portfolio = "",
        members = "",
        category = "",
        clubbed = "",
        assembly = "",
        session = "",
        status = "",
        noticePriority = "",
        questionDateStartDate = "",
        questionDateEndDate = "",
        createdDateStartDate = "",
        createdDateEndDate = "",
        page = 0,
        size = 10,
        sortBy = "createdAt",
        sortDirection = "desc"
      }) => {
        const params = {
          page,
          size,
          sortBy,
          sortDirection,
          noticeType: "NOTICE_FOR_QUESTION",
        };

        if (search) params.searchText = search;
        if (noticeNumber) params.noticeNumber = noticeNumber;
        if (ministerDesignation) params.ministerDesignation = ministerDesignation;
        if (portfolio) params.portfolio = portfolio;
        if (members) params.members = members;
        if (category === "Starred") params.starred = true;
        if (category === "unStarred") params.starred = false;
        if (category === "Unstarred") params.starred = false;
        if (clubbed === "Clubbed") params.clubbed = true;
        if (clubbed === "notClubbed") params.clubbed = false;
        if (assembly) params.assembly = assembly;
        if (session) params.session = session;
        if (status) params.status = status;
        if (noticePriority) params.noticePriority = noticePriority;
        if (questionDateStartDate) params.questionDateStartDate = questionDateStartDate;
        if (questionDateEndDate) params.questionDateEndDate = questionDateEndDate;
        if (createdDateStartDate) params.createdDateStartDate = createdDateStartDate;
        if (createdDateEndDate) params.createdDateEndDate = createdDateEndDate;

        return {
          url: `api/notices/my-notices`,
          params,
        };
      },
      providesTags: [TAG_TYPES.NOTICE_BANK],
    }),

    saveBasicDetails: builder.mutation({
      query: ({ id, data }) => ({
        url: `api/notice-for-questions/${id}/basic-details`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: TAG_TYPES.NOTICE_FOR_QUESTION, id },
        TAG_TYPES.NOTICE_FOR_QUESTION
      ],
    }),
    
    saveNoticeDetails: builder.mutation({
      query: ({ id, data }) => ({
        url: `api/notice-for-questions/${id}/notice-details`,
        method: 'POST',
        body: {
          ...data,
          id
        },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: TAG_TYPES.NOTICE_FOR_QUESTION, id },
        TAG_TYPES.NOTICE_FOR_QUESTION
      ],
    }),
    
    submitNotice: builder.mutation({
      query: (id) => ({
        url: `api/documents/${id}/submit`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, id) => [
        { type: TAG_TYPES.NOTICE_FOR_QUESTION, id },
        TAG_TYPES.NOTICE_FOR_QUESTION
      ],
    }),
  }),
});

export const {
  useGetNoticeForQuestionQuery,
  useGetNoticeForQuestionListQuery,
  useSaveBasicDetailsMutation,
  useSaveNoticeDetailsMutation,
  useSubmitNoticeMutation
} = noticeForQuestionApi;
