import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const actionToBeTakenApi = createApi({
  reducerPath: "actionToBeTaken",
  baseQuery: fetchBaseQuery({
    baseUrl: VITE_API_BASE_URL + "api/",
  }),
  
  endpoints: (builder) => ({
    getActionToBeTaken: builder.query({
      query: ({
        search = "",
        noticeNumber = "",
        ministerDesignation = "",
        portfolio = "",
        members = "",
        category = "",
        clubbed = "",
        kla = "",
        session = "",
        status = "",
        noticePriority = "",
        questionDateStartDate = "",
        questionDateEndDate = "",
        page = 0,
        size = 10
      }) => {
        const params = {
          page: page + 1,
          size
        };
        
        if (search) params.search = search;
        if (noticeNumber) params.noticeNumber = noticeNumber;
        if (ministerDesignation) params.ministerDesignation = ministerDesignation;
        if (portfolio) params.portfolio = portfolio;
        if (members) params.members = members;
        if (category === "Starred") params.starred = true;
        if (category === "unStarred") params.starred = false;
        if (clubbed === "Clubbed") params.clubbed = true;
        if (clubbed === "notClubbed") params.clubbed = false;
        if (kla) params.kla = kla;
        if (session) params.session = session;
        if (status) params.status = status;
        if (noticePriority) params.noticePriority = noticePriority;
        if (questionDateStartDate) params.questionDateStartDate = questionDateStartDate;
        if (questionDateEndDate) params.questionDateEndDate = questionDateEndDate;
        
        return {
          url: `/section-notice-for-questions`,
          params
        };
      },
      providesTags: ["ActionToBeTaken"],
    }),
  }),
});

export const {
  useGetActionToBeTakenQuery,
} = actionToBeTakenApi;