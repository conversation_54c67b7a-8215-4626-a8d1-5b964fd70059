import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const { VITE_API_BASE_URL } = import.meta.env;

export const noticeForShortNoticeApi = createApi({
  reducerPath: "noticeForShortNotice",
  baseQuery: fetchBaseQuery({
    baseUrl: VITE_API_BASE_URL,
  }),
  tagTypes: ["NoticeForShortNotice"],
  endpoints: (builder) => ({
    getShortNoticeList: builder.query({
      query: ({
        searchText,
        ministerDesignation,
        portfolio,
        status,
        createdDateStartDate,
        createdDateEndDate,
        questionDateStartDate,
        questionDateEndDate,
        assembly,
        session,
        starred,
        page = 0,
        size = 10,
        sortBy = "createdAt",
        sortDirection = "desc"
      }) => {
        const params = {
          page,
          size,
          sortBy,
          sortDirection,
          noticeType: "NOTICE_FOR_SHORT_NOTICE",
          ...(searchText && { searchText }),
          ...(ministerDesignation && { ministerDesignation }),
          ...(portfolio && { portfolio }),
          ...(status && { status }),
          ...(createdDateStartDate && { createdDateStartDate }),
          ...(createdDateEndDate && { createdDateEndDate }),
          ...(questionDateStartDate && { questionDateStartDate }),
          ...(questionDateEndDate && { questionDateEndDate }),
          ...(assembly && { assembly }),
          ...(session && { session }),
          ...(starred !== undefined && starred !== "" && { starred })
        };
        
        return {
          url: `api/notices/my-notices`,
          params
        };
      },
      providesTags: ["NoticeForShortNotice"],
    }),
    
    getShortNoticeBank: builder.query({
      query: ({
        searchText,
        ministerDesignation,
        portfolio,
        createdDateStartDate,
        createdDateEndDate,
        questionDateStartDate,
        questionDateEndDate,
        assembly,
        session,
        starred,
        page = 0,
        size = 10,
        sortBy = "createdAt",
        sortDirection = "desc"
      }) => {
        const params = {
          page,
          size,
          sortBy,
          sortDirection,
          noticeType: "NOTICE_FOR_SHORT_NOTICE",
          ...(searchText && { searchText }),
          ...(ministerDesignation && { ministerDesignation }),
          ...(portfolio && { portfolio }),
          ...(createdDateStartDate && { createdDateStartDate }),
          ...(createdDateEndDate && { createdDateEndDate }),
          ...(questionDateStartDate && { questionDateStartDate }),
          ...(questionDateEndDate && { questionDateEndDate }),
          ...(assembly && { assembly }),
          ...(session && { session }),
          ...(starred !== undefined && starred !== "" && { starred })
        };
        
        return {
          url: `api/notices/notice-bank`,
          params
        };
      },
      providesTags: ["NoticeForShortNotice"],
    }),
    
    getShortNotice: builder.query({
      query: (id) => `api/documents/${id}`,
      providesTags: (result, error, id) => [{ type: 'NoticeForShortNotice', id }],
    }),
    
    getNoticeDetails: builder.query({
      query: (id) => `api/notice-for-short-notices/${id}/notice-details`,
      providesTags: (result, error, id) => [{ type: 'NoticeForShortNotice', id }],
    }),
    
    saveBasicDetails: builder.mutation({
      query: ({ id, data }) => ({
        url: `api/notice-for-short-notices/${id}/basic-details`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'NoticeForShortNotice', id },
        'NoticeForShortNotice'
      ],
    }),
    
    saveNoticeDetails: builder.mutation({
      query: ({ id, data }) => ({
        url: `api/notice-for-short-notices/${id}/notice-details`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'NoticeForShortNotice', id },
        'NoticeForShortNotice'
      ],
    }),
    
    saveExplanatoryNote: builder.mutation({
      query: ({ id, data }) => ({
        url: `api/notice-for-short-notices/${id}/explanatory-note`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'NoticeForShortNotice', id },
        'NoticeForShortNotice'
      ],
    }),
    
    saveSignature: builder.mutation({
      query: ({ id, data }) => ({
        url: `api/notice-for-short-notices/${id}/signature`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'NoticeForShortNotice', id },
        'NoticeForShortNotice'
      ],
    }),
    
    submitNotice: builder.mutation({
      query: (id) => ({
        url: `api/documents/${id}/submit`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'NoticeForShortNotice', id },
        'NoticeForShortNotice'
      ],
    }),
  }),
});

export const {
  useGetShortNoticeListQuery,
  useGetShortNoticeBankQuery,
  useGetShortNoticeQuery,
  useGetNoticeDetailsQuery,
  useSaveBasicDetailsMutation,
  useSaveNoticeDetailsMutation,
  useSaveExplanatoryNoteMutation,
  useSaveSignatureMutation,
  useSubmitNoticeMutation
} = noticeForShortNoticeApi;